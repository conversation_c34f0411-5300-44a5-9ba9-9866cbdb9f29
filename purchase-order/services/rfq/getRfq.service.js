const { db } = require('../../../config');
const { rfqComparator } = require('./rfqComparator.service');
const { updateRfqStatus } = require('./updateRfqStatus.service');

exports.getRfq = async (params,page = 1,limit = 30,client = db) => {
        let queries = [];
        //filters
        let rfq_id = params.rfq_id;
        let tenant_id = params.tenant_id;
        let org_id = params.org_id;
        let status = params.status;
        let on_date = params.on_date; // Optional
        let from_date = params.from_date; // Optional
        let to_date = params.to_date; // Optionals 
        let search_keyword = params.search_keyword; // Optionals 
        let tenantIdArr = global.comma_separated_to_array(params?.tenant_id);

        if(rfq_id)
            queries.push(`rfq.rfq_id = ${rfq_id}`)
        
        if(tenant_id)
            queries.push(`rfq.tenant_id in (${tenantIdArr.join(",")}) `)
        
        if(org_id)
            queries.push(`rfq.org_id = ${org_id}`)
        
        if(status)
            queries.push(`rfq.status in ('${status.replace(/,/g, "','")}')`)
        
        if (on_date) 
            queries.push(`rfq.created_at::date='${on_date}'`);
        
        if (from_date)
            queries.push(`rfq.created_at::timestamp>=TO_TIMESTAMP(${from_date}/ 1000)`);
        
        if (to_date)
            queries.push(`rfq.created_at::timestamp<=TO_TIMESTAMP(${to_date}/ 1000)`);

        if (search_keyword)
            queries.push(`
                rfq.rfq_id || ' ' || rfq.rfq_number || ' ' || 
                (
                    
                    SELECT STRING_AGG(rfql.product_sku_name, ' ')
                    FROM rfq_lines rfql WHERE rfql.rfq_id = rfq.rfq_id
                ) 
                ilike '%${search_keyword}%'
            `);
        
        let query = `
            SELECT
            (
                SELECT COUNT(*) as count
                FROM rfq rfq
                ${queries.length ? ` WHERE ${queries.join(" AND ")}` : ""}
            ),
            array_to_json(array_agg(rfq_list.*)) AS rfq_list
            FROM
            (
                SELECT rfq.*,
                (
                    SELECT json_agg(rfq_lines) as rfq_lines
                    from (
                        SELECT rfql.*,
                        (
                            SELECT json_agg(rfqp.rfq_participant_id) as rfq_participant_ids
                            FROM rfq_participant_line_relation rplr
                            LEFT JOIN rfq_participants rfqp ON rplr.rfq_participant_id = rfqp.rfq_participant_id
                            WHERE rplr.rfq_line_id = rfql.rfq_line_id
                        ),
                        (
                            SELECT row_to_json(a.*)
                            FROM addresses a 
                            WHERE a.address_id =  t.default_shipping_address
                        ) AS shipping_address_info,
                        (
                            select row_to_json(product_sku_info) as product_sku_info
                            from (
                                select psk.*,
                                (
                                    SELECT row_to_json(product_category_info) as product_category_info 
                                    from (
                                        SELECT *
                                        FROM product_category pc2
                                        where pc2.product_category_id = psk.product_category_id
                                    )product_category_info
                                )
                                from product_sku psk 
                                where psk.product_sku_id = rfql.product_sku_id
                            ) product_sku_info
                        ),
                        (
                            select COALESCE(sum(pol.quantity),0) AS purchase_quantity
                            from purchase_order_line pol 
                            INNER JOIN purchase_order po ON po.po_id = pol.po_id
                            where pol.rfq_line_id = rfql.rfq_line_id
                            and po.status IN ('ISSUED','SENT_FOR_APPROVAL')
                        ),
                        (
                            SELECT row_to_json(tenant_info.*) AS delivery_tenant_info
                            FROM
                            (
                                SELECT tnt.*,
                                (
                                    SELECT row_to_json(a.*)
                                    FROM addresses a 
                                    WHERE a.address_id =  tnt.default_billing_address
                                ) AS billing_address_info
                                FROM tenant tnt
                                WHERE tnt.tenant_id=rfql.delivery_tenant_id
                            )tenant_info
                        )
                        FROM rfq_lines rfql
                        INNER JOIN tenant t ON t.tenant_id = rfql.delivery_tenant_id
                        WHERE rfql.rfq_id = rfq.rfq_id
                        order by rfql.rfq_line_id
                    ) rfq_lines
                ),
                (
                    select row_to_json(ci) as created_by_info
                    from
                    (
                        select au.first_name, au.last_name
                        from app_user au
                        where au.user_id = rfq.created_by
                    ) ci
                ),
                (
                    select row_to_json(ci) as modified_by_info
                    from
                    (
                        select au.first_name, au.last_name
                        from app_user au
                        where au.user_id = rfq.modified_by
                    ) ci
                ),
                (
                    SELECT json_agg(tmp) AS reference_pr
                    FROM (
                        SELECT pr.purchase_requisition_id, pr.pr_number, pr.status
                        FROM  purchase_requisition pr
                        WHERE rfq.purchase_requisition_id = pr.purchase_requisition_id
                    ) tmp
                ),
                (
                    SELECT json_agg(tmp) AS reference_pi
                    FROM (
                        SELECT pi.pi_id, pi.pi_number, pi.status
                        FROM  purchase_indent pi
                        WHERE rfq.pi_id = pi.pi_id
                    ) tmp
                ),
                (
                    SELECT json_agg(tmp) AS reference_po
                    FROM (
                        SELECT po.po_id, po.po_number, po.status
                        FROM  purchase_order po
                        WHERE rfq.rfq_id = po.rfq_id
                    ) tmp
                ),
                (
                    SELECT json_agg(rfq_participants) as rfq_participants
                    from (
                        SELECT rfqp.*,
                        (
                            SELECT json_agg(rfql.product_sku_id) as product_sku_ids
                            FROM rfq_participant_line_relation rplr
                            LEFT JOIN rfq_lines rfql ON rfql.rfq_line_id = rplr.rfq_line_id
                            WHERE rplr.rfq_participant_id = rfqp.rfq_participant_id
                        ),
                        (
                            SELECT row_to_json(slr.*) AS seller_info
                            FROM  seller slr
                            WHERE slr.seller_id = rfqp.seller_id
                        ),
                        (
                            select json_agg(negotiations) as negotiations
                            from (
                                SELECT n.*,rfqp.participant_name, (
                                    select json_agg(negotiation_lines) as negotiation_lines
                                    from (
                                        select nl.*,rfql.tax_info
                                        from negotiation_lines nl
                                        INNER JOIN rfq_lines rfql ON rfql.rfq_line_id = nl.rfq_line_id
                                        where nl.negotiation_id = n.negotiation_id
                                    ) negotiation_lines
                                )
                                FROM negotiation n
                                where n.rfq_participant_id = rfqp.rfq_participant_id
                                ORDER BY n.created_at DESC
                            ) negotiations
                        ) 
                        FROM rfq_participants rfqp
                        WHERE rfqp.rfq_id = rfq.rfq_id
                        order by rfqp.rfq_participant_id
                    ) rfq_participants
                ),
                (
                    SELECT row_to_json(tenant_info.*) AS tenant_info
                    FROM
                    (
                        SELECT tnt.*,
                        (
                            SELECT row_to_json(a.*)
                            FROM addresses a 
                            WHERE a.address_id =  tnt.default_billing_address
                        ) AS billing_address_info
                        FROM tenant tnt
                        WHERE tnt.tenant_id=rfq.tenant_id
                    )tenant_info
                ),
                (
                    SELECT row_to_json(orga) AS organisation_info
                    FROM
                    (
                        SELECT org.logo as org_logo,org.currency_code
                        FROM organisation org
                        WHERE rfq.org_id = org.org_id
                    ) orga
                )
                FROM rfq rfq
                ${queries.length ? ` WHERE ${queries.join(" AND ")}` : ""}
                ORDER BY rfq.created_at DESC
                LIMIT ${limit} 
                OFFSET ${(page - 1) * limit}
            ) rfq_list;
        `
        if(!params.user_id) params.user_id=params.created_by;

        let response = await client._query(query,[], {
            user_id: params.user_id,
            apply_data_masking: true
        });
        
        // Insert return variable here!
        let rfq_list = response.rows[0]
        rfq_list.rfq_list = await Promise.all((rfq_list?.rfq_list || [])?.map(async rfq => {
            rfq.status = (await updateRfqStatus({
                rfq_id : rfq.rfq_id
            }))?.status || rfq.status
            rfq = rfqComparator(rfq)
            return rfq
        }))
        return rfq_list;
}

