const {db}= require(process.env.PWD + "/config");
const { taskQueue, TASKS } = require("../../../taskQueue");
const { activityLog } = require("../../../logger/firestore_logger");

const PO = require("./getPurchaseOrder");
const {
  createOfferInDB,
  updateOfferinDB,
} = require("../../../sku-offer/services/offer");
const {
  initializePOApprovalFlow,
} = require("./po_approval_flow/initializePOApprovalFlow");
const { ZOHO_QUEUE } = require("../../../taskQueue");
const { linkPoWithMrp } = require("../../../production/services/mrp_run/linkPoWithMrp");
const { unLinkPoWithMrp } = require("../../../production/services/mrp_run/unLinkPoWithMrp");
const { refreshTenantSellerRating } = require("../../../application/services/vendor_rating/refreshTenantSellerRating");
const { updateProductionRouteLineStatus } = require("../../../production/services/production_route");
const { sendPoWaMessage } = require("./sendPoWA.service");
const { sendIssuedPOEmail } =require('./sendIssuedPOEmail.service');
const { refreshProjectedIncomingStockQty } = require("../../../product-sku/services/product-sku");
const { updatePRFulfillmentStatus } = require('../purchase_requisition/helpers/updatePRFulfillmentStatus');
const { updatePIPurchaseStatus } = require('../purchase_indent/helpers/updatePIPurchaseStatus')
const { populateDocumentToTypesense } = require('../../../config/typesense/scripts/populateDocumentsToTypesense');
const { createSalesOrderFromPurchaseOrder } = require("./helper/createSalesOrderFromPo");
const { getLinkedPurchaseRequests } = require("./helper/getLinkedPurchaseRequests");
const { getLinkedPurchaseIndents } = require("./helper/getLinkedPurchaseIndents");


async function updateAssociatedEntityStatus (po_id) {

  try {
    // GET LINKED PURCHASE REQUESTS (DIRECT OR THROUGH RFQ OR THROUGH PURCHASE INDENTS) AND UPDATE THEIR STATUS
    const linked_prs = await getLinkedPurchaseRequests ('purchase_order', po_id);
    if (linked_prs?.length) {
      await updatePRFulfillmentStatus({
        purchase_requisition_id: linked_prs
      });
    }

    // GET LINKED PURCHASE INDENTS (DIRECT OR THROUGH RFQ) AND UPDATE THEIR STATUS
    const linked_pis = await getLinkedPurchaseIndents ('purchase_order', po_id);
    if (linked_pis?.length) {
      await updatePIPurchaseStatus({
        pi_ids: linked_pis
      });
    }
  } catch (error) {
    throw error;
  }
};

exports.change_po_status = async (_input, client) => {
  // Step 1: Get Purchase order
  let _po = await PO.getPurchaseOrder({ po_id: _input.po_id },1,1,client,false);
  if (_po.count != 1) throw new Error("Purchase order not exists");
  let purchase_order = _po.purchase_order[0];

  let _tenant_user = await client.query(
    `
      select row_to_json(mbi) as modified_by_info
      from
      (
          select mu.user_id, mu.username, mu.first_name,
              mu.last_name, mu.is_active, mu.email, mu.mobile
          from app_user mu
          where mu.user_id = ${_input.action_by}
      ) mbi
    `);
  let tenant_user = _tenant_user.rows[0];

  if (purchase_order.status === _input.status)
    throw new Error().UI(`PO already in ${_input.status}`);

  let logObject = {
    entity_id : _input.po_id,
    modified_by_info : tenant_user.modified_by_info,
    log_category : "STATUS_LOG",
  };

  if (_input.status === "ISSUED") {

    // Change status of job works to in progress
    let prLineIds = [];
    
    purchase_order.purchase_order_lines.map((pol => {
      if (pol.production_route_line_id)
          prLineIds.push(pol.production_route_line_id);
    }));
    

    if (prLineIds.length)
      await updateProductionRouteLineStatus({
          production_route_line_ids: prLineIds,
          status: 'IN_PROGRESS',
          action_by: purchase_order.created_by
      }, client)

    // Getting the email ID of user who created the Material Request
    // if(purchase_order.is_automatic_notification_enabled)
    //   await taskQueue({
    //     taskname : TASKS.SEND_PO_ON_MAIL,
    //     payload : {
    //       po_id : purchase_order.po_id,
    //       to_emails : purchase_order.notification_recipients || []
    //     }
    //   });

    let purchase_order_lines = purchase_order.purchase_order_lines;

    if(purchase_order.is_automatic_notification_enabled)
      client.onCommit.push([sendIssuedPOEmail,{po_id : purchase_order.po_id,to_emails :purchase_order.notification_recipients || [], action_by: _input?.action_by }])
    if(purchase_order.is_po_automatic_notification_enabled)
      client.onCommit.push([sendPoWaMessage,{po_id : purchase_order.po_id}])
    client.onCommit.push([refreshProjectedIncomingStockQty,{
        tenant_product_id: [purchase_order_lines.map(i=>i.tenant_product_id)]
    }]);

    let poLinesWithNoOfferID = purchase_order_lines.filter((line) => !line.sku_offer_id);
    let poLinesWithOfferID = purchase_order_lines.filter((line) => line.sku_offer_id);
    
    // Create SKU offer if not exists
    await Promise.all(
      poLinesWithNoOfferID.map(async (line) => {
        let offerObj = [
          {
            tenant_id: purchase_order.tenant_id,
            product_sku_id: line.product_sku_info.product_sku_id,
            seller_id: purchase_order.seller_id,
            moq: 1,
            offer_price: line.offer_price,
            mrp: line.offer_price,
            is_active: true,
            created_by: _input.action_by,
            modified_by: _input.action_by,
            packaging_details: ''
          },
        ];
        let offer = await createOfferInDB(offerObj, _input.action_by, client);
        offer = offer[0];
        let updateOfferIDinLineQuery = `
                UPDATE purchase_order_line
                SET sku_offer_id = ${offer.sku_offer_id}
                WHERE po_line_id = ${line.po_line_id}
                RETURNING *
            `;
        await client.query(updateOfferIDinLineQuery);
        return line;
      })
    );

    // Update SKU offer if exiting
    await Promise.all(
      poLinesWithOfferID.map(async (line) => {
        let offerObj = [
          {
            sku_offer_id: line.sku_offer_id,
            product_sku_id: line.product_sku_info.product_sku_id,
            seller_id: purchase_order.seller_id,
            moq: 1,
            offer_price: line.offer_price,
            mrp: line.offer_price,
            is_active: true,
            modified_by: _input.action_by,
          },
        ];
        await updateOfferinDB(offerObj, _input.action_by, client);
        return line;
      })
    );

     // Update Status
    let pr_response = await client.query(
      `
            UPDATE purchase_order
            SET status = $1
            WHERE po_id = $2
            RETURNING *
        `,
      [_input.status, _input.po_id]
    );
    let _pr_id = pr_response.rows[0].ref_pr_ids;
    let po =pr_response.rows[0];
    //based on inter tenant purchase order and create_automatic_sales_order key we will create a sales order for the destination tenant
    if(purchase_order.is_inter_tenant_purchase_order && purchase_order.create_automatic_sales_order){
      await createSalesOrderFromPurchaseOrder({po_id: purchase_order.po_id, action_by: -2},client);
    }

    // if(_pr_id){
    //     await taskQueue({
    //         taskname: TASKS.SEND_PO_APPROVAL_MAIL_FOR_PR,
    //         payload: {
    //         purchase_requisition_id: _pr_id
    //         },
    //     });   
    //     //. Automatic Link PO with MRP in case of reservation  
    //     //. is enabled in MRP.
    //     let pr_info = await client.query(`
    //         SELECT pr.mrp_run_id,mrp.sku_shortages, mrp.is_reservation_enabled
    //         from purchase_requisition pr
    //         inner join mrp_run mrp ON mrp.mrp_run_id = pr.mrp_run_id
    //         where pr.purchase_requisition_id = ${_pr_id}
    //     `)
    //     pr_info = pr_info?.rows?.[0];
    //     if(pr_info?.mrp_run_id && pr_info?.is_reservation_enabled===true){
    //         let skus = pr_info.sku_shortages
    //         let po_links_to_create = []
    //         purchase_order.purchase_order_lines.map(po_line => {
    //             if(skus.find(sku => sku.product_sku_id === po_line.product_sku_id && sku.tenant_id === purchase_order.tenant_id)){
    //             po_links_to_create.push({
    //                 po_line_id: po_line.po_line_id,
    //                 po_id: po_line.po_id,
    //                 po_number: purchase_order.po_number,
    //                 quantity: po_line.quantity,
    //                 org_id : purchase_order.tenant_info.org_id,
    //                 remaining_quantity : 0, // po->remaining quantity - current quantity
    //                 mrp_sku_id : po_line.product_sku_id,
    //                 mrp_run_id : pr_info.mrp_run_id,
    //                 tenant_id : purchase_order.tenant_info.tenant_id
    //             })
    //             }
    //         })
    //         await linkPoWithMrp(po_links_to_create,client)
    //     }
    // }
    

  } 
  
  else if (_input.status === "DRAFT") {
    if (!["REJECTED", "CANCELLED"].includes(purchase_order.status))
      throw new Error(
        `Failed to set as draft, PO is under ${purchase_order.status}`
      );
    await client.query(
      `
            UPDATE purchase_order
            SET status = $1,
            workflow_step_id = null
            WHERE po_id = $2
            RETURNING *
        `,
      [_input.status, purchase_order.po_id]
    );
  } 
  
  else if (_input.status === "CANCELLED") {
    if (
      !["DRAFT", "REJECTED", "SENT_FOR_APPROVAL","ISSUED"].includes(
        purchase_order.status
      )
    )
      throw new Error(`Failed to CANCEL PO, is ${purchase_order.status}`);

    let getGRNStatus = `
        select grn.grn_id, status
        from good_receiving_note grn
        inner join po_grn_relation pogr on pogr.grn_id = grn.grn_id
        where grn.grn_entity_type = 'PURCHASE_ORDER'
        AND (grn.status IN ('ISSUED', 'DRAFT')) 
        and pogr.po_id = ${_input.po_id}
     `;
    //  if rowCount is available, i.e rowCount more than equal to 1, where grn_status is ISSUED or DRAFT then throw error
    let getStatus = await client.query(getGRNStatus);
    if (getStatus.rowCount) {
      throw new Error("Purchase order has Issued GRN, cancel GRN and try again");
    }

    let updatePOStatusQuery = `
            UPDATE purchase_order
            SET status = 'CANCELLED',
            workflow_step_id = null,
            cancellation_reason = '${_input.cancellation_reason}'
            WHERE po_id = ${purchase_order.po_id}
            RETURNING *
        `;
    await client.query(updatePOStatusQuery);
    let _mrpplr = await client.query(`
        SELECT mrpplr.po_line_id, mrpplr.po_id, mrpplr.mrp_run_id
        FROM mrp_po_line_relation mrpplr
        JOIN purchase_order_line pol ON pol.po_line_id = mrpplr.po_line_id
        WHERE pol.po_id = $1
    `,[purchase_order.po_id]);
    if(_mrpplr?.rows?.length){
        await unLinkPoWithMrp(_mrpplr?.rows);
    }
  } 
  
  else if (_input.status === "SENT_FOR_APPROVAL") {
    if (!["DRAFT", "REJECTED"].includes(purchase_order.status))
      throw new Error(
        `Failed to send for approval, PO is ${purchase_order.status}`
      );

    // Create PO approval flow
    let workflow = await initializePOApprovalFlow(
      {
        po_id: _input.po_id,
        created_by: _input.action_by,
      },
      client
    );

    // if there is not workflow for this po then issue PO
    if (!workflow?.step_1_role_id) {
      _input.status = "ISSUED";
      _input.ignore = _input.previous_status === _input.status;
      await this.change_po_status(_input, client);
      _input.ignore = true;
    }
    // else set workflow for approval for PO
    else {
      await client.query(
        `
                UPDATE purchase_order
                SET status = $1
                WHERE po_id = $2
                RETURNING *
            `,
        [_input.status, _input.po_id]
      );
    }
  } 
  
  else if (_input.status === "REJECTED") {
    if (!["SENT_FOR_APPROVAL"].includes(purchase_order.status))
      throw new Error(
        `Failed to send for approval, PO is ${purchase_order.status}`
      );
    await client.query(
      `
            UPDATE purchase_order
            SET status = $1
            WHERE po_id = $2
            RETURNING *
        `,
      [_input.status, _input.po_id]
    );
  } 
  
  else throw new Error("Invalid status Enum");

  // UPDATE FULFILLMENT AND PURCHASE STATUS OF ASSOCIATED PURCHASE REQUEST AND PURCHASE INDENT RESPECTIVELY
  client.onCommit.push ([updateAssociatedEntityStatus, purchase_order?.po_id]);

  //Handle data Populate for Typesense
  client.onCommit.push([populateDocumentToTypesense,purchase_order.org_id,'PURCHASE_ORDERS',purchase_order.po_id]);
  

  // Sync  PO with Zoho
  let tenant_id = purchase_order.tenant_id;
  await ZOHO_QUEUE.SYNC_PURCHASE_ORDER({
      po_id: purchase_order.po_id,
      delay_in_sec: 2,
      tenant_id: tenant_id
  });

  logObject.prev_status = purchase_order.status;
  logObject.status = _input.status;
  logObject.action = "STATUS UPDATED";
  logObject.org_id = purchase_order.org_id

  if(_input.ignore)
    logObject.ignore = true;

  if(!_input.ignore && _input.previous_status)
    logObject.prev_status = _input.previous_status;


  // TODO: LOG THE UPDATE HERE
  let logRequest = [
    "purchase_order",_input.po_id,logObject
  ];
  client.onCommit.push([activityLog,...logRequest]);

  if(purchase_order.purchase_order_lines[0].mo_fg_id){
    let mo_fg_ids = purchase_order.purchase_order_lines.map(pol => {
        if (pol.mo_fg_id !== null){
        return pol.mo_fg_id
    }} 
    ).filter(id => id)
    let unique_mo_ids = `
    SELECT DISTINCT mofg.mo_id
    FROM manufacturing_order_finished_good mofg 
    where mofg.mo_fg_id in (${mo_fg_ids.join(',')})
    `
    let _ids = await client.query(unique_mo_ids)
    let ids = _ids.rows;
    ids.map(id => {
        id = id.mo_id
        let logRequest1 = [
            "manufacturing_order",id,{
                action : "ADJUSTMENT CREATED",
                log_category : "ADJUSTMENT_LOG",
                modified_by_info : tenant_user.modified_by_info,
                header : `Work Order #${purchase_order.po_number} Status Change`,
                description : `Status Changed to  ${_input.status.replaceAll("_"," ").toProperCase()} `,
                org_id : purchase_order.org_id
            }
        ];
        client.onCommit.push([activityLog,...logRequest1]);
    })
  }

  await refreshTenantSellerRating(purchase_order.tenant_seller_id,purchase_order.tenant_id, client)  
  return _input;
};

exports.updatePurchaseOrderStatus = async (_input, client = null) => {
  client = await db.begin(client);

  try {
    if (!["DRAFT", "SENT_FOR_APPROVAL", "CANCELLED"].includes(_input.status))
      throw new Error(
        `Only DRAFT, SENT_FOR_APPROVAL, CANCELLED status are allowed`
      );
    let _result = await this.change_po_status(_input, client);

    await client.commit();
    return _result;
  } catch (error) {
    console.log(error);
    await client.rollback();
    throw error;
  }
};
