const { db } = require('../../../../config');

exports.getLinkedPurchaseRequests = async (entityName, entityId, client = db) => {

    try {

        let result = [];

        if (entityName === 'purchase_order') {

            let linked_ids =  await client.query (`
                SELECT ARRAY_AGG(pr.purchase_requisition_id) AS purchase_requisition_ids
                FROM purchase_requisition pr
                LEFT JOIN purchase_indent pi ON pi.pr_id = pr.purchase_requisition_id
                LEFT JOIN pi_to_po pipo ON pipo.pi_id = pi.pi_id
                LEFT JOIN rfq
                    ON rfq.purchase_requisition_id = pr.purchase_requisition_id
                    OR rfq.pi_id = pi.pi_id
                LEFT JOIN purchase_order po
                    ON pr.purchase_requisition_id::text = ANY(regexp_split_to_array(po.ref_pr_ids, '\s*,\s*'))
                    OR po.rfq_id = rfq.rfq_id
                    OR po.po_id = pipo.po_id
                WHERE po.po_id = ${Number(entityId)}
            `);
            linked_ids = linked_ids?.rows?.[0]?.purchase_requisition_ids;
            result = linked_ids || [];
        }

        return result;
    } catch (error) {
        throw error;
    }
};

// this.getLinkedPurchaseRequests ('purchase_order', 105394);