const { db } = require('../../../../config');

exports.getLinkedPurchaseRequests = async (entityName, entityId, client = db) => {

    try {

        let result = [];

        if (entityName === 'purchase_order') {

            let linked_ids =  await client.query (`
                SELECT ARRAY_AGG(DISTINCT(prl.purchase_requisition_id)) AS purchase_requisition_ids
                    FROM purchase_requisition_line prl
                    LEFT JOIN purchase_indent_line pil ON pil.pr_line_id = prl.pr_line_id
                    LEFT JOIN rfq_lines rfql
                        ON rfql.pr_line_id = prl.pr_line_id
                        OR rfql.pi_line_id = pil.pi_line_id
                    LEFT JOIN purchase_order_line pol
                        ON pol.pr_line_id = prl.pr_line_id
                        OR pol.rfq_line_id = rfql.rfq_line_id
                        OR pol.pi_line_id = pil.pi_line_id
                    WHERE pol.po_id = ${Number(entityId)};
            `);
            linked_ids = linked_ids?.rows?.[0]?.purchase_requisition_ids;
            result = linked_ids || [];
        }

        return result;
    } catch (error) {
        throw error;
    }
};

// this.getLinkedPurchaseRequests ('purchase_order', 105394);