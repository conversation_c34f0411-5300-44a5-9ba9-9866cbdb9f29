const { db } = require('../../../../config');

exports.getLinkedPurchaseIndents = async (entityName, entityId, client = db) => {

    try {

        let result = [];

        if (entityName === 'purchase_order') {

            let linked_ids_query =  await client.query (`
                SELECT ARRAY_AGG(DISTINCT(pil.pi_id)) AS pi_ids
                FROM purchase_indent_line pil
                LEFT JOIN rfq_lines rfql ON rfql.pi_line_id = pil.pi_line_id
                LEFT JOIN purchase_order_line pol 
                    ON pol.pi_line_id = pil.pi_line_id
                    OR pol.rfq_line_id = rfql.rfq_line_id
                WHERE pol.po_id = ${Number(entityId)};
            `);
            linked_ids = linked_ids_query?.rows?.[0]?.pi_ids;
            result = linked_ids || [];
        }

        return result;
    } catch (error) {
        throw error;
    }
};

// this.getLinkedPurchaseIndents ('purchase_order', 105395);