const { db } = require('../../../../config');

exports.getLinkedPurchaseIndents = async (entityName, entityId, client = db) => {

    try {

        let result = [];

        if (entityName === 'purchase_order') {

            let linked_ids_query =  await client.query (`
                SELECT ARRAY_AGG(pi.pi_id) AS pi_ids
                FROM purchase_indent pi
                LEFT JOIN rfq ON rfq.pi_id = pi.pi_id
                LEFT JOIN pi_to_po pipo ON pipo.pi_id = pi.pi_id
                LEFT JOIN purchase_order po
                    ON po.po_id = pipo.po_id
                    OR po.rfq_id = rfq.rfq_id
                WHERE po.po_id = ${Number(entityId)}
            `);
            linked_ids = linked_ids_query?.rows?.[0]?.pi_ids;
            result = linked_ids || [];
        }

        return result;
    } catch (error) {
        throw error;
    }
};

// this.getLinkedPurchaseIndents ('purchase_order', 105395);