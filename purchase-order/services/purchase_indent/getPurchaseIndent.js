const { db, user_permissions: _up } = require("../../../config");

exports.getWorkflowQueryGenerator = (steps = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]) => {

    let _select_part = steps.map(_n => {
        return `
            CASE WHEN  ws.step_1_role_id IS NOT NULL THEN (
                json_build_object(
                        'workflow_step_id', ws.workflow_step_id,
                        'status', ws.step_1_status,
                        'taken_at', ws.step_1_taken_at,
                        'taken_by', ws.step_1_taken_by,
                        'taken_by_name', aus1.first_name,
                        'taken_by_email', aus1.email,
                        'role_id', ws.step_1_role_id,
                        'role_name', trs1.tenant_role_name,
                        'role_department_name', trs1.tenant_department_name,
                        'rejection_reason', CASE WHEN ws.rejection_step = 1
                                            THEN
                                                ws.rejection_reason 
                                            ELSE
                                                null
                                            END,
                        'rejection_step', CASE WHEN ws.rejection_step = 1
                                            THEN
                                                ws.rejection_step 
                                            ELSE
                                                null
                                            END,
                        'rejection_at', CASE WHEN ws.rejection_step = 1
                                        THEN
                                            ws.rejection_at 
                                        ELSE
                                            null
                                        END,
                        'rejection_by', CASE WHEN ws.rejection_step = 1
                                        THEN
                                            ws.rejection_by 
                                        ELSE
                                            null
                                        END
                    )
                )
            ELSE (null) END step_1
        `.replace(/1/g, _n);
    });

    let _join_part = steps.map(_n => {
        return `
            LEFT JOIN tenant_role trs1 ON trs1.tenant_role_id = ws.step_1_role_id
            LEFT JOIN app_user aus1 ON aus1.user_id = ws.step_1_taken_by

        `.replace(/1/g, _n);
    });

    return `
        ${_select_part.join(",")}
        FROM workflow_steps ws
        ${_join_part.join(" ")}
    `;
};


exports.getPurchaseIndent = async (
    _input,
    page = 1,
    limit = 30,
    client = db
) => {

    if (!/^[0-9]+$/.test(_input.pi_id)) {
        throw new Error("Invalid ID. Only numeric values are allowed.");
      }

    const tenant_role_id = _input.tenant_role_id; // Optional
    let tenant_department_id = (_input?.tenant_department_id || "").split(',').filter(i => i);
    let tenantIdArr = (_input?.tenant_id || "").split(',').filter(i => i);
    let department_ids = (_input?.department_ids || "").split(',').filter(i => i);
    if(_input.pi_id){
        _input.pi_id = comma_separated_to_array(_input.pi_id);
    }
    // Access / Security Check
    // if(_input.action_by && _input.tenant_id) 
    //     await tenantAuthv1(_input.action_by, _input.tenant_id, _up.purchase_indent.READ);

    let get_lines=false;
    if(_input.pi_id || _input.get_lines==true){
        get_lines=true;
    }
    let queries = [];
    if (_input.status) queries.push(`pi.status in ('${_input.status.replace(/,/g, "','")}')`);
    if (_input?.pi_id?.length == 1) queries.push(`pi.pi_id=${_input?.pi_id?.[0]}`);
    if (_input?.pi_id?.length > 1) queries.push(`pi.pi_id IN (${_input?.pi_id.join(",")})`);
    if (_input.org_id) queries.push(`tnt.org_id=${_input.org_id}`);
    if (_input.tenant_id) queries.push(`pi.tenant_id in (${tenantIdArr.join(",")})`);
    if (_input.on_date) queries.push(`pi.pi_date::date='${_input.on_date}'`);
    if (_input.mo_id) queries.push(`pi.mo_id=${_input.mo_id}`);
    if (_input.from_date)
        queries.push(`pi.pi_date::date>='${_input.from_date}'`);
    if (_input.to_date)
        queries.push(`pi.pi_date::date<='${_input.to_date}'`);
    if (tenant_department_id.length)
        queries.push(`pi.tenant_department_id IN (${tenant_department_id.join(",")})`);
    if (department_ids.length)
        queries.push(`d.department_id IN (${department_ids.join(",")})`);
      if (_input.product_sku_ids) {
        let product_sku_ids = _input.product_sku_ids.split(",")
        let product_id_search = []
        product_sku_ids.map(id => product_id_search.push(`pi.product_sku_ids @> '[${id}]'`))
        queries.push(`(
            ${product_id_search.join(" OR ")}
        )`)
    }
    if (_input?.tags) {
        let separate_tags = _input?.tags.split(",")
        let tags_cases = []
        separate_tags.map(tag => tags_cases.push(`pi.tags @> ARRAY['${tag}']`))
        queries.push(`(
                ${tags_cases.join(" OR ")}
            )`)
    }
    if(_input.mrp_id)
        queries.push(`pi.mrp_id = ${_input.mrp_id}`);
        
    
    // SQL like documentation
    if (_input.search_keyword)
        queries.push(`
            pi.pi_id || ' ' || (
                        SELECT String_agg(usr.first_name || ' ' || usr.last_name, ' ')
                        FROM app_user usr
                        where usr.user_id = pi.created_by
                    ) || ' ' || (
            SELECT concat(string_agg(po.po_number, ' '), ' ', pi.pi_number, ' ')
            FROM pi_to_po pipo
            INNER JOIN purchase_order po ON po.po_id = pipo.po_id
            WHERE pipo.pi_id = pi.pi_id
            ) ilike '%${_input.search_keyword}%'
        `);

    // If tenant role id is in req, it will filter out all the PR which requires 
    // approval w.r.t tenant role id
    if (tenant_role_id)
        queries.push(`
            ( 
                ( ws.step_1_role_id = ${tenant_role_id} AND 
                  ws.step_1_status = 'PENDING'
                ) OR
                ( ws.step_2_role_id = ${tenant_role_id} AND 
                  ws.step_2_status = 'PENDING' AND
                  ws.step_1_status = 'APPROVED'
                ) OR
                ( ws.step_3_role_id = ${tenant_role_id} AND 
                  ws.step_3_status = 'PENDING' AND
                  ws.step_2_status = 'APPROVED'
                ) OR
                ( ws.step_4_role_id = ${tenant_role_id} AND 
                  ws.step_4_status = 'PENDING' AND
                  ws.step_3_status = 'APPROVED'
                ) OR
                ( ws.step_5_role_id = ${tenant_role_id} AND 
                  ws.step_5_status = 'PENDING' AND
                  ws.step_4_status = 'APPROVED'
                ) OR
                ( ws.step_6_role_id = ${tenant_role_id} AND 
                  ws.step_6_status = 'PENDING' AND
                  ws.step_5_status = 'APPROVED'
                ) OR
                ( 
                    ws.step_7_role_id = ${tenant_role_id} AND 
                    ws.step_7_status = 'PENDING' AND
                    ws.step_6_status = 'APPROVED'
                ) OR
                ( 
                    ws.step_8_role_id = ${tenant_role_id} AND 
                    ws.step_8_status = 'PENDING' AND
                    ws.step_7_status = 'APPROVED'
                ) OR
                ( 
                    ws.step_9_role_id = ${tenant_role_id} AND 
                    ws.step_9_status = 'PENDING' AND
                    ws.step_8_status = 'APPROVED'
                ) OR
                ( 
                    ws.step_10_role_id = ${tenant_role_id} AND 
                    ws.step_10_status = 'PENDING' AND
                    ws.step_9_status = 'APPROVED'
                )
            )
        `);

        let count_where_cases = queries.map((i) => i);

        // This keyword is used to search like full text. it is bit complex according
        if (!_input?.pi_id?.length) {
            let __pointer = await client.query(`
              SELECT pi.pi_id
              FROM purchase_indent pi
              JOIN tenant tnt ON tnt.tenant_id = pi.tenant_id
              LEFT JOIN tenant_department td ON td.tenant_department_id = pi.tenant_department_id 
              LEFT JOIN workflow_steps ws on pi.workflow_step_id = ws.workflow_step_id
              LEFT JOIN department d on d.department_id = td.department_id
              ${count_where_cases.length ? ` WHERE ${count_where_cases.join(" AND ")}` : ""}
              ORDER BY pi.pi_id desc
              limit ${limit} 
              offset ${(page - 1) * limit}
             `);
             let _pointer = __pointer?.rows?.[0];
             let pointer = _pointer?.pi_id;
             if (pointer) queries.push(`pi.pi_id <= ${pointer}`);
             else queries.push(`pi.pi_id <= 0`);
        }
        
    // This is main query
    let _query = `
        SELECT
        (
            SELECT COUNT(*) as count
            FROM purchase_indent pi
            JOIN tenant tnt ON tnt.tenant_id = pi.tenant_id
            LEFT JOIN tenant_department td ON td.tenant_department_id = pi.tenant_department_id 
            LEFT JOIN workflow_steps ws on pi.workflow_step_id = ws.workflow_step_id
            LEFT JOIN department d on d.department_id =td.department_id
            ${count_where_cases.length ? ` WHERE ${count_where_cases.join(" AND ")}` : ""}
        ),
        array_to_json(array_agg(pi_list.*)) AS purchase_indent
        FROM
        (
            select tnt.org_id, pi.*,pi.pi_date::date,pi.created_at::date,
            td.department_id, td.alias_name AS tenant_department_alias_name, pi.custom_fields,
            json_build_object('department_id', td.department_id, 'department_name', d.department_name) AS department_info,
            --// * Tenant Info
            (
                SELECT row_to_json(tenant_info.*) AS tenant_info
                FROM
                (
                    SELECT tnt.*,
                    (
                        SELECT row_to_json(a.*) AS default_billing_address_info
                        FROM addresses a
                        WHERE a.address_id = tnt.default_billing_address
                    )
                    FROM tenant tnt
                    WHERE tnt.tenant_id=pi.tenant_id
                )tenant_info
            ),
            --// * Organisation Info
            (
                SELECT row_to_json(orga) AS organisation_info
                FROM
                (
                    SELECT org.logo as org_logo,org.currency_code
                    FROM organisation org
                    WHERE tnt.org_id = org.org_id
                ) orga
            ),
            --// * created by info
            (
                SELECT row_to_json(tmp1) AS created_by_info
                FROM
                (
                    SELECT usr.first_name, usr.last_name,
                        usr.username, usr.mobile
                    FROM app_user usr
                    where usr.user_id = pi.created_by
                ) tmp1
            ),
            --// * reference_rfq
            (
                SELECT json_agg(tmp) AS reference_rfq
                FROM (
                    SELECT rfq.rfq_id, rfq.rfq_number, rfq.status
                    FROM  rfq rfq
                    WHERE rfq.pi_id = pi.pi_id
                ) tmp
            ),
            --// * reference_purchase_orders
            (
                SELECT json_agg(pi) AS reference_purchase_orders
                FROM
                (
                    SELECT po.po_id,po.po_number,
                    (
                        SELECT row_to_json(slr) AS seller_info
                        FROM  
                        (
                            SELECT s.seller_name,
                            (
                                select row_to_json(mad) as manufacturing_address_details
                                from
                                (
                                    select 
                                        ad.address_id, ad.address1, ad.address2,
                                        ad.address3, ad.city, ad.state, ad.country,
                                        ad.postal_code 
                                    from addresses ad
                                    where ad.address_id = s.manufacturing_address_id
                                ) mad
                            ),
                            (
                                select row_to_json(oad) as office_address_details
                                from
                                (
                                    select 
                                        ad.address_id, ad.address1, ad.address2,
                                        ad.address3, ad.city, ad.state, ad.country,
                                        ad.postal_code 
                                    from addresses ad
                                    where ad.address_id = s.office_address_id
                                ) oad
                            )
                            FROM seller s
                            JOIN tenant_seller ts ON ts.seller_id = s.seller_id
                            WHERE ts.tenant_seller_id = po.tenant_seller_id
                        ) slr
                    ),
                    (
                        SELECT COUNT(pol.po_line_id) as items_count
                        FROM purchase_order_line pol 
                        WHERE pol.po_id = po.po_id
                    ),
                    po.status,po.po_grand_total
                    FROM pi_to_po pipo
                    INNER JOIN purchase_order po ON po.po_id = pipo.po_id
                    WHERE pipo.pi_id = pi.pi_id
                ) pi
            ),
            ${
                _input.pi_id
                  ? `
               (
                   SELECT row_to_json(dcs) AS document_tenant_configuration
                   FROM
                   (
                       SELECT dc.*
                       FROM document_config dc
                       WHERE dc.tenant_id=tnt.tenant_id
                       AND dc.entity_name='PURCHASE_INDENT'
                   )dcs
               ),
               (
                    SELECT row_to_json(tenant_configuration.*) AS tenant_configuration
                    FROM
                    (
                        SELECT tnt_conf.*
                        FROM tenant_configuration tnt_conf
                        WHERE tnt_conf.tenant_id=pi.tenant_id
                    )tenant_configuration
                ),
               `
                  : ""
              }
            --// * Purchase Indent Lines
            ${get_lines ? `
            (
                SELECT array_to_json(array_agg(tmp)) as purchase_indent_lines 
                FROM
                (
                    SELECT pi_line.*, tp.tenant_product_id AS tenant_product_id,tp.department_quantities,
                    tp.secondary_uom_dept_qty,
                    um.uom_name, um.uom_id, um.group_id, um.ratio, um.uqc, tp.cost_price, pi_line.uom_info,
                    (
                        SELECT json_agg(pil) AS reference_po_lines
                        FROM
                        (
                            SELECT
                                pol.po_id,
                                pol.po_line_id,
                                pol.quantity
                            FROM purchase_order_line pol
                            LEFT JOIN purchase_order po ON po.po_id = pol.po_id
                            LEFT JOIN rfq_lines rfql ON rfql.rfq_line_id = pol.rfq_line_id
                            WHERE
                                pi_line.pi_line_id = COALESCE (pol.pi_line_id, rfql.pi_line_id)
                                AND po.status = 'ISSUED'
                        ) pil
                    ),
                    (
                        select
                              case
                                  when sum(tpb.available_qty) is null
                                  then 0
                                  else sum(tpb.available_qty)
                              end as available_qty
                        from tenant_product_batches tpb
                        where tpb.tenant_product_id = tp.tenant_product_id
                    ),
                    (
                        SELECT SUM(pil.quantity) as total_po_quantity
                        FROM purchase_indent_line pil
                        INNER JOIN purchase_indent pi ON pi.pi_id = pil.pi_id
                        WHERE pil.pi_line_id = pi_line.pi_line_id 
                            AND pi.status NOT IN ('CANCELLED')
                    ),
                    (
                        SELECT row_to_json(_ps) AS product_sku_info
                        FROM (
                            SELECT _ps.*,
                            (
                                SELECT row_to_json(tmp) as tax_info FROM
                                (
                                    SELECT t.* ,
                                    (
                                        SELECT json_agg(child_taxes) as child_taxes
                                        from (
                                            SELECT t2.*,
                                            tt2.*
                                            FROM tax_group tg 
                                            INNER JOIN tax t2 ON t2.tax_id = tg.child_tax_id
                                            INNER JOIN tax_type tt2 ON tt2.tax_type_id = t2.tax_type
                                            WHERE tg.group_tax_id = t.tax_id
                                        ) child_taxes
                                    ),
                                    tt.*
                                    FROM tax t
                                    INNER JOIN tax_type tt ON tt.tax_type_id = t.tax_type
                                    WHERE t.tax_id = _ps.tax_id
                                ) tmp 
                              ),
                              (
                                SELECT row_to_json(product_category_info) as product_category_info 
                                from (
                                    SELECT *
                                    FROM product_category pc2
                                    where pc2.product_category_id = _ps.product_category_id
                                )product_category_info
                            )
                            FROM product_sku _ps
                            WHERE _ps.product_sku_id = tp_tmp.product_sku_id
                        ) _ps
                    )
                    FROM purchase_indent_line pi_line
                    LEFT JOIN uom um on um.uom_id = pi_line.uom_id
                    LEFT JOIN tenant_product tp_tmp ON tp_tmp.tenant_product_id = pi_line.tenant_product_id
                    LEFT JOIN tenant_product tp 
                        ON tp.product_sku_id = tp_tmp.product_sku_id
                        AND tp.tenant_id = pi.tenant_id
                    WHERE pi_line.pi_id = pi.pi_id
                    ORDER BY pi_line.pi_line_id
                ) tmp
            ),
            ` : `
                (SELECT COUNT(pi_line.pi_line_id) lines_count
                FROM purchase_indent_line pi_line
                WHERE pi_line.pi_id = pi.pi_id),
            `}
            (
                SELECT row_to_json(workflow_steps) as workflow_steps
                FROM
                (
                    SELECT
                    ${this.getWorkflowQueryGenerator()}
                    where ws.workflow_step_id = pi.workflow_step_id
                ) workflow_steps
            )
            FROM purchase_indent pi
            JOIN tenant tnt ON tnt.tenant_id = pi.tenant_id
            LEFT JOIN tenant_department td ON td.tenant_department_id = pi.tenant_department_id
            LEFT JOIN department d ON d.department_id = td.department_id
            LEFT JOIN workflow_steps ws on pi.workflow_step_id = ws.workflow_step_id
            ${queries.length ? ` WHERE ${queries.join(" AND ")}` : ""}
            ORDER BY pi.created_at DESC
            LIMIT ${limit} 
        ) pi_list
    `;
    let { rows } = await client._query(_query,[],{
        user_id: _input?.action_by,
        apply_data_masking: true
    });
    let count = Number(rows?.[0]?.count || 0);
    let purchase_indents = rows?.[0]?.purchase_indent || [];

    purchase_indents?.map (pi => {
        pi.purchase_indent_lines?.map (pil => {

            // CALCULATING PO LINES TOTAL QUANTITY FOR A PI LINE
            let ordered_quantity = 0;

            pil.reference_po_lines?.map (pol => {
                ordered_quantity += pol.quantity;
            });

            const DELTA = 0.25;

            pil.ordered_quantity = ordered_quantity;
            pil.pending_quantity = pil.quantity - ordered_quantity;

            // CALCULATING PURCHASE STATUS OF A LINE ON THE BASIS OF ORDERED QUANTITY
            if (pil.ordered_quantity >= pil.quantity - DELTA) {
                pil.purchase_status = 'COMPLETED';
            } else if (pil.ordered_quantity > 0) {
                pil.purchase_status = 'PARTIAL';
            } else {
                pil.purchase_status = 'PENDING';
            }
        });
    });

    return {
        success: true,
        count: count,
        data: purchase_indents,
    };
};

// // (
//     SELECT itw.is_purchase_indent_approval_enabled
//     FROM integration_tenant_whatsapp itw 
//     WHERE itw.tenant_id = pi.tenant_id
// )