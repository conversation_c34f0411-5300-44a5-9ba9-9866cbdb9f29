const { db } = require('../../../../config');
const DELTA = 0.25;

exports.updatePIPurchaseStatus = async (_input, client = null) => {
  client = await db.begin(client);

  try {
    const { pi_ids } = _input;
    if (pi_ids.length === 0) {
      throw new Error("Purchase Indent IDs (pi_ids) array is required and cannot be empty");
    }

    // QUERY FOR PI LINE QUANTITIES AND RESPECTIVE PO LINE QUANTITIES (DIRECT OR THROUGH RFQ)
    const joined_lines = `
      SELECT
        pil.pi_id,
        pil.pi_line_id,
        COALESCE (
          convert_uom_quantity(
            pil.quantity,
            pi_sku.uom_list::jsonb,
            pil.uom_id,
            pi_sku.uom_id
          ),
          0
        ) AS pi_qty,
        (
          SELECT
            COALESCE(
              SUM(
                convert_uom_quantity(
                  pol.quantity,
                  po_sku.uom_list::jsonb,
                  pol.uom_id,
                  po_sku.uom_id
                )
              ),
              0
            ) AS po_qty
          FROM purchase_order_line pol
          LEFT JOIN purchase_order po ON po.po_id = pol.po_id
          LEFT JOIN tenant_product po_tp ON po_tp.tenant_product_id = pol.tenant_product_id
          LEFT JOIN product_sku po_sku ON po_sku.product_sku_id = po_tp.product_sku_id
          LEFT JOIN rfq_lines rfql ON rfql.rfq_line_id = pol.rfq_line_id
          WHERE 
            (pol.pi_line_id = pil.pi_line_id OR rfql.pi_line_id = pil.pi_line_id)
            AND po.status = 'ISSUED'
        )
      FROM purchase_indent_line pil
      LEFT JOIN tenant_product pi_tp ON pi_tp.tenant_product_id = pil.tenant_product_id
      LEFT JOIN product_sku pi_sku ON pi_sku.product_sku_id = pi_tp.product_sku_id
      WHERE pil.pi_id = ANY($1)
    `;

    let pi_po_qty_lines = await client.query(joined_lines, [pi_ids]);

    pi_po_qty_lines = pi_po_qty_lines.rows;
    
    const lineStatuses = {};

    // FINDING DISTINCT LINE STATUSES
    pi_po_qty_lines?.forEach((item) => {
      // Initialize Set if it doesn't exist for this pi_id
      if (!lineStatuses[item?.pi_id]) {
        lineStatuses[item?.pi_id] = new Set();
      }

      if (Number(item?.po_qty) >= Number(item?.pi_qty) - DELTA) {
        lineStatuses[item?.pi_id].add('COMPLETED');
      } else if (Number(item?.po_qty) > 0) {
        lineStatuses[item?.pi_id].add('PARTIAL');
      } else {
        lineStatuses[item?.pi_id].add('PENDING');
      }
    });

    const entityStatus = (statusSet) => {

      if (statusSet?.size > 1) {
        return 'PARTIAL';
      } else if (statusSet?.size == 1) {
        if (statusSet?.has('COMPLETED')) {
          return 'COMPLETED';
        } else if (statusSet?.has('PARTIAL')) {
          return 'PARTIAL';
        } else {
          return 'PENDING';
        }
      } else {
        return 'PENDING';
      }
    };

    await Promise.all (Object.keys(lineStatuses)?.map(async (id) => {

      await client.query (
        `
          UPDATE purchase_indent pi
          SET purchase_status = $2
          WHERE pi_id = $1
        `,
        [id, entityStatus(lineStatuses[id])]
      );
    }));

    await client.commit();

    return { message: 'PI Purchase Status Updated Successfully' };
  } catch (error) {
    await client.rollback();
    throw error;
  }
};

//this.updatePIPurchaseStatus({pi_ids : [10050]})