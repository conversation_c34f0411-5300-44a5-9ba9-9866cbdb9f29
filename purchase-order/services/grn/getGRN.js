const { CF_ENTITY } = require("../../../application/constants/customField.contant");
const { GetOrganisation } = require("../../../authentication/services/organisation/organisation");
const { db } = require("../../../config");
const { getCfValueByEntityIdAndName } = require("../../../application/services/customFieldValue/sql");

/**
 * To get GRN from the database based on the GRN ID
 * if the GRN ID is not provided, then use other parameters to get the GRN
 * @param {*} params
 * @param {*} page
 * @param {*} limit
 * @returns
 */

exports.getWorkflowQueryGenerator = (
    steps = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
) => {
    let _select_part = steps.map((_n) => {
        return `
              CASE WHEN  ws.step_1_role_id IS NOT NULL THEN (
                  json_build_object(
                          'workflow_step_id', ws.workflow_step_id,
                          'status', ws.step_1_status,
                          'taken_at', ws.step_1_taken_at,
                          'taken_by', ws.step_1_taken_by,
                          'taken_by_name', aus1.first_name,
                          'taken_by_email', aus1.email,
                          'role_id', ws.step_1_role_id,
                          'role_name', trs1.tenant_role_name,
                          'role_department_name', trs1.tenant_department_name
                      )
                  )
              ELSE (null) END step_1
          `.replace(/1/g, _n);
    });

    let _join_part = steps.map((_n) => {
        return `
              LEFT JOIN tenant_role trs1 ON trs1.tenant_role_id = ws.step_1_role_id
              LEFT JOIN app_user aus1 ON aus1.user_id = ws.step_1_taken_by
  
          `.replace(/1/g, _n);
    });

    return `
          ${_select_part.join(",")}
          FROM workflow_steps ws
          ${_join_part.join(" ")}
      `;
};
exports.getGRNFromDB = async (params, page = 1, limit = 10, client = db, currency_convertion = true) => {
    if(client == null) client = db;
    let whereCases = [];
    let tenant_role_id = params?.tenant_role_id;
    let tenantIdArr = String(params?.tenant_id || "")?.split(",")?.filter((i) => i);

    if (!('pointer_pagination' in params))
        params.pointer_pagination = true
        
    if (params.grn_id) 
        whereCases.push(`grn.grn_id = ${params.grn_id}`);

    if (params.seller_id) 
        whereCases.push(`grn.seller_id = ${params.seller_id}`);

    if (params.seller_ids)
        whereCases.push(`grn.seller_id in (${params.seller_ids})`)

    if (params.department_ids)
            whereCases.push(`td.department_id in (${params.department_ids})`)

    if (params.exclude_grn_ids) whereCases.push(`grn.grn_id NOT IN (${params.exclude_grn_ids})`);

    if (params.status)
        whereCases.push(`grn.status in ('${params.status.replace(/,/g, "','")}')`);

    if (params.tenant_id)
        whereCases.push(`grn.grn_tenant_id in (${tenantIdArr.join(",")})`);

    if (params.grn_entity_id && !params.grn_entity_type)
        throw new Error("Mention grn_entity_type also while sending grn_entity_id");

    if (params.grn_entity_type)
        whereCases.push(`grn.grn_entity_type in ('${params.grn_entity_type.replace(/,/g, "','")}')`);

    if (params.grn_entity_id){
        // check if grn_entity_id exists in table po_grn_relation table
        if(params.grn_entity_type == "PURCHASE_ORDER")
            whereCases.push(`EXISTS (select 1 from po_grn_relation pgrn where pgrn.grn_id = grn.grn_id and pgrn.po_id = ${params.grn_entity_id})`);
        else
            whereCases.push(`grn.grn_entity_id = '${params.grn_entity_id}'`);
    }

    if (params.search_keyword)
        whereCases.push(`
        concat(grn.grn_number, '', grn.grn_id, '', po.po_id, ' ',grn.grn_entity_type,' ', po.tenant_seller_id,' ', s.seller_name) ilike '%${params.search_keyword?.trim()}%'
        `);

    if (params.tenant_seller_id)
        whereCases.push(`grn.tenant_seller_info->>'tenant_seller_id' = '${params.tenant_seller_id}'`);

    if (params.payment_status)
        whereCases.push(`grn.payment_status IN ('${params.payment_status.replace(/,/g, "','")}')`)

    if (params.tags) {
        let separate_tags = params.tags.split(",")
        let tags_cases = []
        separate_tags.map(tag => tags_cases.push(`grn.tags @> ARRAY['${tag}']`))
        whereCases.push(`(
                ${tags_cases.join(" OR ")}
            )`)
    }

    if (params.from_grn_due_date && params.to_grn_due_date)
        whereCases.push(`(
            coalesce
            (
                grn.grn_due_date::date,
                case
                    when po.payment_terms is null or po.payment_terms::jsonb = '[]'::jsonb then grn.grn_date_time::date
                    else (grn.grn_date_time::date + (json_extract_path_text(po.payment_terms, '0', 'due_days')::integer || ' days')::interval)::date
                end
            ) between '${params.from_grn_due_date}'::date and '${params.to_grn_due_date}'::date
        )`);

    if (params.from_grn_date_time && params.to_grn_date_time)
        whereCases.push(`(
            grn.grn_date_time::date between '${params.from_grn_date_time}'::date and '${params.to_grn_date_time}'::date
        )`);

    // Tenant level permission rule
    // if(params.action_by && params.tenant_id){
    //     await tenantAuthv1( params.action_by, params.tenant_id, _up.good_receiving.READ );
    // }

    let mapGrnPaymentsQuery = "";

    if (params.map_grn_payments == "true") {
        mapGrnPaymentsQuery = `
            (
                select array_to_json(array_agg(grn_payment)) as grn_payments
                from
                (
                    select 
                        gp.grn_payment_id, gp.amount, gp.payment_id,gp.entity_id,gp.applied_payment_type,gp.entity_type,
                        payo.utr_number, payo.payment_date, payo.attachments, payo.payment_status, payo.zoho_bill_payment_id,
                        pa.account_id, pa.account_name, pa.tenant_id as payment_tenant_id, pa.zoho_account_id, pa.read_only,gp.zoho_payment_id,dn.debit_note_number
                    from grn_payments gp
                    left join payments_outgoing payo on payo.payment_id = gp.payment_id
                    LEFT JOIN payment_accounts pa ON pa.account_id = payo.paid_through_account_id
                    left join debit_note dn ON dn.dn_id = gp.payment_id
                    where 
                        gp.entity_id = grn.grn_id
                ) grn_payment
            ),
        `;
    }

    if (tenant_role_id)
        whereCases.push(`
          ( 
              ( ws.step_1_role_id = ${tenant_role_id} AND 
                ws.step_1_status = 'PENDING'
              ) OR
              ( ws.step_2_role_id = ${tenant_role_id} AND 
                ws.step_2_status = 'PENDING' AND
                ws.step_1_status = 'APPROVED'
              ) OR
              ( ws.step_3_role_id = ${tenant_role_id} AND 
                ws.step_3_status = 'PENDING' AND
                ws.step_2_status = 'APPROVED'
              ) OR
              ( ws.step_4_role_id = ${tenant_role_id} AND 
                ws.step_4_status = 'PENDING' AND
                ws.step_3_status = 'APPROVED'
              ) OR
              ( ws.step_5_role_id = ${tenant_role_id} AND 
                ws.step_5_status = 'PENDING' AND
                ws.step_4_status = 'APPROVED'
              ) OR
              ( ws.step_6_role_id = ${tenant_role_id} AND 
                ws.step_6_status = 'PENDING' AND
                ws.step_5_status = 'APPROVED'
              ) OR
              ( 
                  ws.step_7_role_id = ${tenant_role_id} AND 
                  ws.step_7_status = 'PENDING' AND
                  ws.step_6_status = 'APPROVED'
              ) OR
              ( 
                  ws.step_8_role_id = ${tenant_role_id} AND 
                  ws.step_8_status = 'PENDING' AND
                  ws.step_7_status = 'APPROVED'
              ) OR
              ( 
                  ws.step_9_role_id = ${tenant_role_id} AND 
                  ws.step_9_status = 'PENDING' AND
                  ws.step_8_status = 'APPROVED'
              ) OR
              ( 
                  ws.step_10_role_id = ${tenant_role_id} AND 
                  ws.step_10_status = 'PENDING' AND
                  ws.step_9_status = 'APPROVED'
              )
          )
      `);

    if(params.allow_grn_to_create_ap_invoice == "true" || params.allow_grn_to_create_ap_invoice == true)
        whereCases.push(`grn.allow_grn_to_create_ap_invoice = true`);
    if(params.allow_grn_to_create_ap_invoice == "false" || params.allow_grn_to_create_ap_invoice == false)
        whereCases.push(`grn.allow_grn_to_create_ap_invoice = false`);
    
    let count_where_cases = whereCases.map((i) => i);

    if (!params.grn_id && params.pointer_pagination) {
        let __pointer = await client.query(`
              SELECT 
              distinct(grn.grn_id)
              from good_receiving_note grn
                LEFT JOIN  purchase_order po on grn.grn_entity_id = po.po_id
                LEFT JOIN seller s on s.seller_id = grn.seller_id
                LEFT JOIN workflow_steps ws on grn.workflow_step_id = ws.workflow_step_id
                LEFT JOIN tenant_department td on td.tenant_department_id = grn.tenant_department_id
              ${count_where_cases.length ? "WHERE (" + count_where_cases.join(" AND ") + " )" : ""}
              ORDER BY grn.grn_id desc
              LIMIT ${limit} 
              OFFSET ${(page - 1) * limit}
              `);
        let _pointer = __pointer?.rows

        let ids = _pointer.map(obj => obj.grn_id)

        ids = ids.join(',')

        if (ids)
            whereCases.push(`grn.grn_id IN (${ids})`);
        else
            whereCases.push(`grn.grn_id<= 0`);
    }

    let getGrnQuery = `

        select
            (
                select count(grn.*)
                from good_receiving_note grn
                LEFT JOIN  purchase_order po on grn.grn_entity_id = po.po_id
                LEFT JOIN seller s on s.seller_id = grn.seller_id
                LEFT JOIN workflow_steps ws on grn.workflow_step_id = ws.workflow_step_id
                LEFT JOIN tenant_department td on td.tenant_department_id = grn.tenant_department_id
                ${count_where_cases.length ? "WHERE " + count_where_cases.join(" AND ") : ""}
            ),                    
            array_to_json(array_agg(grn_list)) as grn_list
            from  
            (
                select 
                    grn.*, 
                    po.po_number, 
                    po.po_date, 
                    po.po_id, 
                    grn.seller_id, 
                    islr.zoho_seller_id, 
                    tnt.zoho_branch_id,
                    tnt.tally_company_name, 
                    tnt.it_id,
                    tnt.org_id,
                    po.tenant_seller_id, 
                    s.email_id_1, 
                    s.seller_name,
                    s.seller_type,
                    s.pan,
                    s.internal_slr_code,
                    po.zoho_po_id,
                    s.tally_seller_name, 
                    s.internal_slr_code,
                    s.tally_updated_at as seller_tally_updated_at,
                    ibtm.busy_location_name, CASE WHEN ibtm.integration_busy_id IS NOT NULL THEN true ELSE false END as is_busy_connected,
                    (
                        SELECT json_agg(linked_pos) as linked_pos
                        from (
                            SELECT po.po_number, po.po_id
                            FROM po_grn_relation pgrn
                            inner join purchase_order po on po.po_id = pgrn.po_id
                            where pgrn.grn_id = grn.grn_id
                        ) linked_pos
                    ),
                    (
                        select row_to_json(tdpt) as tenant_department_info
                        from
                        (
                            select 
                                td.*, 
                                d.department_name
                            from tenant_department td
                            inner join department d on d.department_id = td.department_id
                            where
                                td.tenant_department_id = grn.tenant_department_id
                        ) tdpt
                      ),
                (
                    select ts.rating as seller_rating from tenant_seller ts
                    where ts.tenant_id = grn.grn_tenant_id and ts.seller_id = grn.seller_id
                ),
                coalesce
                (
                    grn.grn_due_date::date,
                    case
                        when po.payment_terms is null or po.payment_terms::jsonb = '[]'::jsonb then grn.grn_date_time::date
                        else (grn.grn_date_time + (json_extract_path_text(po.payment_terms, '0', 'due_days')::integer || ' days')::interval)::date
                    end
                ) as grn_due_date,
                (
                    SELECT row_to_json(orga) AS organisation_info
                    FROM
                    (
                        SELECT org.logo as org_logo,org.currency_code, org.org_id
                        FROM organisation org
                        inner join tenant torg on torg.tenant_id = grn.grn_tenant_id
                        WHERE torg.org_id = org.org_id
                    ) orga
                ),
                (
                    select json_agg(ref_gate_document) as ref_gate_document
                    from (
                        SELECT gdld.document_id, gdld.document_type, gd.gate_document_number, gd.gate_document_id
                        from gate_document_linked_document gdld
                        inner join gate_document gd on gd.gate_document_id = gdld.gate_document_id
                        WHERE gdld.document_id = grn.grn_id and gdld.document_type = 'GRN'
                    ) ref_gate_document
                ),
                -- // * Tenant Billing Address 
                (
                    SELECT row_to_json(a.*)
                    FROM addresses a 
                    WHERE a.address_id =  tnt.default_billing_address
                ) AS tenant_billing_address,
                -- // * Tenant Configuration
                (
                    SELECT row_to_json(tenant_configuration.*) AS tenant_configuration
                    FROM
                    (
                        SELECT tnt_conf.*
                        FROM tenant_configuration tnt_conf
                        WHERE tnt_conf.tenant_id = grn.grn_tenant_id
                    ) tenant_configuration
                ),
                (
                    SELECT row_to_json(tenant_info.*) AS tenant_info
                    FROM
                    (
                        SELECT tnt.*,
                        COALESCE(grn.tenant_info->>'tenant_name', tnt.tenant_name) AS tenant_name, 
                        COALESCE(grn.tenant_info->>'legal_name', tnt.legal_name) AS legal_name,
                        (
                            SELECT row_to_json(a.*) AS default_billing_address_info
                            FROM addresses a
                            WHERE a.address_id = tnt.default_billing_address
                        )
                        FROM tenant tnt
                        WHERE tnt.tenant_id=grn.grn_tenant_id
                    )tenant_info
                ),
                -- // * Applied Debit Notes List
                (
                    select array_to_json(array_agg(dn_list.*)) as debit_notes
                    from
                    (
                        select 
                            dn.dn_id, dn.status, 
                            dn.dn_grand_total,dn.document_dn_grand_total, dn.debit_note_number
                        from debit_note dn
                        where
                            dn.grn_id = grn.grn_id
                    ) dn_list
                ),
                (
                    select array_to_json(array_agg(ap_invoices.*)) as ap_invoices
                    from
                    (
                        select 
                            apigrn.ap_invoice_id, api.status, 
                            api.grand_total,api.document_grand_total, 
                            api.ap_invoice_number
                        from account_payable_invoice_has_grn apigrn
                        JOIN account_payable_invoice api on api.ap_invoice_id = apigrn.ap_invoice_id
                        where
                            apigrn.grn_id = grn.grn_id
                    ) ap_invoices
                ),
                -- // * Stock Transfers List (With sources & dest department)
                (
                    select array_to_json(array_agg(st)) as stock_transfers
                    from
                    (
                        select 
                            st.*,
                    (
                        select row_to_json(sdi) as source_department_info
                        from
                        (
                            select td.alias_name, td.tenant_id,td.is_active,t.tenant_name,
                            t.legal_name,td.department_id, d.department_name,
                            (
                                SELECT row_to_json(a.*)
                                FROM addresses a 
                                WHERE a.address_id =  t.default_billing_address
                            ) AS tenant_billing_address_info,
                            (
                                SELECT row_to_json(a.*)
                                FROM addresses a 
                                WHERE a.address_id =  t.default_shipping_address
                            ) AS tenant_shipping_address_info
                            from tenant_department td
                            inner join tenant t on t.tenant_id = td.tenant_id
                            inner join department d on d.department_id = td.department_id
                            where tenant_department_id = st.source_tenant_department_id
                        ) sdi
                    ),
                    (
                        select row_to_json(ddi) as destination_department_info
                        from
                        (
                            select td.alias_name, td.tenant_id,td.is_active,t.tenant_name,
                            t.legal_name,td.department_id, d.department_name,
                            (
                                SELECT row_to_json(a.*)
                                FROM addresses a 
                                WHERE a.address_id =  t.default_billing_address
                            ) AS tenant_billing_address_info,
                            (
                                SELECT row_to_json(a.*)
                                FROM addresses a 
                                WHERE a.address_id =  t.default_shipping_address
                            ) AS tenant_shipping_address_info
                            from tenant_department td
                            inner join tenant t on t.tenant_id = td.tenant_id
                            inner join department d on d.department_id = td.department_id
                            where tenant_department_id = st.destination_tenant_department_id
                        ) ddi
                    )
                        from stock_transfer st
                        where
                            st.stock_transfer_id = grn.grn_entity_id
                    ) st
                ),
                -- // * Debit note info list 
                (
                    select array_to_json(array_agg(dbi)) as debit_note_info
                    from
                    (
                        SELECT coalesce(SUM(grnp.amount),0) as db_total
                        FROM debit_note dn2
                        join grn_payments grnp ON dn2.dn_id = grnp.payment_id AND grnp.applied_payment_type = 'DEBIT_NOTE'
                        WHERE grnp.entity_id = grn.grn_id AND dn2.status = 'CONFIRMED'
                    )dbi
                ),                          
                (
                    select 
                        case 
                            when sum(grnp.amount) is null
                            then 0
                        else sum(grnp.amount)
                        end as total_payment_made
                    from grn_payments grnp
                    inner join payments_outgoing payout
                    on payout.payment_id = grnp.payment_id AND grnp.applied_payment_type ='PAYMENT'
                    where 
                        grnp.entity_id = grn.grn_id and
                        payout.payment_status = 'PAYMENT_SUCCESS'
                ),
                    case
                        when grn.grn_entity_type = 'INVENTORY_TRANSFER'
                        then 
                        (
                            select concat(t.tenant_name, ', ', ad1.city)
                            from stock_transfer st
                            inner join tenant_department td
                            on td.tenant_department_id = st.source_tenant_department_id
                            inner join tenant t
                            on t.tenant_id = td.tenant_id
                            inner join addresses ad1
                            on ad1.address_id = t.default_shipping_address
                            where st.stock_transfer_id = grn.grn_entity_id
                        )
                        else null
                    end as goods_received_from,
                    case
                        when grn.grn_entity_type = 'PURCHASE_ORDER' or grn.grn_entity_type = 'GOOD_RECEIVING_NOTE'
                        then 
                        (
                            select row_to_json(tsi) as seller_bank_details
                            from
                            (
                                select 
                                    sl.seller_name,sl.seller_id,
                                    sl.bank_name, sl.bank_account_no, sl.bank_ifsc, sl.gst_number, sl.email_id_1, mobile_1,
                                    (
                                        SELECT row_to_json(a.*) AS default_office_address_info
                                        FROM addresses a
                                        WHERE a.address_id = sl.office_address_id
                                    )
                                    from seller sl
                                    where sl.seller_id = grn.seller_id
                            ) tsi
                        )
                        else null
                    end as seller_bank_details,
                    ${params.map_grn_payments == "true"
            ? mapGrnPaymentsQuery
            : ""
        }
        ${params.grn_id
            ? `
            (
                SELECT row_to_json(dcs) AS document_tenant_configuration
                FROM
                (
                    SELECT dc.*
                    FROM document_config dc
                    WHERE dc.tenant_id=grn.grn_tenant_id
                    AND dc.entity_name='GOOD_RECEIVING_NOTES'
                )dcs
            ),
            (
                SELECT row_to_json(t.*) as integration_tally_config
                FROM
                (
                    SELECT it.*, tnt.tally_location_name,
                    (
                        SELECT array_to_json(array_agg(voucher_types)) as voucher_types
                        FROM (
                        SELECT * from integration_tally_voucher_types vt
                        WHERE vt.org_id = it.temp_org_id AND (it.it_id = vt.it_id) AND parent='Purchase'
                        ) voucher_types 
                    )
                    FROM integration_tally it
                    WHERE it.it_id = tnt.it_id
                ) t
            ),
            (
                SELECT row_to_json(tmp.*) as integration_busy_config
                FROM
                (
                    SELECT ib.*, ibtm.busy_location_name, ibtm.purchase_voucher_series
                    FROM integration_busy ib
                    JOIN integration_busy_tenant_mapping ibtm
                        ON ib.integration_busy_id = ibtm.integration_busy_id
                    WHERE ibtm.tenant_id = grn.grn_tenant_id
                ) tmp
            ),
            `
            : ""
        }
                (
                    select row_to_json(ci) as created_by_info
                    from
                    (
                        select 
                            au.first_name, au.last_name,
                            au.username, au.email
                        from app_user au
                        where au.user_id = grn.created_by
                    ) ci
                ),
                (
                    select row_to_json(rating_info.*) as rating_info
                    from
                    (
                        select r.* from entity_rating r
                        where r.entity_rating_id= grn.entity_rating_id 
                        and r.source_entity_name= 'GOOD_RECEIVING_NOTE'
                    )rating_info
                ),
                (
                    SELECT json_agg(subcontractor_mos) as subcontractor_mos
                    from (
                            Select 
                                mo.mo_id,mo.mo_number
                            from manufacturing_order mo
                            where mo.mo_id = grn.subcontractor_mo_id
                    ) subcontractor_mos
                ),
                (
                    select array_to_json(array_agg(grnls)) as grn_lines
                    from
                    (
                        select
                        grnl.grn_line_id, grnl.grn_id, grnl.quantity as received_qty, grnl.line_discount_percentage, grnl.line_discount_amount, grnl.value_before_tax,
                        grnl.status, grnl.tenant_product_id, grnl.grn_entity_line_id,grnl.remarks,
                        grnl.offer_price, ots.tax_value as pan_india_gst, ots.*, grnl.total_price,
                        grnl.expiry_date, grnl.tax_info, grnl.uom_info, grnl.uom_id, grnl.tax_id,
                        grnl.batch_id,  pol.zoho_po_line_id, grnl.zoho_bill_line_id,grnl.tax_group_info,
                        grnl.tally_purchase_account,grnl.document_line_discount_amount,grnl.document_offer_price,
                        grnl.document_total_price,grnl.document_value_before_tax, grnl.is_discount_in_percent,
                        grnl.custom_fields as grn_line_custom_fields, grnl.secondary_uom_qty,grnl.po_line_status as grn_line_po_line_status,
                        pol.po_id, po.po_number,grnl.grn_entity_line_id,
                        grnl.invoice_quantity,
                        (
                             SELECT
                             COALESCE(SUM(apil.quantity), 0) AS ap_invoiced_quantity
                             FROM account_payable_invoice_line apil
                             INNER JOIN account_payable_invoice api ON apil.ap_invoice_id = api.ap_invoice_id
                             WHERE api.status IN ('DRAFT', 'ISSUED') and apil.grn_line_id = grnl.grn_line_id
                        ),
                        COALESCE(
                            (
                                select json_agg(json_build_object(
                                    'po_line_status_id', pols.po_line_status_id,
                                    'status_name', pols.status_name,
                                    'is_system_field', pols.is_system_field,
                                    'quantity', polhs.quantity,
                                    'po_line_id', polhs.po_line_id,
                                    'po_id', polhs.po_id,
                                    'line_status_received_qty', COALESCE(
                                        (
                                            SELECT (status_elem->>'line_status_received_qty')::numeric
                                            FROM jsonb_array_elements(COALESCE(grnl.po_line_status, '[]'::jsonb)) AS status_elem
                                            WHERE (status_elem->>'po_line_status_id')::integer = pols.po_line_status_id
                                            LIMIT 1
                                        ), 
                                        0
                                    )
                                ))
                                from po_line_has_status polhs
                                inner join po_line_status pols on pols.po_line_status_id = polhs.po_line_status_id
                                where polhs.po_line_id = pol.po_line_id
                            ),
                            '[]'::json
                        ) as po_line_status,
                        CASE 
                            WHEN grnl.grn_entity_line_id IS NULL THEN true
                            ELSE false
                        END as is_adhoc_line,
                        (
                            SELECT
                            jsonb_agg(json_build_object(
                                'created_at', tpb2.created_at,
                                'batch_id', exploded_batches.value->'batch_id',
                                'batch_number', exploded_batches.value->'batch_number',
                                'is_rejected_batch', exploded_batches.value->'is_rejected_batch',
                                'quantity', exploded_batches.value->'quantity',
                                'uom_id', exploded_batches.value->'uom_id',
                                'tenant_department_id', exploded_batches.value->'tenant_department_id',
                                'tenant_product_id', exploded_batches.value->'tenant_product_id',
                                'expiry_date', exploded_batches.value->'expiry_date',
                                'custom_batch_number', exploded_batches.value->'custom_batch_number',
                                'cost_price', exploded_batches.value->'cost_price',
                                'selling_price', exploded_batches.value->'selling_price',
                                'mrp', exploded_batches.value->'mrp',
                                'seller_id', exploded_batches.value->'seller_id',
                                'seller_name', exploded_batches.value->'seller_name',
                                'lot_number', exploded_batches.value->'lot_number',
                                'inventory_location_id', exploded_batches.value->'inventory_location_id',
                                'inventory_location_name', il.inventory_location_name,
                                'inventory_parent_id', il.parent_id,
                                'batch_created_qty', exploded_batches.value->'batch_created_qty',
                                'ar_number', exploded_batches.value->'ar_number',
                                'seller_name',exploded_batches.value->'seller_name',
                                'seller_id',exploded_batches.value->'seller_id',
                                'roll_no', exploded_batches.value->'roll_no',
                                'freight_cost', exploded_batches.value->'freight_cost',
                                'other_cost', exploded_batches.value->'other_cost',
                                'landed_cost', exploded_batches.value->'landed_cost',
                                'margin', exploded_batches.value->'margin',
                                'brand', exploded_batches.value->'brand',
                                'mfg_batch_no', exploded_batches.value->'mfg_batch_no',
                                'is_restricted', exploded_batches.value->'is_restricted',
                                'manufacturing_date', exploded_batches.value->'manufacturing_date',
                                'next_qc_date', exploded_batches.value->'next_qc_date',
                                'last_qc_date', exploded_batches.value->'last_qc_date',
                                'batch_inward_date', exploded_batches.value->'batch_inward_date',
                                'custom_fields', exploded_batches.value->'custom_fields',
                                'batch_custom_fields', ${getCfValueByEntityIdAndName("(exploded_batches->>'batch_id')::integer",`${CF_ENTITY.BATCH.name}`,'psku1.org_id')},
                                ${params.grn_id ? `'quality_checks', (
                                    SELECT array_to_json(array_agg(qc)) as quality_checks FROM (
                                        SELECT qc.*,
                                        (
                                            SELECT row_to_json(tpb) as rejected_batch FROM (
                                                SELECT tpb.*, 
                                                ${getCfValueByEntityIdAndName("tpb.batch_id",`${CF_ENTITY.BATCH.name}`,'psku1.org_id')} as custom_fields
                                                from tenant_product_batches tpb
                                                WHERE tpb.batch_id = qc.rejected_batch_id
                                            ) tpb
                                        ) as rejected_batch,
                                         (
                                            SELECT row_to_json(tpb) as approved_batch FROM (
                                                SELECT tpb.*, 
                                                ${getCfValueByEntityIdAndName("tpb.batch_id",`${CF_ENTITY.BATCH.name}`,'psku1.org_id')} as custom_fields
                                                from tenant_product_batches tpb
                                                WHERE tpb.batch_id = qc.batch_id
                                            ) tpb
                                        ) as approved_batch,
                                        case
                                            WHEN grn.status = 'ISSUED' then false
                                            else true
                                        end as is_qc_editable
                                        FROM quality_checks qc
                                        WHERE qc.entity_id = ${params.grn_id} AND qc.entity_name = 'good_receiving_note'
                                        AND qc.batch_id = coalesce((exploded_batches->>'batch_id')::integer,0)
                                        ) qc
                                    ),`
                                    : ""
                                }
                                'inventory_location_path',il.inventory_location_path
                                )) as product_batch_row
                            FROM good_receiving_note_line grnl1
                            CROSS JOIN LATERAL json_array_elements(grnl1.product_batches) AS exploded_batches
                            LEFT JOIN inventory_location il ON il.inventory_location_id = (exploded_batches->>'inventory_location_id')::integer
                            LEFT JOIN tenant_product_batches tpb2 ON tpb2.batch_id = (exploded_batches->>'batch_id')::integer 
                            LEFT JOIN tenant_product tp1 ON tp1.tenant_product_id = grnl1.tenant_product_id
                            LEFT JOIN product_sku psku1 ON psku1.product_sku_id = tp1.product_sku_id
                            where grnl1.grn_line_id = grnl.grn_line_id
                        ) as product_batches,
                        (
                            SELECT array_to_json(array_agg(tmp)) as available_batches
                            from
                            (
                                SELECT 
                                il.*,tpb.*,
                                coalesce(tpb.is_rejected_batch, false) as is_rejected_batch
                                FROM tenant_product_batches tpb
                                LEFT JOIN inventory_location il
                                    ON il.inventory_location_id = tpb.inventory_location_id
                                inner join tenant t_po on t_po.tenant_id = grn.grn_tenant_id
                                WHERE
                                    tpb.tenant_product_id = grnl.tenant_product_id and
                                    tpb.available_qty > 0 and
                                    tpb.tenant_department_id = grn.tenant_department_id
                                ORDER BY tpb.created_at desc
                            ) tmp
                        ),
                        (
                            SELECT row_to_json(stld) as stock_transfer_line_details
                            from (
                                select *,
                                (
                                    SELECT SUM(grnl.quantity) as consumed_qty
                                    FROM good_receiving_note_line grnl 
                                    INNER JOIN good_receiving_note grn ON grn.grn_id = grnl.grn_id
                                    WHERE grn.grn_entity_type = 'INVENTORY_TRANSFER'
                                    AND grn.status IN ('ISSUED','SENT_FOR_APPROVAL')
                                    AND grnl.grn_entity_line_id = stl.stock_transfer_line_id
                                ),
                                (
                                    SELECT SUM(dcl.quantity) as dc_quantity
                                    FROM delivery_challan_lines dcl 
                                    INNER JOIN delivery_challan dc ON dc.delivery_challan_id = dcl.challan_id
                                    WHERE dcl.stock_transfer_line_id = stl.stock_transfer_line_id
                                ),
                                (
                                    select row_to_json(pi) as product_sku_info
                                    from
                                    (
                                        select 
                                        tp.threshold_qty, ps.product_sku_name, ps.assets, 
                                        uom.uom_name as unit, ps.hsn_code, uom.group_id,
                                        ps.expiry_days, ps.purchase_uom_info, ps.uom_info, ps.uom_list, ps.secondary_uom_id, ps.secondary_uom_info,
                                        ps.internal_sku_code, ps.product_batch_counter, ps.ref_product_code,
                                        ps.product_type, ps.expiry_date_format, ps.manufacturing_date_format,
                                        ps.default_outwards_method,
                                          (
                                            SELECT row_to_json(tmp) as tax_info FROM
                                            (
                                                SELECT t.* ,
                                                (
                                                    SELECT json_agg(child_taxes) as child_taxes
                                                    from (
                                                        SELECT t2.*,
                                                        tt2.*
                                                        FROM tax_group tg 
                                                        INNER JOIN tax t2 ON t2.tax_id = tg.child_tax_id
                                                        INNER JOIN tax_type tt2 ON tt2.tax_type_id = t2.tax_type
                                                        WHERE tg.group_tax_id = t.tax_id
                                                    ) child_taxes
                                                ),
                                                tt.*
                                                FROM tax t
                                                INNER JOIN tax_type tt ON tt.tax_type_id = t.tax_type
                                                WHERE t.tax_id = ps.tax_id
                                            ) tmp 
                                          )
                                        from tenant_product tp
                                        inner join product_sku ps
                                        on ps.product_sku_id = tp.product_sku_id
                                        inner join uom
                                        on uom.uom_id = ps.uom_id
                                        where 
                                            tp.tenant_product_id = stl.tenant_product_id
                                    ) pi
                                ),
                                (
                                    select array_to_json(array_agg(pi)) as available_batches
                                    from
                                    (
                                        select tpb.*
                                        from tenant_product_batches tpb
                                        where tpb.tenant_product_id = stl.tenant_product_id
                                        and tpb.tenant_department_id = st.source_tenant_department_id
                                        AND tpb.available_qty <> 0
                                    ) pi
                                ),
                                (
                                    select SUM(tpb.available_qty) as total_available_batches_quantity
                                    from tenant_product_batches tpb
                                    where tpb.tenant_product_id = stl.tenant_product_id
                                    and tpb.tenant_department_id = st.source_tenant_department_id
                                ),
                                (
                                    select row_to_json(pi) as product_batch_info
                                    from
                                    (
                                        select * 
                                        from tenant_product_batches tpb
                                        where tpb.batch_id = stl.source_batch_id
                                        and tpb.tenant_department_id = st.source_tenant_department_id
                                    ) pi
                                )
                                from stock_transfer_lines stl
                                INNER JOIN stock_transfer st ON stl.stock_transfer_id = st.stock_transfer_id
                                where stl.stock_transfer_line_id = grnl.grn_entity_line_id
                            ) stld
                        ),
                        (
                            select 
                                case
                                    when sum(dnl.quantity) is null then 0
                                    else sum(dnl.quantity)
                                end as dn_qty
                            from debit_note_line dnl
                            inner join debit_note dn on dnl.dn_id = dn.dn_id
                            where
                                dnl.grn_line_id = grnl.grn_line_id and
                                dn.grn_id = grn.grn_id and
                                dn.status not in ('VOID', 'REJECTED')
                        ),
                        (
                            select row_to_json(psi) as product_sku_info
                            from
                            (
                                select 
                                    ps.product_sku_id, ps.product_sku_name, ps.assets,
                                    tp.tenant_product_id, tp.alias_name, tp.threshold_qty, ps.hsn_code,
                                    ps.uom_id, uom.group_id, uom.uqc, uom.ratio,
                                    ps.expiry_days, ps.purchase_uom_info, ps.uom_info, ps.secondary_uom_id, ps.secondary_uom_info,
                                    ip.zoho_sku_code, ps.internal_sku_code,
                                    ps.product_batch_counter, ps.tally_product_name, 
                                    ps.tally_updated_at, ps.tally_stock_group_name,
                                    ps.tally_unit_name,  ps.uom_list,
                                    ps.expiry_date_format, ps.manufacturing_date_format,
                                    ps.product_type, ps.ref_product_code,ps.ar_number_prefix,ps.ar_number_counter,
                                    ps.default_outwards_method,
                                    (
                                        select
                                               case
                                                  when sum(tpb.available_qty) is null
                                                  then 0
                                                  else sum(tpb.available_qty)
                                               end as available_qty
                                        from tenant_product_batches tpb
                                        where tpb.tenant_product_id = tp.tenant_product_id
                                     ), 
                                    uom.uom_name as unit,
                                     (
                                        SELECT row_to_json(product_category_info) as product_category_info 
                                        from (
                                            SELECT *
                                            FROM product_category pc2
                                            where pc2.product_category_id = ps.product_category_id
                                        )product_category_info
                                     )
                                from product_sku ps
                                LEFT JOIN integration_product ip ON ip.product_sku_id = ps.product_sku_id
                                inner join tenant_product tp ON ps.product_sku_id = tp.product_sku_id
                                inner join uom on uom.uom_id = ps.uom_id
                                where tp.tenant_product_id = grnl.tenant_product_id
                            ) psi
                        ),
                        (
                            select pol.quantity as ordered_qty
                            from purchase_order_line pol
                            where pol.po_line_id = grnl.grn_entity_line_id
                        ),
                        (
                            select 
                                case
                                    when sum(grnl2.quantity) is null then 0
                                    else sum(grnl2.quantity)
                                end as total_received_qty
                            from good_receiving_note_line grnl2
                            inner join good_receiving_note grn2
                            on grn2.grn_id = grnl2.grn_id
                            where 
                                grnl.grn_entity_line_id = grnl2.grn_entity_line_id and
                                grn2.status in ('DRAFT', 'ISSUED') and
                                grnl2.grn_line_id != grnl.grn_line_id and
                                grn2.grn_id != grn.grn_id
                        ), 
                        (
                            SELECT 
                                CASE
                                    WHEN SUM(apil.quantity) IS NULL THEN 0
                                    ELSE SUM(apil.quantity)
                                END AS total_ap_invoice_qty
                            FROM account_payable_invoice_line apil
                            WHERE apil.grn_line_id = grnl.grn_line_id
                        ),
                        (
                            select stl.quantity as transferred_qty
                            from stock_transfer_lines stl
                            where stl.stock_transfer_line_id = grnl.grn_entity_line_id
                        )
                        FROM good_receiving_note_line grnl
                        left join tax as ots ON ots.tax_id = grnl.tax_id
                        LEFT JOIN purchase_order_line pol ON pol.po_line_id = grnl.grn_entity_line_id
                        LEFT JOIN purchase_order po ON po.po_id = pol.po_id
                        where grnl.grn_id = grn.grn_id
                        order by grnl.grn_line_id
                    ) grnls
                ),
                 (
                    select row_to_json(grnbdtls.*) as grn_billing_details
                    from 
                    (
                        select
                            grnbd.received_amount,
                            grnbd.total_dn_amount,
                            case
                                when (grnbd.received_amount + grnbd.total_dn_amount ) > grn.grn_grand_total and grn.status='ISSUED' then 'PAID_MORE'
                                when ((grnbd.received_amount + grnbd.total_dn_amount ) < grn.grn_grand_total) and (grnbd.received_amount + grnbd.total_dn_amount) > 0 and grn.status='ISSUED' then 'PARTIALLY_PAID'
                                when (grnbd.received_amount + grnbd.total_dn_amount ) = grn.grn_grand_total and grn.status='ISSUED' then 'PAID'
                                when (grnbd.received_amount + grnbd.total_dn_amount ) = 0 then 'NOT_PAID'
                            end as grn_billing_status
                        from
                        (
                            SELECT
                            COALESCE(SUM(CASE WHEN grnp.applied_payment_type = 'PAYMENT' THEN grnp.amount ELSE 0 
                            END), 0) AS received_amount,
                            COALESCE(SUM(CASE WHEN grnp.applied_payment_type = 'DEBIT_NOTE' THEN grnp.amount ELSE 0 
                            END), 0) AS total_dn_amount
                            from grn_payments grnp
                            left join payments_outgoing po on po.payment_id = grnp.payment_id AND grnp.applied_payment_type = 'PAYMENT'
                            left join debit_note dn on dn.dn_id = grnp.payment_id AND grnp.applied_payment_type='DEBIT_NOTE'
                            where grnp.entity_id = grn.grn_id and grn.status='ISSUED' and (po.payment_status ='PAYMENT_SUCCESS' OR dn.status = 'CONFIRMED')
                        ) grnbd
                    ) grnbdtls
                ),
                (
                    SELECT row_to_json(oc.*) as org_currency_info
                    FROM
                    (
                        SELECT * FROM org_currency
                        where org_currency_id = grn.org_currency_id
                    ) oc
                ),
                (
                    SELECT row_to_json(oc.*) as base_currency_info
                    FROM
                    (
                        SELECT * FROM org_currency oc
                        where oc.org_id= tnt.org_id and oc.is_default=true
                    ) oc
                ),
                (
                    SELECT json_agg(qcs) as grn_quality_checks
                    from (
                       select qc.*
                       from quality_checks qc
                       where qc.entity_id = grn.grn_id and qc.entity_name = 'good_receiving_note'
                    ) qcs
                ),
                (
                    SELECT row_to_json(workflow_steps) as workflow_steps
                    FROM
                    (
                        SELECT
                        ${this.getWorkflowQueryGenerator()}
                        where ws.workflow_step_id = grn.workflow_step_id
                    ) workflow_steps
                ),
                (
                    SELECT itw.is_good_receiving_note_approval_enabled
                    FROM integration_tenant_whatsapp itw 
                    WHERE itw.tenant_id = grn.grn_tenant_id
                )
            from good_receiving_note grn
            LEFT JOIN tenant_department td on td.tenant_department_id = grn.tenant_department_id
            LEFT JOIN purchase_order po on grn.grn_entity_id = po.po_id AND grn.grn_entity_type = 'PURCHASE_ORDER'
            LEFT JOIN seller s on s.seller_id = grn.seller_id
            LEFT JOIN integration_seller islr ON islr.seller_id = s.seller_id
            LEFT JOIN tenant tnt ON tnt.tenant_id = grn.grn_tenant_id
            LEFT JOIN workflow_steps ws on grn.workflow_step_id = ws.workflow_step_id
            LEFT JOIN integration_busy_tenant_mapping ibtm ON ibtm.tenant_id = grn.grn_tenant_id
            ${whereCases.length ? "where " + whereCases.join(" and ") : ""}
            order by grn.grn_id desc
            limit ${limit} 
        ) grn_list
    `;

    let _grn = await client._query(getGrnQuery, [], {
        user_id: params.action_by,
        apply_data_masking: true
    });
    _grn = _grn.rows[0];
    let count = _grn.count;

    let grn = _grn.grn_list || [];

    // Map returned quantity at batch level in grn lines and overide currency if user select other than default currency
    grn = await Promise.all(
        grn.map(async (gn) => {
            let grnLines = gn.grn_lines;
            grnLines = await Promise.all(
                grnLines.map(async (grnl) => {

                    let pending_billing_quantity = grnl.invoice_quantity - grnl.ap_invoiced_quantity;
                    grnl.pending_billing_quantity = pending_billing_quantity;
                    let grnProductBatches = grnl.product_batches || [];
                    let grnLineId = grnl.grn_line_id;

                    let getDnProductBatchesQuery = `
                    select
                        dnl.product_batches, dnl.dn_line_id
                    from debit_note_line dnl
                    inner join debit_note dn on dn.dn_id = dnl.dn_id
                    where
                        dn.status != 'VOID' and
                        dnl.grn_line_id = ${grnLineId}
                `;
                    let _dnProductBatches = await client.query(getDnProductBatchesQuery);
                    _dnProductBatches = _dnProductBatches.rows || [];

                    grnProductBatches = await Promise.all(
                        grnProductBatches.map(async (pb) => {
                            let grnBatchId = pb.batch_id;

                            let dnGenQty = 0;
                            _dnProductBatches.map((dpbs) => {
                                let cpbsProductBatches = dpbs.product_batches || [];
                                cpbsProductBatches.map((dpb) => {
                                    if (dpb.batch_id == grnBatchId) dnGenQty += dpb.quantity;
                                });
                            });

                            pb.dn_gen_qty = dnGenQty;
                            return pb;
                        })
                    );
                    grnl.product_batches = grnProductBatches;
                    return grnl;
                })
            );
            gn.grn_lines = grnLines;
            return gn;
        })
    );
    grn.forEach(gn => {
        if (gn.org_currency_id && currency_convertion) {
            gn.grn_base_price = gn.document_grn_base_price;
            gn.grn_grand_total_amount = gn.grn_grand_total;
            gn.grn_grand_total = gn.document_grn_grand_total;
            gn.grn_gst = gn.document_grn_gst;
            gn.grn_round_off = gn.document_grn_round_off;
            gn.grn_total = gn.document_grn_total;
            gn.discount_amount = gn.document_discount_amount;
            gn.payment_made = gn.total_payment_made;
            gn.total_payment_made = gn.total_payment_made / gn.conversion_rate;
            gn.charge_1_value = gn.document_charge_1_value;
            gn.charge_2_value = gn.document_charge_2_value;
            if (gn.debit_note_info) {
                gn.debit_note_info?.forEach(dn => {
                    dn.document_db_total = dn.db_total / gn.conversion_rate;
                })
            }

            if (gn.other_charges) {
                gn.other_charges?.forEach(charge => {
                    charge.charge_amount = charge.document_charge_amount;
                })
            }
            if (gn.tcs_info) {
                gn.tcs_info.tcs_amount = gn.tcs_info?.document_tcs_amount;
            }
            if (gn.tds_info) {
                gn.tds_info.tds_amount = gn.tds_info?.document_tds_amount;
            }
            if(gn.tax_info?.length){
                gn?.tax_info?.forEach(tax =>{
                    if(tax?.document_tax_amount)
                        tax.tax_amount = tax.document_tax_amount
                })
                if( gn?.tax_info?.child_taxes?.length){
                    gn?.tax_info?.child_taxes?.forEach((ctax)=>{
                        if(ctax?.document_tax_amount)
                            ctax.tax_amount = ctax.document_tax_amount 
                    })
                }
            }
            if(gn?.freight_tax_info){
                if(gn?.freight_tax_info?.document_tax_amount){
                    gn.freight_tax_info.tax_amount =gn.freight_tax_info.document_tax_amount
                }
                gn.freight_tax_info?.child_taxes?.forEach(ctax =>{
                    if(ctax?.document_tax_amount){
                        ctax.tax_amount = ctax.document_tax_amount ;
                    }
                   
                })
            }
            gn.grn_lines.forEach(grnl => {
                grnl.line_discount_amount = grnl.document_line_discount_amount;
                grnl.offer_price = grnl.document_offer_price;
                grnl.total_price = grnl.document_total_price;
                grnl.value_before_tax = grnl.document_value_before_tax;
                if(grnl.tax_info){
                    if(grnl?.tax_info?.document_tax_amount ){
                        grnl.tax_info.tax_amount = grnl.tax_info.document_tax_amount 
                    }
                    if( grnl?.tax_info?.child_taxes?.length){
                        grnl?.tax_info?.child_taxes?.forEach((ctax)=>{
                            if(ctax?.document_tax_amount){
                                ctax.tax_amount  = ctax.document_tax_amount 
                            }
                        })
                    }
                }
            })
        }
        if(params?.grn_id){
            let taxable_amount = gn?.grn_base_price - gn?.discount_amount;
            if(gn?.freight_tax_id){
                taxable_amount+= gn?.charge_1_value;
            }
            gn?.other_charges?.forEach((charge)=>{
                if(charge?.tax_info){
                    taxable_amount+= charge?.charge_amount;
                }
            })
            gn.taxable_amount = taxable_amount;
        }
        gn?.grn_lines?.forEach((grnl) => {
            grnl?.product_batches?.forEach((pb) => {
            if(pb.batch_custom_fields){
                pb.custom_fields = pb.batch_custom_fields;
            }
            pb.custom_fields = pb?.custom_fields?.map((cf) => {
                cf.default_expression = cf?.field_value
                return cf;
            })
            });
        })
    })
    if (params.schedule_grn == true || params.schedule_grn == 'true') {
        grn = grn.filter(gn => {
            return gn?.grn_grand_total - (gn?.total_payment_made || 0 + gn?.debit_note_info?.[0]?.db_total || 0) > 0
        })
    }
    return {
        count: params.schedule_grn ? grn.length : Number(count),
        tenant_info: _grn.tenant_info,
        grn: grn,
        page: page,
        limit: limit,
    };
};

function trimObjectForCustomerAPIs(_input) {
    let final_object = {
      "grn_id": _input.grn_id,
      "grn_tenant_id": _input.grn_tenant_id,
      "status": _input.status,
      "grn_date_time": _input.grn_date_time,
      "created_at": _input.created_at,
      "delivery_document_1": _input.delivery_document_1,
      "remark": _input.remark,
      "grn_entity_type": _input.grn_entity_type,
      "grn_entity_id": _input.grn_entity_id,
      "grn_base_price": _input.grn_base_price,
      "grn_gst": _input.grn_gst,
      "grn_total": _input.grn_total,
      "grn_round_off": _input.grn_round_off,
      "grn_grand_total": _input.grn_grand_total,
      "attachments": _input.attachments,
      "custom_fields": _input.custom_fields,
      "grn_number": _input.grn_number,
      "invoice_date": _input.invoice_date,
      "invoice_number": _input.invoice_number,
      "seller_address_info": {
        "address1": _input?.seller_address_info?.address1,
        "address2": _input?.seller_address_info?.address2,
        "address3": _input?.seller_address_info?.address3,
        "city": _input?.seller_address_info?.city,
        "state": _input?.seller_address_info?.state,
        "country": _input?.seller_address_info?.country,
        "postal_code": _input?.seller_address_info?.postal_code,
        "tally_address_type": _input?.seller_address_info?.tally_address_type,
      },
      "tenant_info": {
        "tenant_id": _input?.tenant_info?.tenant_id,
        "tenant_name": _input?.tenant_info?.tenant_name,
        "legal_name": _input?.tenant_info?.legal_name,
        "branch_code": _input?.tenant_info?.branch_code
      },
      "tenant_seller_info": {
        "tenant_seller_id": _input?.tenant_seller_info?.tenant_seller_id,
        "seller_name": _input?.tenant_seller_info?.seller_name,
        "gst_number": _input?.tenant_seller_info?.gst_number
      },
      "tax_info": _input.tax_info.map(tax => ({
        "tax_amount": tax.tax_amount,
        "tax_type_name": tax.tax_type_name
      })),
      "tenant_department_info": {
        "tenant_department_id": _input?.tenant_department_info?.tenant_department_id,
        "department_id": _input?.tenant_department_info?.department_id,
        "alias_name": _input?.tenant_department_info?.alias_name,
        "is_active": _input?.tenant_department_info?.is_active,
        "tenant_id": _input?.tenant_department_info?.tenant_id,
        "department_name": _input?.tenant_department_info?.department_name
      },
      "organisation_info": {
        "org_logo": _input?.organisation_info?.org_logo,
        "currency_code": _input?.organisation_info?.currency_code,
        "org_id": _input?.organisation_info?.org_id
      },
      "tenant_billing_address": {
        "address_id": _input?.tenant_billing_address?.address_id,
        "address1": _input?.tenant_billing_address?.address1,
        "address2": _input?.tenant_billing_address?.address2,
        "address3": _input?.tenant_billing_address?.address3,
        "city": _input?.tenant_billing_address?.city,
        "state": _input?.tenant_billing_address?.state,
        "country": _input?.tenant_billing_address?.country,
        "postal_code": _input?.tenant_billing_address?.postal_code,
        "zone": _input?.tenant_billing_address?.zone,
        "gst_number": _input?.tenant_billing_address?.gst_number,
        "phone_number": _input?.tenant_billing_address?.phone_number
      },
      "grn_lines": _input.grn_lines.map(line => ({
        "grn_line_id": line.grn_line_id,
        "grn_id": line.grn_id,
        "received_qty": line.received_qty,
        "line_discount_percentage": line.line_discount_percentage,
        "line_discount_amount": line.line_discount_amount,
        "value_before_tax": line.value_before_tax,
        "status": line.status,
        "tenant_product_id": line.tenant_product_id,
        "grn_entity_line_id": line.grn_entity_line_id,
        "remarks": line.remarks,
        "offer_price": line.offer_price,
        "pan_india_gst": line.pan_india_gst,
        "tax_id": line.tax_id,
        "tax_name": line.tax_name,
        "tax_value": line.tax_value,
        "tax_type": line.tax_type,
        "total_price": line.total_price,
        "expiry_date": line.expiry_date,
        "description": line.description,
        "tax_info": {
          "tax_name": line.tax_info.tax_name,
          "tax_value": line.tax_info.tax_value,
          "country_code": line.tax_info.country_code,
          "tax_type_name": line.tax_info.tax_type_name,
          "taxed_value": line.tax_info.taxed_value,
          "tax_amount": line.tax_info.tax_amount,
          "child_taxes": line.tax_info.child_taxes.map(childTax => ({
            "tax_name": childTax.tax_name,
            "tax_value": childTax.tax_value,
            "tax_amount": childTax.tax_amount,
            "taxable_value": childTax.taxable_value,
            "value_after_tax": childTax.value_after_tax,
            "taxed_value": childTax.taxed_value
          }))
        },
        "uom_info": line.uom_info.map(uom => ({
          "uom_id": uom.uom_id,
          "uom_name": uom.uom_name,
          "ratio": uom.ratio,
          "uqc": uom.uqc,
          "precision": uom.precision
        })),
        "product_sku_info": {
            "product_sku_id": line?.product_sku_info?.product_sku_id,
            "product_sku_name": line?.product_sku_info?.product_sku_name,
            "internal_sku_code": line?.product_sku_info?.internal_sku_code,
            "ref_product_code": line?.product_sku_info?.ref_product_code,
            "unit": line?.product_sku_info?.unit
        }
      }))
    };
    return final_object;
}

exports.getGRNForCustomerAPIs = async (params) => {

    let page = Number(params?.page || 1);
    let limit = Number(params?.limit || 10);

    // Verify Data that is coming from API
    let organisation = await GetOrganisation(params);
    let _verifyTenantIds = organisation.verifyTenantIds({
        tenant_id: params.tenant_id
    });
    if(_verifyTenantIds===false) throw new Error("You don't have access to this tenant").UI("You don't have access to this tenant", 401)
    
    // Main logic to get GRN
    let _result = await this.getGRNFromDB(params, page, limit, db);
    _result.grn = (_result?.grn || [])?.map(i=>{
        return trimObjectForCustomerAPIs(i);
    })
    return  _result;
}
