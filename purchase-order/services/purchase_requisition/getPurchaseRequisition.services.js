const { tenantAuthv1 } = require("../../../common/tenant_auth_v1");

const { db, user_permissions: _up } = require("../../../config");

exports.getWorkflowQueryGenerator = (steps = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]) => {

    let _select_part = steps.map(_n => {
        return `
            CASE WHEN  ws.step_1_role_id IS NOT NULL THEN (
                json_build_object(
                        'workflow_step_id', ws.workflow_step_id,
                        'status', ws.step_1_status,
                        'taken_at', ws.step_1_taken_at,
                        'taken_by', ws.step_1_taken_by,
                        'taken_by_name', aus1.first_name,
                        'taken_by_email', aus1.email,
                        'role_id', ws.step_1_role_id,
                        'role_name', trs1.tenant_role_name,
                        'role_department_name', trs1.tenant_department_name,
                        'rejection_reason', CASE WHEN ws.rejection_step = 1
                                            THEN
                                                ws.rejection_reason 
                                            ELSE
                                                null
                                            END,
                        'rejection_step', CASE WHEN ws.rejection_step = 1
                                            THEN
                                                ws.rejection_step 
                                            ELSE
                                                null
                                            END,
                        'rejection_at', CASE WHEN ws.rejection_step = 1
                                        THEN
                                            ws.rejection_at 
                                        ELSE
                                            null
                                        END,
                        'rejection_by', CASE WHEN ws.rejection_step = 1
                                        THEN
                                            ws.rejection_by 
                                        ELSE
                                            null
                                        END
                    )
                )
            ELSE (null) END step_1
        `.replace(/1/g, _n);
    });

    let _join_part = steps.map(_n => {
        return `
            LEFT JOIN tenant_role trs1 ON trs1.tenant_role_id = ws.step_1_role_id
            LEFT JOIN app_user aus1 ON aus1.user_id = ws.step_1_taken_by

        `.replace(/1/g, _n);
    });

    return `
        ${_select_part.join(",")}
        FROM workflow_steps ws
        ${_join_part.join(" ")}
    `;
};


exports.getPurchaseRequisition = async (
    _input,
    page = 1,
    limit = 30,
    client = db
) => {

    const tenant_role_id = _input.tenant_role_id; // Optional
    const source_tenant_department_id = _input.source_tenant_department_id; // Optional
    const source_tenant_id = _input.source_tenant_id
    let tenant_department_id = (_input?.tenant_department_id || "").split(',').filter(i => i);
    let tenantIdArr = (_input?.tenant_id || "").split(',').filter(i => i);
    let department_ids = (_input?.department_ids || "").split(',').filter(i => i);
    if(_input.purchase_requisition_id){
        _input.purchase_requisition_id = comma_separated_to_array(_input.purchase_requisition_id);
    }
    // Access / Security Check
    // if(_input.action_by && _input.tenant_id) 
    //     await tenantAuthv1(_input.action_by, _input.tenant_id, _up.purchase_request.READ);

    let _input_tenant_id;
    if (source_tenant_department_id) {
        let _query = `
            SELECT td.tenant_id
            FROM tenant_department td
            WHERE td.tenant_department_id = ${source_tenant_department_id}
        `;
        let _result = await client.query(_query);
        _input_tenant_id = _result?.rows?.[0]?.tenant_id;
    }
    let get_lines=false;
    if(_input.purchase_requisition_id || _input.get_lines==true){
        get_lines=true;
    }
    let queries = [];
    if (_input.status) queries.push(`pr.status in ('${_input.status.replace(/,/g, "','")}')`);
    if (_input.fulfillment_status) queries.push(`pr.fulfillment_status in ('${_input.fulfillment_status.replace(/,/g, "','")}')`);
    if (_input?.purchase_requisition_id?.length == 1) queries.push(`pr.purchase_requisition_id=${_input?.purchase_requisition_id?.[0]}`);
    if (_input?.purchase_requisition_id?.length > 1) queries.push(`pr.purchase_requisition_id IN (${_input?.purchase_requisition_id.join(",")})`);
    if (_input.org_id) queries.push(`tnt.org_id=${_input.org_id}`);
    if (_input.tenant_id) queries.push(`pr.tenant_id in (${tenantIdArr.join(",")})`);
    if (_input.on_date) queries.push(`pr.pr_date::date='${_input.on_date}'`);
    if (_input.mo_id) queries.push(`pr.mo_id=${_input.mo_id}`);
    if (_input.from_date)
        queries.push(`pr.pr_date::date>='${_input.from_date}'`);
    if (_input.to_date)
        queries.push(`pr.pr_date::date<='${_input.to_date}'`);
    if (_input.delivery_from_date)
        queries.push(`pr.delivery_date::timestamp>TO_TIMESTAMP(${_input.delivery_from_date}/ 1000)`);
    if (_input.delivery_to_date)
        queries.push(`pr.delivery_date::timestamp<TO_TIMESTAMP(${_input.delivery_to_date}/ 1000)`);
    if (tenant_department_id.length)
        queries.push(`pr.tenant_department_id IN (${tenant_department_id.join(",")})`);
    if (department_ids.length)
        queries.push(`d.department_id IN (${department_ids.join(",")})`);
      if (_input.product_sku_ids) {
        let product_sku_ids = _input.product_sku_ids.split(",")
        let product_id_search = []
        product_sku_ids.map(id => product_id_search.push(`pr.product_sku_ids @> '[${id}]'`))
        queries.push(`(
            ${product_id_search.join(" OR ")}
        )`)
    }
    if (_input?.tags) {
        let separate_tags = _input?.tags.split(",")
        let tags_cases = []
        separate_tags.map(tag => tags_cases.push(`pr.tags @> ARRAY['${tag}']`))
        queries.push(`(
                ${tags_cases.join(" OR ")}
            )`)
    }
    if(_input.mrp_id)
        queries.push(`pr.mrp_id = ${_input.mrp_id}`);
        
    
    // SQL like documentation
    if (_input.search_keyword)
        queries.push(`
            pr.purchase_requisition_id || ' ' || (
                        SELECT String_agg(usr.first_name || ' ' || usr.last_name, ' ')
                        FROM app_user usr
                        where usr.user_id = pr.created_by
                    ) || ' ' || (
            SELECT concat(string_agg(po.po_number, ' '), ' ', pr.pr_number, ' ')
            FROM purchase_order po WHERE pr.purchase_requisition_id = po.purchase_requisition_id
            ) ilike '%${_input.search_keyword}%'
        `);

    // If tenant role id is in req, it will filter out all the PR which requires 
    // approval w.r.t tenant role id
    if (tenant_role_id)
        queries.push(`
            ( 
                ( ws.step_1_role_id = ${tenant_role_id} AND 
                  ws.step_1_status = 'PENDING'
                ) OR
                ( ws.step_2_role_id = ${tenant_role_id} AND 
                  ws.step_2_status = 'PENDING' AND
                  ws.step_1_status = 'APPROVED'
                ) OR
                ( ws.step_3_role_id = ${tenant_role_id} AND 
                  ws.step_3_status = 'PENDING' AND
                  ws.step_2_status = 'APPROVED'
                ) OR
                ( ws.step_4_role_id = ${tenant_role_id} AND 
                  ws.step_4_status = 'PENDING' AND
                  ws.step_3_status = 'APPROVED'
                ) OR
                ( ws.step_5_role_id = ${tenant_role_id} AND 
                  ws.step_5_status = 'PENDING' AND
                  ws.step_4_status = 'APPROVED'
                ) OR
                ( ws.step_6_role_id = ${tenant_role_id} AND 
                  ws.step_6_status = 'PENDING' AND
                  ws.step_5_status = 'APPROVED'
                ) OR
                ( 
                    ws.step_7_role_id = ${tenant_role_id} AND 
                    ws.step_7_status = 'PENDING' AND
                    ws.step_6_status = 'APPROVED'
                ) OR
                ( 
                    ws.step_8_role_id = ${tenant_role_id} AND 
                    ws.step_8_status = 'PENDING' AND
                    ws.step_7_status = 'APPROVED'
                ) OR
                ( 
                    ws.step_9_role_id = ${tenant_role_id} AND 
                    ws.step_9_status = 'PENDING' AND
                    ws.step_8_status = 'APPROVED'
                ) OR
                ( 
                    ws.step_10_role_id = ${tenant_role_id} AND 
                    ws.step_10_status = 'PENDING' AND
                    ws.step_9_status = 'APPROVED'
                )
            )
        `);

        let count_where_cases = queries.map((i) => i);

        // This keyword is used to search like full text. it is bit complex according
        if (!_input?.purchase_requisition_id?.length) {
            let __pointer = await client.query(`
              SELECT pr.purchase_requisition_id
              FROM purchase_requisition pr
              JOIN tenant tnt ON tnt.tenant_id = pr.tenant_id
              LEFT JOIN tenant t2 ON t2.tenant_id = pr.issuer_tenant_id
              LEFT JOIN tenant_department td ON td.tenant_department_id = pr.tenant_department_id 
              LEFT JOIN tenant_department td2 ON td2.tenant_department_id = pr.issuer_tenant_department_id
              LEFT JOIN workflow_steps ws on pr.workflow_step_id = ws.workflow_step_id
              LEFT JOIN department d on d.department_id = td.department_id
              LEFT JOIN department d2 on d2.department_id = td2.department_id
              ${count_where_cases.length ? ` WHERE ${count_where_cases.join(" AND ")}` : ""}
              ORDER BY pr.purchase_requisition_id desc
              limit ${limit} 
              offset ${(page - 1) * limit}
             `);
             let _pointer = __pointer?.rows?.[0];
             let pointer = _pointer?.purchase_requisition_id;
             if (pointer) queries.push(`pr.purchase_requisition_id <= ${pointer}`);
             else queries.push(`pr.purchase_requisition_id <= 0`);
        }
        
    // This is main query
    let _query = `
        SELECT
        ${!_input?.purchase_requisition_id?.length?
        `(
            SELECT COUNT(*) as count
            FROM purchase_requisition pr
            JOIN tenant tnt ON tnt.tenant_id = pr.tenant_id
            LEFT JOIN tenant t2 ON t2.tenant_id = pr.issuer_tenant_id
            LEFT JOIN tenant_department td ON td.tenant_department_id = pr.tenant_department_id 
            LEFT JOIN tenant_department td2 ON td2.tenant_department_id = pr.issuer_tenant_department_id
            LEFT JOIN workflow_steps ws on pr.workflow_step_id = ws.workflow_step_id
            LEFT JOIN department d on d.department_id =td.department_id
            LEFT JOIN department d2 on d2.department_id = td2.department_id
            ${count_where_cases.length ? ` WHERE ${count_where_cases.join(" AND ")}` : ""}
        ),
        `:""}
        array_to_json(array_agg(pr_list.*)) AS purchase_requisition
        FROM
        (
            select tnt.org_id, pr.*, pr.delivery_date::date,pr.pr_date::date,pr.created_at::date,t2.tenant_name as issuer_tenant_name,
            td.department_id, td2.department_id as issuer_department_id, td.alias_name AS tenant_department_alias_name, pr.custom_fields,
            json_build_object('department_id', td.department_id, 'department_name', d.department_name) AS department_info,
            json_build_object('department_id', td2.department_id, 'department_name', d2.department_name) AS issuer_department_info,
            --// * Tenant Info
            (
                SELECT row_to_json(tenant_info.*) AS tenant_info
                FROM
                (
                    SELECT tnt.*,
                    (
                        SELECT row_to_json(a.*) AS default_billing_address_info
                        FROM addresses a
                        WHERE a.address_id = tnt.default_billing_address
                    )
                    FROM tenant tnt
                    WHERE tnt.tenant_id=pr.tenant_id
                )tenant_info
            ),
            --// * Organisation Info
            (
                SELECT row_to_json(orga) AS organisation_info
                FROM
                (
                    SELECT org.logo as org_logo,org.currency_code
                    FROM organisation org
                    WHERE tnt.org_id = org.org_id
                ) orga
            ),
            --// * Shipping Address Info
            (
                SELECT row_to_json(ad.*) AS shipping_address
                FROM  addresses ad
                WHERE ad.address_id = pr.shipping_address_id
            ),
            --// * Billing Address Info
            (
                SELECT row_to_json(ad.*) AS billing_address
                FROM  addresses ad
                WHERE ad.address_id = pr.billing_address_id
            ),
            --// * created by info
            (
                SELECT row_to_json(tmp1) AS created_by_info
                FROM
                (
                    SELECT usr.first_name, usr.last_name,
                        usr.username, usr.mobile
                    FROM app_user usr
                    where usr.user_id = pr.created_by
                ) tmp1
            ),
            --// * reference_stock_transfers
            (
                SELECT array_to_json(array_agg(tmp1)) AS reference_stock_transfers
                FROM
                (
                    SELECT 
                    st.stock_transfer_id, st.stock_transfer_number,
                    st.status, st.source_tenant_department_id,
                    st.destination_tenant_department_id,
                    td1.alias_name AS source_department_alias_name, 
                    td2.alias_name AS destination_department_alias_name,
                    (
                        SELECT COUNT(stl.*) 
                        FROM stock_transfer_lines stl 
                        WHERE stl.stock_transfer_id = st.stock_transfer_id
                    )
                    FROM stock_transfer st
                    LEFT JOIN tenant_department td1 
                        ON td1.tenant_department_id = st.source_tenant_department_id
                    LEFT JOIN tenant_department td2
                        ON td2.tenant_department_id = st.destination_tenant_department_id
                    WHERE EXISTS (
                        SELECT 1
                        FROM unnest(string_to_array(st.ref_pr_ids, ',')) AS ref_pr_id
                        WHERE ref_pr_id::integer = pr.purchase_requisition_id
                    )
                ) tmp1
            ),
            --// * reference_manufacturing_order
            (
                SELECT row_to_json(tmp) AS reference_manufacturing_order
                FROM (
                    SELECT mo.mo_id, mo.mo_number, mo.status, mo.closed_at
                    FROM  manufacturing_order mo
                    WHERE pr.mo_id = mo.mo_id
                ) tmp
            ),
            (
                SELECT json_agg(tmp) AS reference_rfq
                FROM (
                    SELECT rfq.rfq_id, rfq.rfq_number, rfq.status
                    FROM  rfq rfq
                    WHERE rfq.purchase_requisition_id = pr.purchase_requisition_id
                ) tmp
            ),
            (
                SELECT json_agg(tmp) AS reference_purchase_indents
                FROM (
                    SELECT pi.pi_id, pi.pi_number, pi.status
                    FROM purchase_indent pi
                    WHERE pi.pr_id = pr.purchase_requisition_id
                ) tmp
            ),
            --// * reference_purchase_orders
            (
                SELECT json_agg(po) AS reference_purchase_orders
                FROM
                (
                    SELECT po.po_id,po.po_number,
                    (
                        SELECT row_to_json(slr) AS seller_info
                        FROM  
                        (
                            SELECT s.seller_name,
                            (
                                select row_to_json(mad) as manufacturing_address_details
                                from
                                (
                                    select 
                                        ad.address_id, ad.address1, ad.address2,
                                        ad.address3, ad.city, ad.state, ad.country,
                                        ad.postal_code 
                                    from addresses ad
                                    where ad.address_id = s.manufacturing_address_id
                                ) mad
                            ),
                            (
                                select row_to_json(oad) as office_address_details
                                from
                                (
                                    select 
                                        ad.address_id, ad.address1, ad.address2,
                                        ad.address3, ad.city, ad.state, ad.country,
                                        ad.postal_code 
                                    from addresses ad
                                    where ad.address_id = s.office_address_id
                                ) oad
                            )
                            FROM seller s
                            JOIN tenant_seller ts ON ts.seller_id = s.seller_id
                            WHERE ts.tenant_seller_id = po.tenant_seller_id
                        ) slr
                    ),
                    (
                        SELECT COUNT(pol.po_line_id) as items_count
                        FROM purchase_order_line pol 
                        WHERE pol.po_id = po.po_id
                    ),
                    po.status,po.po_grand_total
                    FROM purchase_order po
                    WHERE pr.purchase_requisition_id in (SELECT unnest(string_to_array(po.ref_pr_ids, ','))::int)
                ) po
            ),
            ${
                _input.purchase_requisition_id
                  ? `
               (
                   SELECT row_to_json(dcs) AS document_tenant_configuration
                   FROM
                   (
                       SELECT dc.*
                       FROM document_config dc
                       WHERE dc.tenant_id=tnt.tenant_id
                       AND dc.entity_name='PURCHASE_REQUEST'
                   )dcs
               ),
               (
                    SELECT row_to_json(tenant_configuration.*) AS tenant_configuration
                    FROM
                    (
                        SELECT tnt_conf.*
                        FROM tenant_configuration tnt_conf
                        WHERE tnt_conf.tenant_id=pr.tenant_id
                    )tenant_configuration
                ),
               `
                  : ""
              }
            --// * Purchase Requisition Lines
            ${get_lines ? `
            (
                SELECT array_to_json(array_agg(tmp)) as purchase_requistion_lines 
                FROM
                (
                    SELECT pr_line.*, ots.tax_value as pan_india_gst, ots.*, tp.tenant_product_id AS tenant_product_id,tp.department_quantities,
                    tp.secondary_uom_dept_qty,
                    um.uom_name, um.uom_id, um.group_id, um.ratio, um.uqc, tp.cost_price, pr_line.uom_info,
                    (
                        SELECT json_agg(pol) AS reference_po_lines
                        FROM
                        (
                            SELECT pol.po_id,pol.po_line_id,pol.quantity
                            FROM purchase_order_line pol
                            LEFT JOIN purchase_order po ON po.po_id = pol.po_id
                            LEFT JOIN rfq_lines rfql ON rfql.rfq_line_id = pol.rfq_line_id
                            LEFT JOIN purchase_indent_line pil ON pil.pi_line_id = COALESCE(pol.pi_line_id, rfql.pi_line_id)
                            WHERE
                                pr_line.pr_line_id = COALESCE (pol.pr_line_id, rfql.pr_line_id, pil.pr_line_id)
                                AND po.status = 'ISSUED'
                        ) pol
                    ),
                    ${_input.purchase_requisition_id
                        ? `
                     (
                        SELECT json_agg(pos) AS pos_quantity_against_product
                        FROM(
                            SELECT
                            s.internal_slr_code,
                            s.seller_id,
                            SUM(COALESCE(pol.quantity, 0)) AS total_pending_quantity,
                            CASE
                                WHEN (
                                    SELECT SUM(COALESCE(grnl2.quantity, 0))
                                    FROM good_receiving_note_line grnl2
                                    JOIN good_receiving_note grn ON grn.grn_id = grnl2.grn_id
                                    WHERE grnl2.grn_entity_line_id = pol.po_line_id AND grn.status='ISSUED'
                                ) > pol.quantity THEN pol.quantity
                                ELSE (
                                    SELECT SUM(COALESCE(grnl2.quantity, 0))
                                    FROM good_receiving_note_line grnl2
                                    JOIN good_receiving_note grn ON grn.grn_id = grnl2.grn_id
                                    WHERE grnl2.grn_entity_line_id = pol.po_line_id AND grn.status='ISSUED'
                                )
                            END AS total_received_quantity,
                            s.seller_name
                        FROM purchase_order_line pol
                        JOIN purchase_order po ON po.po_id = pol.po_id
                        JOIN tenant_seller ts ON ts.tenant_seller_id = (po.tenant_seller_info->>'tenant_seller_id')::integer
                        JOIN seller s ON s.seller_id = ts.seller_id
                        WHERE pol.tenant_product_id = tp.tenant_product_id AND po.status='ISSUED'
                        GROUP BY s.internal_slr_code, s.seller_id, s.seller_name,pol.po_line_id               
                        )pos
                    ),
                     `
                    : ""
                    }
                    (
                        select
                              case
                                  when sum(tpb.available_qty) is null
                                  then 0
                                  else sum(tpb.available_qty)
                              end as available_qty
                        from tenant_product_batches tpb
                        where tpb.tenant_product_id = tp.tenant_product_id
                    ),
                    (
                      select array_to_json(array_agg(tmp)) as product_batches
                      from
                      (
                        select
                            tbml.batch_id,
                            tbml.batch_number,
                            tbml.tenant_product_id,
                            tbml.expiry_date,
                            tbml.created_by,
                            tbml.created_at,
                            tbml.tenant_department_id,
                            tbml.lot_number,
                            tbml.custom_batch_number,
                            tbml.cost_price,
                            tbml.selling_price,
                            tbml.inventory_location_id,
                            tbml.mrp,
                            tbml.is_rejected_batch,
                            tbml.org_id,
                            tbml.tenant_id,
                            tbml.is_archived,
                            tbml.product_sku_id,
                            tbml.batch_barcode,
                            tbml.entity_id,
                            tbml.entity_name,
                            tbml.seller_id,
                            tbml.seller_name,
                            tbml.unicommerce_status,
                            tbml.batch_inward_date,
                            tbml.ar_number,
                            tbml.unicommerce_pushed_at,
                            tbml.retest_date,
                            tbml.roll_no,
                            tbml.freight_cost,
                            tbml.other_cost,
                            tbml.landed_cost,
                            tbml.margin,
                            tbml.brand,
                            tbml.mfg_batch_no,
                            tbml.is_restricted,
                            tbml.manufacturing_date,
                            tbml.next_qc_date,
                            tbml.last_qc_date,
                            br.reservation_id as reservation_id,
                            true as is_reserved,
                            (br.reserved_qty - br.consumed_qty) as available_qty,
                            tbml.available_qty as on_hand_qty
                        from batch_reservations br
                        inner join tenant_product_batches tbml on tbml.batch_id = br.batch_id
                        where
                            br.tenant_product_id = tp.tenant_product_id and
                            (br.reserved_qty - br.consumed_qty) > 0 and
                            br.reservation_entity_name = 'MANUFACTURING_ORDER' and
                            br.reservation_entity_line_name = 'RAW_MATERIAL' and
                            br.reservation_entity_id = pr.mo_id and
                            br.reservation_entity_line_id = pr_line.mo_line_id and
                            br.reserved_qty > 0
                        UNION ALL
                        select 
                            tpb.batch_id,
                            tpb.batch_number,
                            tpb.tenant_product_id,
                            tpb.expiry_date,
                            tpb.created_by,
                            tpb.created_at,
                            tpb.tenant_department_id,
                            tpb.lot_number,
                            tpb.custom_batch_number,
                            tpb.cost_price,
                            tpb.selling_price,
                            tpb.inventory_location_id,
                            tpb.mrp,
                            tpb.is_rejected_batch,
                            tpb.org_id,
                            tpb.tenant_id,
                            tpb.is_archived,
                            tpb.product_sku_id,
                            tpb.batch_barcode,
                            tpb.entity_id,
                            tpb.entity_name,
                            tpb.seller_id,
                            tpb.seller_name,
                            tpb.unicommerce_status,
                            tpb.batch_inward_date,
                            tpb.ar_number,
                            tpb.unicommerce_pushed_at,
                            tpb.retest_date,
                            tpb.roll_no,
                            tpb.freight_cost,
                            tpb.other_cost,
                            tpb.landed_cost,
                            tpb.margin,
                            tpb.brand,
                            tpb.mfg_batch_no,
                            tpb.is_restricted,
                            tpb.manufacturing_date,
                            tpb.next_qc_date,
                            tpb.last_qc_date,
                            null as reservation_id,
                            false as is_reserved,
                            (tpb.available_qty - coalesce((
                                select coalesce(sum(br1.reserved_qty)) - coalesce(sum(br1.consumed_qty))
                                from batch_reservations br1
                                where br1.batch_id = tpb.batch_id
                                group by br1.batch_id
                            ), 0)) as available_qty,
                            tpb.available_qty as on_hand_qty
                        from tenant_product_batches tpb
                        where
                          tpb.tenant_product_id = tp.tenant_product_id
                          AND (tpb.available_qty - coalesce((
                                select coalesce(sum(br1.reserved_qty)) - coalesce(sum(br1.consumed_qty))
                                from batch_reservations br1
                                where br1.batch_id = tpb.batch_id
                                group by br1.batch_id
                            ), 0)) > 0
                      ) tmp
                    ),
                    ${source_tenant_department_id ? `
                    (
                        select array_to_json(array_agg(tmp)) as tenant_department_product_batches
                        from
                        (
                          select 
                            tpb.batch_id, 
                            tpb.batch_number, 
                            tpb.expiry_date, 
                            tpb.available_qty, 
                            tpb.tenant_department_id,
                            tpb.custom_batch_number, 
                            tpb.cost_price, 
                            tpb.selling_price, 
                            tpb.lot_number,
                            tpb.ar_number
                          from tenant_product_batches tpb
                          where
                            tpb.product_sku_id = tp.product_sku_id
                            AND tpb.available_qty <> 0
                            AND tpb.tenant_department_id = ${source_tenant_department_id}
                          order by tpb.expiry_date asc
                        ) tmp
                    ),
                    `: ""}
                    ${source_tenant_id ? `
                    (
                        select array_to_json(array_agg(tmp)) as source_tenant_product_batches
                        from
                        (
                          select 
                            tpb.batch_id,
                            tpb.batch_number,
                            tpb.tenant_product_id,
                            tpb.expiry_date,
                            tpb.created_by,
                            tpb.created_at,
                            tpb.tenant_department_id,
                            tpb.lot_number,
                            tpb.custom_batch_number,
                            tpb.cost_price,
                            tpb.selling_price,
                            tpb.inventory_location_id,
                            tpb.mrp,
                            tpb.is_rejected_batch,
                            tpb.org_id,
                            tpb.tenant_id,
                            tpb.is_archived,
                            tpb.product_sku_id,
                            tpb.batch_barcode,
                            tpb.entity_id,
                            tpb.entity_name,
                            tpb.seller_id,
                            tpb.seller_name,
                            tpb.unicommerce_status,
                            tpb.batch_inward_date,
                            tpb.ar_number,
                            tpb.unicommerce_pushed_at,
                            tpb.retest_date,
                            tpb.roll_no,
                            tpb.freight_cost,
                            tpb.other_cost,
                            tpb.landed_cost,
                            tpb.margin,
                            tpb.brand,
                            tpb.mfg_batch_no,
                            tpb.is_restricted,
                            tpb.manufacturing_date,
                            tpb.next_qc_date,
                            tpb.last_qc_date,
                            null as reservation_id,
                            false as is_reserved,
                            (tpb.available_qty - coalesce((
                                select coalesce(sum(br1.reserved_qty)) - coalesce(sum(br1.consumed_qty))
                                from batch_reservations br1
                                where br1.batch_id = tpb.batch_id
                                group by br1.batch_id
                            ), 0)) as available_qty,
                            tpb.available_qty as on_hand_qty
                        from tenant_product_batches tpb
                        where
                          tpb.product_sku_id = tp.product_sku_id
                          AND (tpb.available_qty - coalesce((
                                select coalesce(sum(br1.reserved_qty)) - coalesce(sum(br1.consumed_qty))
                                from batch_reservations br1
                                where br1.batch_id = tpb.batch_id
                                group by br1.batch_id
                            ), 0)) > 0
                            AND tpb.tenant_id = ${source_tenant_id}
                          order by tpb.expiry_date asc
                        ) tmp
                    ),
                    (
                        SELECT tp2.department_quantities as source_department_quantities
                        FROM tenant_product tp2
                        WHERE tp2.product_sku_id = tp.product_sku_id
                            AND tp2.tenant_id = ${source_tenant_id}
                    ),
                    `: ""}
                    (
                        SELECT SUM(pol.quantity) as total_po_quantity
                        FROM purchase_order_line pol
                        INNER JOIN purchase_order po ON po.po_id = pol.po_id
                        WHERE pol.pr_line_id = pr_line.pr_line_id 
                            AND po.status NOT IN ('CANCELLED')
                    ),
                    (
                        SELECT row_to_json(_ps) AS product_sku_info
                        FROM (
                            SELECT _ps.*,
                            (
                                SELECT row_to_json(tmp) as tax_info FROM
                                (
                                    SELECT t.* ,
                                    (
                                        SELECT json_agg(child_taxes) as child_taxes
                                        from (
                                            SELECT t2.*,
                                            tt2.*
                                            FROM tax_group tg 
                                            INNER JOIN tax t2 ON t2.tax_id = tg.child_tax_id
                                            INNER JOIN tax_type tt2 ON tt2.tax_type_id = t2.tax_type
                                            WHERE tg.group_tax_id = t.tax_id
                                        ) child_taxes
                                    ),
                                    tt.*
                                    FROM tax t
                                    INNER JOIN tax_type tt ON tt.tax_type_id = t.tax_type
                                    WHERE t.tax_id = _ps.tax_id
                                ) tmp 
                              ),
                              (
                                SELECT row_to_json(product_category_info) as product_category_info 
                                from (
                                    SELECT *
                                    FROM product_category pc2
                                    where pc2.product_category_id = _ps.product_category_id
                                )product_category_info
                            )
                            FROM product_sku _ps
                            WHERE _ps.product_sku_id = tp_tmp.product_sku_id
                        ) _ps
                    ),
                    (
                        SELECT SUM(stl.quantity) as st_gen_quantity
                        FROM stock_transfer_lines stl
                        INNER JOIN stock_transfer st ON st.stock_transfer_id = stl.stock_transfer_id
                        WHERE stl.pr_line_id = pr_line.pr_line_id
                        AND st.status NOT IN ('VOID')
                    )
                    FROM purchase_requisition_line pr_line
                    LEFT JOIN tax ots ON ots.tax_id = pr_line.tax_id
                    LEFT JOIN uom um on um.uom_id = pr_line.uom_id
                    LEFT JOIN tenant_product tp_tmp ON tp_tmp.tenant_product_id = pr_line.tenant_product_id
                    LEFT JOIN tenant_product tp 
                        ON tp.product_sku_id = tp_tmp.product_sku_id
                        AND tp.tenant_id = ${_input_tenant_id ? _input_tenant_id : "COALESCE(pr.issuer_tenant_id, pr.tenant_id)"}
                    WHERE pr_line.purchase_requisition_id = pr.purchase_requisition_id
                    ORDER BY pr_line.pr_line_id
                ) tmp
            ),
            ` : `
                (SELECT COUNT(pr_line.pr_line_id) lines_count
                FROM purchase_requisition_line pr_line
                WHERE pr_line.purchase_requisition_id = pr.purchase_requisition_id),
            `}
            (
                SELECT row_to_json(workflow_steps) as workflow_steps
                FROM
                (
                    SELECT
                    ${this.getWorkflowQueryGenerator()}
                    where ws.workflow_step_id = pr.workflow_step_id
                ) workflow_steps
            ),
            (
                SELECT itw.is_purchase_requisition_approval_enabled
                FROM integration_tenant_whatsapp itw 
                WHERE itw.tenant_id = pr.tenant_id
            )
            FROM purchase_requisition pr
            JOIN tenant tnt ON tnt.tenant_id = pr.tenant_id
            LEFT JOIN tenant t2 ON t2.tenant_id = pr.issuer_tenant_id
            LEFT JOIN tenant_department td ON td.tenant_department_id = pr.tenant_department_id
            LEFT JOIN tenant_department td2 ON td2.tenant_department_id = pr.issuer_tenant_department_id
            LEFT JOIN department d ON d.department_id = td.department_id
            LEFT JOIN department d2 on d2.department_id = td2.department_id
            LEFT JOIN workflow_steps ws on pr.workflow_step_id = ws.workflow_step_id
            ${queries.length ? ` WHERE ${queries.join(" AND ")}` : ""}
            ORDER BY pr.created_at DESC
            LIMIT ${limit} 
        ) pr_list
    `;
    let { rows } = await client._query(_query,[],{
        user_id: _input?.action_by,
        apply_data_masking: true
    });
    let count = Number(rows?.[0]?.count || 0);
    let purchase_requisitions = rows?.[0]?.purchase_requisition || [];
    if(_input.purchase_requisition_id){
        purchase_requisitions?.forEach(pr =>{
            pr?.purchase_requistion_lines?.forEach(line =>{
                if(line.pos_quantity_against_product?.length >0 ){
                    const data = line?.pos_quantity_against_product.reduce((acc, item) => {
                        const key = `${item.internal_slr_code}_${item.seller_id}_${item.seller_name}`;
                        
                        if (!acc[key]) {
                          acc[key] = {
                            internal_slr_code: item.internal_slr_code,
                            seller_id: item.seller_id,
                            seller_name: item.seller_name,
                            total_pending_quantity: item?.total_pending_quantity || 0,
                            total_received_quantity: item?.total_received_quantity || 0,
                          };
                        }else{
                            acc[key].total_pending_quantity += item.total_pending_quantity;
                            acc[key].total_received_quantity += item.total_received_quantity || 0;
                        }
                        return acc;
                      }, {});
                      
                      const result = Object.values(data);
                      line.pos_quantity_against_product = result;
                }
            })
        })
    }

    purchase_requisitions?.forEach((pr) => {
        pr?.purchase_requistion_lines?.forEach((prl) => {
            let ordered_quantity = 0;

            prl.reference_po_lines?.forEach((pol) => {
                ordered_quantity += pol.quantity;
            });
            
            const DELTA = 0.25;

            prl.ordered_quantity = ordered_quantity;
            prl.pending_quantity = prl.quantity - ordered_quantity;
            
            if (prl.ordered_quantity >= prl.quantity - DELTA) {
                prl.purchase_status = 'COMPLETED';
            } else if (prl.ordered_quantity > 0) {
                prl.purchase_status = 'PARTIAL';
            } else {
                prl.purchase_status = 'PENDING';
            }
        });
    });    

    return {
        success: true,
        count: count,
        filters: _input,
        purchase_requisition: purchase_requisitions,
    };
};
