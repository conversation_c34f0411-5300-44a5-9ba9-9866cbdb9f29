const { db } = require('../../../../config');
const DELTA = 0.25;

exports.updatePRFulfillmentStatus = async (_input, client = null) => {
    client = await db.begin(client);

    try {
        const { purchase_requisition_id } = _input;

        if (!purchase_requisition_id) throw new Error("Purchase Requisition ID is required");
        
            const update_status_query = `
                UPDATE purchase_requisition
                SET fulfillment_status =
                    CASE
                        WHEN COALESCE(totals.total_pr_quantity - (totals.total_po_quantity + totals.total_st_quantity), 0) < ${DELTA} THEN 'FULFILLED'
                        WHEN COALESCE(totals.total_po_quantity + totals.total_st_quantity, 0) > 0 
                            AND COALESCE(totals.total_po_quantity + totals.total_st_quantity, 0) < COALESCE(totals.total_pr_quantity, 0) THEN 'PARTIALLY_FULFILLED'
                        ELSE 'PENDING'
                    END
                FROM (
                    SELECT 
                        pr.purchase_requisition_id,
                        COALESCE((SELECT SUM(COALESCE(
                            convert_uom_quantity(prl.quantity, psk.uom_list::jsonb, prl.uom_id, psk.uom_id), 0
                        )) FROM purchase_requisition_line prl
                        LEFT JOIN tenant_product tp ON tp.tenant_product_id = prl.tenant_product_id
                        LEFT JOIN product_sku psk ON psk.product_sku_id = tp.product_sku_id
                        WHERE prl.purchase_requisition_id = pr.purchase_requisition_id), 0) AS total_pr_quantity,
                        COALESCE((SELECT SUM(COALESCE(
                            convert_uom_quantity(pol.quantity, psk.uom_list::jsonb, pol.uom_id, psk.uom_id), 0
                        )) FROM purchase_order_line pol
                        JOIN purchase_order po ON po.po_id = pol.po_id AND po.status = 'ISSUED'
                        LEFT JOIN tenant_product tp ON tp.tenant_product_id = pol.tenant_product_id
                        LEFT JOIN product_sku psk ON psk.product_sku_id = tp.product_sku_id
                        LEFT JOIN rfq_lines rfql ON rfql.rfq_line_id = pol.rfq_line_id
                        LEFT JOIN purchase_indent_line pil ON pil.pi_line_id = COALESCE (pol.pi_line_id, rfql.pi_line_id)
                        WHERE COALESCE (pol.pr_line_id, rfql.pr_line_id, pil.pr_line_id) IN (
                            SELECT pr_line_id 
                            FROM purchase_requisition_line prl
                            WHERE prl.purchase_requisition_id = pr.purchase_requisition_id)
                        ), 0) AS total_po_quantity,
                        COALESCE((SELECT SUM(COALESCE(
                            convert_uom_quantity(stl.quantity, psk.uom_list::jsonb, stl.uom_id, psk.uom_id), 0
                        )) FROM stock_transfer_lines stl
                        JOIN stock_transfer st ON st.stock_transfer_id = stl.stock_transfer_id AND st.status = 'ISSUED'
                        LEFT JOIN tenant_product tp ON tp.tenant_product_id = stl.tenant_product_id
                        LEFT JOIN product_sku psk ON psk.product_sku_id = tp.product_sku_id
                        WHERE stl.pr_line_id IN (
                            SELECT pr_line_id 
                            FROM purchase_requisition_line prl
                            WHERE prl.purchase_requisition_id = pr.purchase_requisition_id)
                        ), 0) AS total_st_quantity
                    FROM 
                        purchase_requisition pr 
                    WHERE 
                        pr.purchase_requisition_id IN (${purchase_requisition_id})
                ) AS totals
                WHERE 
                    purchase_requisition.purchase_requisition_id = totals.purchase_requisition_id;
            `;

            await client.query(update_status_query);
            await client.commit();

        return { message: 'PR Fulfillment Status Updated Successfully' };
    } catch (error) {
        await client.rollback();
        throw error;
    }
};

