const Joi = require('joi');
const { RFQ_PARTICIPANTS_VALIDATORS } = require('./rfq_participants.joi');

let create_rfq_line = Joi.object({
    rfq_line_id : Joi.number(),
    product_sku_id : Joi.number().required(),
    product_sku_name : Joi.string().required(),
    tenant_id : Joi.number().required(),
    delivery_tenant_id : Joi.number().required(),
    product_specification : Joi.array().items(Joi.object({
        spec_name : Joi.string(),
        spec_value : Joi.string()
    })).allow(null),
    quantity : Joi.number().required(),
    uom_id : Joi.number().required(),
    uom_info : Joi.object().required(),
    tax_id : Joi.number().required(),
    tax_group_info : Joi.object().required(),
    delivery_date : Joi.string().allow(null),
    shipping_address_info : Joi.object().allow(null),
    quotation_price : Joi.number().allow(null),
    rfq_line_questions : Joi.array().items(Joi.object({
        question : Joi.string(),
        is_mandatory : Joi.bool()
    })).allow(null),
    pr_line_id : Joi.number().allow(null),
    pi_line_id : Joi.number().allow(null)
})

let create_rfq = {
    strategy : Joi.string().default('RANK_BASED'),
    selection : Joi.string().valid(...['SINGLE_VENDOR','MULTIPLE_VENDOR']).required(),
    duration : Joi.number(),
    begin_time : Joi.number(),
    end_time : Joi.number(),
    is_delivery_date_at_product_level : Joi.boolean().default(false).required(),
    add_sku_vendors:Joi.boolean().default(false),
    price_with_tax : Joi.boolean().default(false),
    delivery_date : Joi.string().allow(null),
    other_charges : Joi.array().items(Joi.object({
        charge_name : Joi.string().required(),
        value : Joi.number().required()
    })).allow(null),
    description : Joi.string().allow("",null),
    billing_address_info : Joi.object().allow(null),
    tenant_info : Joi.object().allow(null),
    tenant_id : Joi.number().required(),
    org_id : Joi.number().required(),
    terms_and_condition : Joi.string().allow("",null),
    status : Joi.string().default("DRAFT").required(),
    created_by : Joi.number().required(),
    modified_by : Joi.number(),
    attachments : Joi.array().allow(null),
    public_attachments : Joi.array().allow(null),
    rfq_questions : Joi.array().items(Joi.object({
        question : Joi.string(),
        is_mandatory : Joi.bool()
    })).allow(null),
    rfq_lines : Joi.array().items(create_rfq_line).min(1),
    rfq_participants : Joi.array().allow(null),
    purchase_requisition_id : Joi.number().allow(null),
    seq_id: Joi.number().allow(null),
    rfq_number: Joi.string().allow(null, ''),
    pi_id : Joi.number().allow(null),
};
let update_rfq = {
    rfq_id : Joi.number(),
    selection : Joi.string().valid(...['SINGLE_VENDOR','MULTIPLE_VENDOR']).required(),
    strategy : Joi.string(),
    duration : Joi.number(),
    end_time : Joi.number(),
    begin_time : Joi.number(),
    is_delivery_date_at_product_level : Joi.boolean().default(false).required(),
    add_sku_vendors:Joi.boolean().default(false),
    price_with_tax : Joi.boolean().default(false),
    delivery_date : Joi.string().allow(null),
    other_charges : Joi.array().items(Joi.object({
        charge_name : Joi.string().required(),
        value : Joi.number().required()
    })).allow(null),
    description : Joi.string().allow("",null),
    tenant_info : Joi.object().allow(null),
    tenant_id : Joi.number().required(),
    org_id : Joi.number().required(),
    terms_and_condition : Joi.string().allow("",null),
    status : Joi.string().default("DRAFT").required(),
    created_by : Joi.number(),
    modified_by : Joi.number().required(),
    attachments : Joi.array().allow(null),
    public_attachments : Joi.array().allow(null),
    rfq_questions : Joi.array().items(Joi.object({
        question : Joi.string(),
        is_mandatory : Joi.bool()
    })).allow(null),
    rfq_lines : Joi.array().items(create_rfq_line).min(1),
    rfq_participants : Joi.array().allow(null),
    purchase_requisition_id : Joi.number().allow(null),
    rfq_number: Joi.string().allow(null, ''),
    pi_id : Joi.number().allow(null),
};

let update_rfq_configuration = {
    rfq_id : Joi.number().required(),
    strategy : Joi.string(),
    selection : Joi.string(),
    show_lowest_price_to_vendor : Joi.boolean(),
    show_rank_to_vendor : Joi.boolean(),
    duration : Joi.number(),
    price_with_tax : Joi.boolean(),
    begin_time : Joi.number(),
    terms_and_condition : Joi.string(),
    end_time : Joi.number()
}


exports.VALIDATORS = {
    CREATE_RFQ: Joi.object(create_rfq),
    UPDATE_RFQ: Joi.object(update_rfq),
    UPDATE_RFQ_CONFIGURATION : Joi.object(update_rfq_configuration)
};


exports.create_rfq_validator = (_input) => __global_joi.validate(this.VALIDATORS.CREATE_RFQ, _input);
exports.update_rfq_validator = (_input) => __global_joi.validate(this.VALIDATORS.UPDATE_RFQ, _input);
exports.update_rfq_line_validator = (_input) => __global_joi.validate(this.VALIDATORS.UPDATE_RFQ, _input);
exports.update_rfq_configuration_validator = (_input) => __global_joi.validate(this.VALIDATORS.UPDATE_RFQ_CONFIGURATION, _input);