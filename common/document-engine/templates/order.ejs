<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Procuzy</title>
    <style>
        @media print {
            .move-to-next-page {
                page-break-after: always;
            }
        }

        @media print {
            .div-to-next-page {
                page-break-inside: avoid;
            }
        }

        @media print {
            .footer-text {
                display: none;
            }

            .footer-text:last-child {
                display: block;
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                text-align: center;
                padding: 10px;
            }
        }
    </style>
</head>

<body style="overflow-x: hidden; width: 100%; font-family: Arial; padding: 0px; margin: 0">
    <% let uniqueFields = collectUniqueFields(order.sales_order_lines, 'so_line_custom_fields') %>
    <div style="line-height: 14px">
        <% if(order?.document_tenant_configuration?.print_prepared_by == true || order?.document_tenant_configuration?.print_prepared_by == 'true') {%>
          <div style="text-align: right; padding-right: 30px;font-size: 10px">
            <% if(order?.created_by) {%>
              Prepared By: <%= order?.created_by_info?.first_name + order?.created_by_info?.last_name%>
            <%} %>
          </div>
        <%} %>
      
        <% if(order?.document_tenant_configuration?.print_prepared_at == true || order?.document_tenant_configuration?.print_prepared_at == 'true') {%>
          <div style="text-align: right; padding-right: 30px;font-size: 10px">
            <% if(order?.created_at) {%>
              Prepared At: <%= new Date().formatDateTime(order?.created_at) %>
            <%} %>
          </div>
        <%} %>
    </div>
    <% if(order.beautify) { %>
        <div
            style="padding-right: 30px; width: <%= order.width%>;margin: <%= order.margin%>;box-shadow: <%= order.shadow%>;padding-left: 30px; padding-top: 0px; padding-bottom: 0px;">
            <% } else {%>
                <div style="padding-right: 30px; padding-left: 30px; padding-top: 0px; padding-bottom: 0px;">
                    <% } %>
                        <div
                            style="font-size: 26px; color: <%= order.tenant_configuration.doc_primary_colour%>; padding: 20px 0px 20px 0px">
                            <%= order?.document_tenant_configuration?.document_title %>
                        </div>
                        <div style="display: flex;flex-flow: row wrap; justify-content: space-around;">
                            <div style="flex: 5;">
                                <div style="display: flex; font-size:12px; font-weight: 700; line-height: 20px">
                                    <div style="width: 150px; color: #939393;">
                                        <%= order.order_type === 'SALES_ESTIMATE' ? document_ui_names.estimateUIName : order.order_type === 'PACKING_SLIP' ? 'Packing Slip' : order?.order_type === 'ANNUAL_MAINTENANCE_CONTRACT' ? document_ui_names?.amcUIName : document_ui_names.salesOrderUIName %> #
                                    </div>
                                    <div style="width: 150px;">
                                        <%= order.sales_order_number %>
                                    </div>
                                </div>

                                <% if(order?.document_tenant_configuration?.print_status==true
                                || order?.document_tenant_configuration?.print_status=='true' ) {%>
                                  <div style="display: flex; font-size: 12px; font-weight: 700;line-height: 20px">
                                    <div style="width: 150px; color: #939393;">Status</div>
                                    <div style="width: 150px;">
                                      <%= order.secondary_status?.toProperCase() %>
                                    </div>
                                  </div>
                                  <% } %>
                                <div style="display: flex; font-size:12px; font-weight: 700;line-height: 20px">
                                    <div style="width: 150px; color: #939393;">
                                        <%= order.order_type === 'SALES_ESTIMATE' ? document_ui_names.estimateShortUIName : order.order_type === 'PACKING_SLIP' ? 'Packing' : order?.order_type === 'ANNUAL_MAINTENANCE_CONTRACT' ? document_ui_names?.amcShorUIName : document_ui_names.salesOrderShortUIName %> Date
                                    </div>
                                    <div style="width: 150px;">
                                        <%= new Date(order.order_date).toDateString().substring(4); %>
                                    </div>
                                </div>
                                <% if(order.order_type !== 'PACKING_SLIP') { %>
                                <% if((order.document_tenant_configuration?.print_delivery_date == true || order.document_tenant_configuration?.print_delivery_date == 'true') && order.delivery_date) {%>
                                <div style="display: flex; font-size:12px; font-weight: 700;line-height: 20px">
                                    <div style="width: 150px; color: #939393;">Delivery Date</div>
                                    <div style="width: 150px;">
                                        <%= new Date(order.delivery_date).toDateString().substring(4); %>
                                    </div>
                                </div>
                                <%}%>
                                <%}%>
                                <% if(order.order_type == 'PACKING_SLIP') { %>
                                    <% if(order?.ps_so_details?.sales_order_number) {%>
                                    <div style="display: flex; font-size:12px; font-weight: 700;line-height: 20px">
                                        <div style="width: 150px; color: #939393;"><%= document_ui_names.salesOrderUIName %> #</div>
                                        <div style="width: 150px;">
                                            <%= order?.ps_so_details?.sales_order_number%>
                                        </div>
                                    </div>
                                    <%}%>
                                    <% if(order?.customer_info?.customer_name) {%>
                                    <div style="display: flex; font-size:12px; font-weight: 700;line-height: 20px">
                                        <div style="width: 150px; color: #939393;">Customer name </div>
                                        <div style="width: 150px;">
                                            <%= order?.customer_info?.customer_name%>
                                        </div>
                                    </div>
                                    <%}%>
                                    <% if(order?.vehicle_number) {%>
                                    <div style="display: flex; font-size:12px; font-weight: 700;line-height: 20px">
                                        <div style="width: 150px; color: #939393;">Vehicle number</div>
                                        <div style="width: 150px;">
                                            <%= order?.vehicle_number%>
                                        </div>
                                    </div>
                                    <%}%>
                                    <% if(order?.remarks) {%>
                                    <div style="display: flex; font-size:12px; font-weight: 700;line-height: 20px">
                                        <div style="width: 150px; color: #939393;">Remarks</div>
                                        <div style="width: 150px;">
                                            <%= order?.remarks%>
                                        </div>
                                    </div>
                                    <%}%>
                                <%}%>
                                <% if(order.estimate_expiry_date && order.order_type !== 'PACKING_SLIP') {%>
                                    <div style="display: flex; font-size:12px; font-weight: 700;line-height: 20px">
                                        <div style="width: 150px; color: #939393;">Expiry Date</div>
                                        <div style="width: 150px;">
                                            <%= new Date(order.estimate_expiry_date).toDateString().substring(4); %>
                                        </div>
                                    </div>
                                <%}%>
                                <div>
                                    <%- await render_custom_fields(order.custom_fields,'SECTION-A', order.order_type
                                        ,order.org_id,`<div style="display: flex; font-size:12px; font-weight: 700;line-height: 20px">
                                            <div style="width: 150px; color: #939393;">{{field_name}}</div>
                                            <div style="width: 150px;">
                                                {{field_value}}
                                            </div>
                                        </div>`)%>
                                </div>
                                <% if (order?.shopify_payment_modes?.length) {%>
                                    <div style="display: flex; font-size:12px; font-weight: 700;line-height: 20px">
                                        <div style="width: 150px; color: #939393;">Payment Mode</div>
                                        <div style="width: 150px;">
                                            <%= order?.shopify_payment_modes?.[0]?.replaceAll("_"," ")?.toProperCase()%>
                                        </div>
                                    </div>
                                    <div class=" lv_table_row">
                                                <div class="lv_table_label"></div>
                                                <div class="lv_table_value"></div>
                                        </div>
                                        <% }%>
                                        <% if(order.document_tenant_configuration?.print_payment_terms == true || order.document_tenant_configuration?.print_payment_terms == 'true') {%>
                                            <% if (order.payment_terms && !order.shopify_order_id) { %>
                                                <%order.payment_terms.forEach(function(payment){%>
                                                    <div
                                                        style="display: flex; font-size:12px; font-weight: 700;line-height: 20px">
                                                        <div style="width: 150px; color: #939393;">Payment Terms</div>
                                                        <div style="width: 150px;">
                                                            <%if(payment.due_days){%>
                                                                <%= `${payment.due_days} days Credit` %>
                                                            <%} else {%>
                                                                On Delivery
                                                            <%}%>
                                                        </div>
                                                    </div>
                                                    <%})%>
                                            <%}%>
                                        <%}%>
                                        <% if(order.document_tenant_configuration?.print_payment_remarks == true || order.document_tenant_configuration?.print_payment_remarks == 'true') {%>
                                            <% if (order.payment_terms && !order.shopify_order_id) { %>
                                                <%order.payment_terms.forEach(function(payment){%>
                                                    <%if(payment.remark){%>
                                                    <div style="display: flex; font-size:12px; font-weight: 700;line-height: 20px">
                                                        <div style="width: 150px; color: #939393;">Payment Remarks</div>
                                                        <div style="width: 150px;"> <%= payment.remark %> </div>
                                                    </div>
                                                    <%} %>
                                                <%})%>
                                            <%}%>
                                        <%}%>
                            
                                <% if((order?.document_tenant_configuration?.print_sales_person == true || order?.document_tenant_configuration?.print_sales_person == 'true')) {%>                        
                                    <% if((order?.account_manager_id)) {%>
                                        <div style="display: flex; font-size:12px; font-weight: 700;line-height: 20px">
                                            <div style="width: 150px; color: #939393;">Sales Person</div>
                                            <div style="width: 150px;">
                                                <%= order?.account_manager_info?.first_name || "" %> <%= order?.account_manager_info?.last_name || "" %>
                                            </div>
                                        </div>  
                                    <% }%>
                                <% }%>

                                    </div>

                                    <div style="flex: 5; margin-top: -50px; text-align: right;">
                                        <% if (order.tenant_configuration.logo_base_64) { %>
                                            <div style="flex: 1;">
                                                <img style="height:<%=logo_height ? logo_height:50 %>px; object-fit: contain;margin-bottom: 5px;margin-top: 5px;"
                                                    src="<%= order.tenant_configuration.logo_base_64 %>"
                                                    alt="tenant_img">
                                            </div>
                                            <% } else if (order.organisation_info.org_logo) { %>
                                                <div style="flex: 1;">
                                                    <img style="height:<%=logo_height ? logo_height:50 %>px; object-fit: contain;margin-bottom: 5px;margin-top: 5px;"
                                                        src="<%= order.organisation_info.org_logo %>"
                                                        alt="organisation_img">
                                                </div>
                                                <% } else { %>
                                                    <div style="flex: 1; margin-top: 65px;">

                                                    </div>
                                                    <% } %>
                                                        <% var billing_address=order.tenant_billing_address_info || {}%>
                                                            <div
                                                                style="font-size: 10px; text-align: right; font-weight: 600;line-height: 1.3;">
                                                                <h3 style="line-height: 0;">
                                                                    <% if (order?.document_tenant_configuration?.print_organisation_name) { %>
                                                                        <%= order?.organisation_info?.organisation_name %>
                                                                            <% } else { %>
                                                                                <%= order?.tenant_info?.legal_name ||
                                                                            order?.tenant_info?.tenant_name || "" %>
                                                                            <% } %>
                                                                </h3>
                                                                <%=billing_address.address1 || "" %><br />
                                                                    <%=billing_address.city || "" %>, <%=
                                                                            billing_address.state || "" %>
                                                                            <%= billing_address.postal_code || "" %>
                                                                            <br /><br />
                                                                            <% if (order?.tenant_info?.gst_number){ %>
                                                                                <b>GSTIN:</b>
                                                                                <%= order?.tenant_info?.gst_number || "" %>
                                                                                <br />
                                                                            <% } %>
                                                                <% if (order?.tenant_info?.identification_number?.filter(item=>
                                                                item?.is_printable)?.length) { %>
                                                                    <% order?.tenant_info?.identification_number?.filter(item=>
                                                                    item?.is_printable)?.map(item => { %>
                                                                    <div style="font-size: 10px;margin-top: 5px;">
                                                                        <b>
                                                                        <%=item?.identification_name %>:
                                                                        </b>
                                                                        <%=item?.identification_number || "" %>
                                                                    </div>
                                                                    <% })%>
                                                                <% }%>
                                                            </div>
                                                            <% if(order.download_button_url) { %>
                                                                <div style="margin-top: 20px; margin-left: 100px">
                                                                    <a class="link"
                                                                        style="text-decoration:none;padding:8px 15px;margin-top: 100px;border:none;border-radius:5px;font-weight:400;color:white;background-color: <%= order.tenant_configuration.doc_primary_colour%>;"
                                                                        href="<%=order.download_button_url%>"
                                                                        target="_blank">Download
                                                                    </a>
                                                                </div>
                                                                <% }%>
                                    </div>
                            </div>
                            <% if(order.order_type !== 'PACKING_SLIP') { %>
                            <div style="margin-top: 20px; display: flex; width: 100%; margin-bottom: 30px">
                                <% if(order.order_type === 'SALES_ORDER' || (order.order_type === 'SALES_ESTIMATE' && order.document_tenant_configuration?.print_billing_address)) { %>
                                <div style="width: 99%; margin-right: 1%;">
                                    <div
                                        style="background-color: <%= order.tenant_configuration.doc_secondary_colour%>; padding: 15px; border-radius: 10px; height: 90% !important">
                                        <div
                                            style="font-size: 16px; color: <%= order.tenant_configuration.doc_primary_colour%>; font-weight: bold">
                                            Bill To</div>
                                        <div style="line-height: 18px; margin-top: 5px; font-size: 11px">
                                            <%=order?.customer_info?.customer_name || order?.customer_info?.legal_name
                                                || order?.customer_info?.customer_info?.customer_name ||
                                                order?.customer_info?.alias_name || "" %> <br />
                                                <%= order?.billing_address_info?.address1%> <br />
                                                    <%= order?.billing_address_info?.city%>, <%=
                                                            order?.billing_address_info?.state%>
                                                            <%= order?.billing_address_info?.postal_code%><br />

                                                                <% if (order?.customer_info?.gst_number) { %>
                                                                    <b>GSTIN:</b>
                                                                    <%= order?.customer_info?.gst_number %> <br />
                                                                        <% } %>

                                                                            <% if (order?.customer_info?.email_address
                                                                                ||
                                                                                order?.customer_info?.customer_info?.email_address)
                                                                                { %>
                                                                                <b>Email:</b>
                                                                                <%= order?.customer_info?.email_address
                                                                                    ||
                                                                                    order?.customer_info?.customer_info?.email_address
                                                                                    %> <br />
                                                                                    <% } %>

                                                                                        <% if
                                                                                            (order?.customer_info?.phone_number
                                                                                            ||
                                                                                            order?.customer_info?.customer_info?.phone_number)
                                                                                            { %>
                                                                                            <b>Phone:</b>
                                                                                            <%= order?.customer_info?.phone_number
                                                                                                ||
                                                                                                order?.customer_info?.customer_info?.phone_number
                                                                                                %> <br />
                                                                                                <% } %>

                                        </div>
                                    </div>
                                </div>
                                <% } %>
                                <% if(order.order_type === 'SALES_ORDER' || (order.order_type === 'SALES_ESTIMATE' && order?.document_tenant_configuration?.print_shipping_address)) { %>
                                <div style="width: 99%; margin-left: 1%;">
                                    <div
                                        style="background-color: <%= order.tenant_configuration.doc_secondary_colour%>; padding: 15px; border-radius: 10px; height: 90% !important">
                                        <div
                                            style="font-size: 16px; color: <%= order.tenant_configuration.doc_primary_colour%>; font-weight: bold">
                                            Ship To</div>
                                        <div style="line-height: 18px; margin-top: 5px; font-size: 11px">
                                            <% if(order?.document_tenant_configuration?.print_party_name==true
                                                ||order?.document_tenant_configuration?.print_party_name=='true' ) {%>
                                            <%= order?.customer_info?.customer_name
                                                || order?.customer_info?.legal_name || order?.customer_info?.customer_info?.customer_name ||
                                                order?.customer_info?.alias_name || "" %> <br />
                                                <% } %>
                                                <%= order?.shipping_address_info?.address1%> <br />
                                                    <%= order?.shipping_address_info?.city%>, <%=
                                                            order?.shipping_address_info?.state%>
                                                            <%= order?.shipping_address_info?.postal_code%><br />
                                        </div>
                                    </div>
                                </div>
                                <% } %>
                            </div>
                            <% } %>

                            <table
                                style="width: 100%; font-size: 11px; border-radius: 5px 5px 0 0; border-collapse: collapse">
                                <thead style="border: 1px solid <%= order.tenant_configuration.doc_primary_colour%>">
                                    <tr
                                        style="background-color: <%= order.tenant_configuration.doc_primary_colour%>; color: #ffffff; text-align: left; font-weight: bold;">
                                        <th style="padding: 12px 5px; white-space: nowrap; width: 10px"> S No.
                                        </th>
                                        <th style="padding: 12px 5px; white-space: nowrap">
                                            Item & Description
                                        </th>
                                        <th style="padding: 12px 5px; white-space: nowrap">
                                            HSN/SAC
                                        </th>
                                        <th style="padding: 12px 5px; white-space: nowrap">
                                            Qty
                                        </th>
                                        <th style="padding: 12px 5px; white-space: nowrap">
                                            Unit
                                        </th>
                                        <% if(order.order_type !== 'PACKING_SLIP') { %>
                                        <th style="padding: 12px 5px; white-space: nowrap">
                                            Rate
                                        </th>
                                        <% } %>
                                        <% if (uniqueFields?.length > 0) { %>
                                            <% uniqueFields.forEach(field => { %>
                                                <th style="padding: 12px 5px; white-space: nowrap"><%= field.field_name %></th>
                                            <% }) %>
                                        <% } %>
                                        <% if(order.order_type !== 'PACKING_SLIP') { %>
                                        <% if(order?.order_discount !== 0) {%>
                                            <th style="padding: 12px 5px; white-space: nowrap">
                                                Discount
                                            </th>
                                        <% }%>
                                        <th style="padding: 12px 5px; white-space: nowrap">
                                            Tax
                                        </th>
                                        <th style="padding: 2px 5px; white-space: nowrap">
                                            Item Total
                                        </th>
                                        <% } %>


                                    </tr>
                                </thead>
                                <tbody>
                                    <% var sNo=0; %>
                                        <% order.sales_order_lines.forEach(function(product) { %>
                                            <% sNo +=1; %>
                                                <tr>
                                                    <td
                                                        style="padding: 2px 3px; border: 1px solid #d7d7d7; text-align: center; height:50px;">
                                                        <%= sNo %>
                                                    </td>
                                                    <td style=" padding: 5px; border: 1px solid #d7d7d7">
                                                        <div style="width: 170px;">
                                                            <% if((order?.document_tenant_configuration?.print_item_code==true
                                                            ||order?.document_tenant_configuration?.print_item_code=='true' ) &&
                                                            product?.product_sku_info?.ref_product_code) {%>
                                                                <div style="font-size: 10px;  font-weight: 500; margin-bottom: 2px;">
                                                              <%= product?.product_sku_info?.ref_product_code %>
                                                            </div>
                                                            <% } %>
                                                              <% if((order?.document_tenant_configuration?.print_internal_sku_code==true
                                                                ||order?.document_tenant_configuration?.print_internal_sku_code=='true' ) &&
                                                                product?.product_sku_info?.internal_sku_code) {%>
                                                                <div style="font-size: 10px;  font-weight: 500; margin-bottom: 2px;">
                                                                  <%= product?.product_sku_info?.internal_sku_code %>
                                                                </div>
                                                                <% } %>
                                                            <div style="font-weight: bold;padding-bottom: 2px;"><%= product.product_sku_name %></div>
                                                            <% if( product?.product_sku_info?.product_category_info ) { %>
                                                                <% if((order?.document_tenant_configuration?.print_category_status =='Child Category' )) {%>
                                                                    <div><span style="font-weight:bold ;">Category - </span> <%= product?.product_sku_info?.product_category_info?.product_category_name %></div>
                                                                <% } %>
                                                                <% if((order?.document_tenant_configuration?.print_category_status =='Parent Category' )) {%>
                                                                    <div><span style="font-weight: bold;">Category - </span>  <%= product?.product_sku_info?.product_category_info?.category_path?.[0] %></div>
                                                                <% } %>
                                                                <% if((order?.document_tenant_configuration?.print_category_status =='Full Category Path' )) {%>
                                                                    <div><span style="font-weight:bold ;">Category - </span>  <%= product?.product_sku_info?.product_category_info?.category_path?.join('/') %></div>
                                                                <% } %>
                                                            <% } %>
                                                                <% if (product.remarks ) { %>
                                                                    </br><span
                                                                        style="font-size: 10px; color: <%= order.tenant_configuration.doc_primary_colour%> !important">
                                                                        <%- product.remarks %>
                                                                    </span>
                                                                    <% } else { %>
                                                                        <% } %>
                                                                        <% if(order.order_type === 'PACKING_SLIP') { %>
                                                                        <% product.fl_product_batches=product?.bundle_products?.length===1 ?
                                                                        product?.bundle_products?.[0]?.fl_product_batches :
                                                                        product?.fl_product_batches %>
                                                                        <% if ((product?.product_batches?.length ||
                                                                            product?.fl_product_batches?.length)) { %>
                                                                            <div class="primary_color"
                                                                                style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                                                <% if(product?.product_batches?.length) { %>
                                                                                    <% product?.product_batches.forEach(function(pbtch) { %>
                                                                                        <div class="primary_color"
                                                                                            style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                                                            <% if (pbtch?.custom_batch_number) { %>
                                                                                                Batch #<%= pbtch?.custom_batch_number %> </br>
                                                                                            <% } %>
                                                                                            <% if(pbtch?.lot_number){ %>
                                                                                                Lot #<%= pbtch?.lot_number %> </br>
                                                                                            <% } %>
                                                                                            <% if(pbtch?.expiry_date && (pbtch?.expiry_date< '2099-01-01' )){ %>
                                                                                                Expiry <%= new Date(pbtch?.expiry_date).customFormat(product?.product_sku_info?.expiry_date_format) || 'NA' %></br>
                                                                                            <% } %>
                                                                                            <% if(pbtch?.manufacturing_date) { %>
                                                                                                Mfg Date <%= new Date(pbtch?.manufacturing_date).customFormat(product?.product_sku_info?.manufacturing_date_format) || 'NA' %><br>
                                                                                            <%}%>
                                                                                                Quantity <%= toFixedQuantity(pbtch.quantity, product?.uom_info?.[0]?.precision) %> <%= product?.uom_info?.[0]?.uqc?.toProperCase()|| 'Nos' %></br>
                                                                                                <% if (pbtch?.custom_fields?.length > 0) { %>
                                                                                                    <% let batchCustomFields = pbtch?.custom_fields; %>
                                                                                                    <% batchCustomFields?.forEach(field => { %>
                                                                                                        <% if((field?.is_printable ==true || field?.is_printable=='true') &&(field?.is_active ==true || field?.is_active=='true') ) { %> 
                                                                                                        <div style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                                                                            <%= field?.field_name %> - <%= field?.field_value %>
                                                                                                        </div>
                                                                                                        <%} %>
                                                                                                    <% }); %>
                                                                                                <% } %>                           
                                                                                        </div>
                                                                                        <% }) %>
                                                                                            <% } else if(product?.fl_product_batches?.length) {%>
                            
                                                                                                <% product?.fl_product_batches.forEach(function(pbtch)
                                                                                                    { %>
                                                                                                    <div class="primary_color"
                                                                                                        style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                                                                        <% if (pbtch?.custom_batch_number) { %>
                                                                                                            Batch #<%= pbtch?.custom_batch_number %></br>
                                                                                                        <% } %>
                                                                                                        <% if(pbtch?.lot_number){ %>
                                                                                                            Lot #<%= pbtch?.lot_number%> </br>
                                                                                                       <% } %>
                                                                                                        <% if(pbtch?.expiry_date && (pbtch?.expiry_date < '2099-01-01' )){ %>
                                                                                                            Expiry <%= new Date(pbtch?.expiry_date).customFormat(product?.product_sku_info?.expiry_date_format) || 'NA' %></br>
                                                                                                        <% } %>
                                                                                                        <% if(pbtch?.manufacturing_date) { %>
                                                                                                             Manufacturing Date <%= new Date(pbtch?.manufacturing_date).customFormat(product?.product_sku_info?.manufacturing_date_format) || 'NA' %><br>
                                                                                                        <%}%>
                                                                                                            Quantity <%= toFixedQuantity(pbtch.quantity, product?.uom_info?.[0]?.precision) %> <%= product?.uom_info?.[0]?.uqc?.toProperCase()|| 'Nos' %> <%= product?.uom_info?.[0]?.uqc?.toProperCase() || 'Nos'%>
                                                                                                            </br>
                                                                                                        <% if (pbtch?.custom_fields?.length > 0) { %>
                                                                                                            <% let batchCustomFields = pbtch?.custom_fields; %>
                                                                                                            <% batchCustomFields?.forEach(field => { %>
                                                                                                                <% if((field?.is_printable ==true || field?.is_printable=='true') &&(field?.is_active ==true || field?.is_active=='true') ) { %> 
                                                                                                                <div style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                                                                                    <%= field?.field_name %> - <%= field?.field_value %>
                                                                                                                </div>
                                                                                                                <%} %>
                                                                                                            <% }); %>
                                                                                                        <% } %> 
                                                                                                    </div>
                                                                                                    <% }) %>
                                                                                                        <% } %>
                            
                                                                            </div>
                                                                            <% } %>
                                                                            <%}%>
                                                                            

                                                                            <% if(order.order_type === 'SALES_ORDER' && order.print_reserved_batches && product?.batch_reservations?.reserved_batches?.length) { %>
                                                                            <div class="primary_color"
                                                                                style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                                                    <% product?.batch_reservations?.reserved_batches?.forEach(function(pbtch) { %>
                                                                                        <div class="primary_color"
                                                                                            style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                                                            <% if (pbtch?.custom_batch_number) { %>
                                                                                                Batch: #<%= pbtch?.custom_batch_number %> </br>
                                                                                            <% } %>
                                                                                            <% if(pbtch?.lot_number){ %>
                                                                                                Lot: #<%= pbtch?.lot_number %> </br>
                                                                                            <% } %>
                                                                                            <% if(pbtch?.inventory_location_path){ %>
                                                                                                Location: <%= pbtch?.inventory_location_path %> </br>
                                                                                            <% } %>
                                                                                            <% if(pbtch?.expiry_date && (pbtch?.expiry_date< '2099-01-01' )){ %>
                                                                                                Expiry: <%= new Date(pbtch?.expiry_date).customFormat(product?.product_sku_info?.expiry_date_format) || 'NA' %></br>
                                                                                            <% } %>
                                                                                            <% if(pbtch?.manufacturing_date) { %>
                                                                                                Mfg Date: <%= new Date(pbtch?.manufacturing_date).customFormat(product?.product_sku_info?.manufacturing_date_format) || 'NA' %><br>
                                                                                            <%}%>
                                                                                                Reserved Quantity: <%= toFixedQuantity(pbtch.reserved_qty, product?.uom_info?.[0]?.precision) %> <%= product?.uom_info?.[0]?.uqc?.toProperCase()|| 'Nos' %></br>
                                                                                            
                                                                                            <% if(pbtch?.brand){ %>
                                                                                                Brand: <%= pbtch?.brand %> </br>
                                                                                            <% } %>
                                                                                            <% if(pbtch?.mfg_batch_no){ %>
                                                                                                Mfg No: <%= pbtch?.mfg_batch_no %> </br>
                                                                                            <% } %>
                                                                                            <% if(pbtch?.seller_name){ %>
                                                                                                Vendor: <%= pbtch?.seller_name %> </br>
                                                                                            <% } %>

                                                                                                <% if (pbtch?.custom_fields?.length > 0) { %>
                                                                                                    <% let batchCustomFields = pbtch?.custom_fields; %>
                                                                                                    <% batchCustomFields?.forEach(field => { %>
                                                                                                        <% if((field?.is_printable ==true || field?.is_printable=='true') &&(field?.is_active ==true || field?.is_active=='true') ) { %>
                                                                                                            <%= field?.field_name %>: <%= field?.field_value %>
                                                                                                        <%} %>
                                                                                                    <% }); %>
                                                                                                <% } %>                           
                                                                                        </div>
                                                                                        <% }) %>                            
                                                                            </div>
                                                                            <%}%>
                                                        </div>
                                                    </td>

                                                    <td style="padding: 2px 5px; border: 1px solid #d7d7d7">
                                                        <%= product?.hsn_code || product.product_sku_info?.hsn_code %>
                                                    </td>

                                                    <td style="padding: 2px 5px; border: 1px solid #d7d7d7">
                                                            <div>
                                                                <%= toFixedQuantity(product.quantity, product?.uom_info?.[0]?.precision) %>
                                                            </div>
                                                            <%if(product.free_quantity){%>
                                                                <div>
                                                                    <span class="primary_color" style="font-size: 10px;"><%=`+ ${product.free_quantity} Free`%></span>
                                                                </div>
                                                            <%}%>
                                                    </td>

                                                    <td style="padding: 2px 5px; border: 1px solid #d7d7d7">
                                                        <%= product?.uom_info?.[0].uqc || 'Nos' %>
                                                    </td>
                                                    <% if(order.order_type !== 'PACKING_SLIP') { %>
                                                    <td style="padding: 2px 5px; border: 1px solid #d7d7d7;">
                                                        <%= applyUserPolicy(product.unit_price, order?.currency_code, order?.organisation_info?.global_config?.settings?.price_precision, order?.hide_selling_price) %>
                                                    </td>
                                                    <% } %>


                                                    <% if (product?.so_line_custom_fields?.length > 0 && uniqueFields?.length > 0) { %>
                                                        <% let ilCustomFields = product.so_line_custom_fields; %>
                                                        <% uniqueFields.forEach(field => { %>
                                                            <% let matchingRow = ilCustomFields.find(row => row.cf_id === field.cf_id); %>
                                                            <% if(matchingRow?.field_type == 'DATE' && matchingRow?.field_value?.includes('T')){ %>
                                                                <td style="padding: 2px 5px; border: 1px solid #d7d7d7;"><%= matchingRow?.field_value?.split('T')?.[0] || '-'  %></td>
                                                              <% } else {%>
                                                                <td style="padding: 2px 5px; border: 1px solid #d7d7d7;"><%= matchingRow?.field_value || '-' %></td>
                                                              <%} %>
                                                        <% }); %>
                                                    <% } %>

                                                    <% if(order.order_type !== 'PACKING_SLIP') { %>
                                                    <% if(order?.order_discount !== 0) {%>
                                                    <td style="padding: 2px 5px; border: 1px solid #d7d7d7;">
                                                        <%= Number.parseFloat(Number(product?.line_discount_percentage).toFixed(2)) || 0 %>%
                                                    </td>
                                                        <% }%>

                                                    <td style="padding: 2px 3px; border: 1px solid #d7d7d7;">
                                                        <%= product.tax_info.tax_value %>%
                                                    </td>

                                                    <td style="padding: 2px 4px; border: 1px solid #d7d7d7">
                                                        <%= applyUserPolicy(product.line_total_price, order?.currency_code, order?.organisation_info?.global_config?.settings?.price_precision, order?.hide_selling_price) %>
                                                    </td>
                                                    <% } %>
                                                </tr>
                                                <% if(order?.document_tenant_configuration?.print_bundled_products) {%>
                                                    <% (product?.bundle_products||[]).forEach(function(child_product,child_product_idx)
                                                        { %>
                                                        <tr>
                                                            <td
                                                                style="padding: 2px 3px; border: 1px solid #d7d7d7; text-align: center; height:50px;">
                                                                <%= `${sNo}.${child_product_idx+1}`%>
                                                            </td>
                                                            <td style="padding: 2px 5px; border: 1px solid #d7d7d7">
                                                                <%= child_product.product_sku_info.ref_product_code %>
                                                            </td>
                                                            <td style=" padding: 5px; border: 1px solid #d7d7d7">
                                                                <div style="width: 170px;">
                                                                    <%= child_product.product_sku_name %>
                                                                    <% if(order.order_type === 'PACKING_SLIP') { %>
                                                                    <% if (child_product?.product_batches?.length) { %>
                                            <div class="primary_color"
                                                style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                <% child_product?.product_batches.forEach(function(pbtch) { %>
                                                    <div class="primary_color"
                                                        style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                        <% if (pbtch?.custom_batch_number) { %>
                                                            Batch #<%= pbtch?.custom_batch_number %> </br>
                                                                <% } %>
                                                                    <% if(pbtch?.lot_number){ %>
                                                                        Lot #<%= pbtch?.lot_number %> </br>
                                                                            <% } %>
                                                                                <% if(pbtch?.expiry_date &&
                                                                                    (pbtch?.expiry_date < '2099-01-01'
                                                                                    )){ %>
                                                                                    Expiry <%= new Date(pbtch?.expiry_date).customFormat(child_product?.product_sku_info?.expiry_date_format) || 'NA' %>
                                                                                        </br>
                                                                                        <% } %>
                                                                                            Quantity <%=
                                                                                                pbtch.quantity%>
                                                                                                <%= child_product?.uom_info?.[0]?.uqc?.toProperCase()
                                                                                                    || 'Nos' %>
                                                                                                    </br>
                                                    </div>
                                                    <% }) %>
                                            </div>
                                            <% } %>
                                                <% if (!child_product?.product_batches?.length &&
                                                    child_product?.fl_product_batches?.length
                                                    ) { %>
                                                    <div class="primary_color"
                                                        style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                        <% child_product?.fl_product_batches.forEach(function(pbtch) {
                                                            %>
                                                            <div class="primary_color"
                                                                style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                                <% if (pbtch?.custom_batch_number) { %>
                                                                    Batch #<%= pbtch?.custom_batch_number %> </br>
                                                                        <% } %>
                                                                            <% if(pbtch?.lot_number){ %>
                                                                                Lot #<%= pbtch?.lot_number %> </br>
                                                                                    <% } %>
                                                                                        <% if(pbtch?.expiry_date &&
                                                                                            (pbtch?.expiry_date
                                                                                            < '2099-01-01' )){ %>
                                                                                            Expiry <%= new Date(pbtch?.expiry_date).customFormat(child_product?.product_sku_info?.expiry_date_format) || 'NA' %>
                                                                                                </br>
                                                                                                <% } %>
                                                                                                    Quantity <%=
                                                                                                        pbtch.quantity%>
                                                                                                        <%= child_product?.uom_info?.[0]?.uqc?.toProperCase()
                                                                                                            || 'Nos' %>
                                                                                                            </br>
                                                            </div>
                                                            <% }) %>
                                                    </div>
                                                    <% } %>
                                                    <%}%>
                                                                </div>
                                                            </td>

                                                            <td style="padding: 2px 5px; border: 1px solid #d7d7d7">
                                                                <%= child_product?.hsn_code || child_product.product_sku_info?.hsn_code %>
                                                            </td>

                                                            <td style="padding: 2px 5px; border: 1px solid #d7d7d7">
                                                                <%= toFixedQuantity(child_product.quantity,
                                                                    child_product?.uom_info?.[0]?.precision) %>
                                                            </td>

                                                            <td style="padding: 2px 5px; border: 1px solid #d7d7d7">
                                                                <%= child_product?.uom_info?.[0].uqc || 'Nos' %>
                                                            </td>

                                                            <td style="padding: 2px 5px; border: 1px solid #d7d7d7;">
                                                            </td>
                                                            <td style="padding: 2px 5px; border: 1px solid #d7d7d7;">
                                                            </td>

                                                            <td style="padding: 2px 3px; border: 1px solid #d7d7d7;">
                                                            </td>

                                                            <td style="padding: 2px 4px; border: 1px solid #d7d7d7">
                                                            </td>
                                                        </tr>
                                                        <% }) %>
                                                            <%}%>
                                                                <% }) %>
                                                                
                                                                
                                </tbody>
                            </table>
                            <div style="display: flex;page-break-inside: avoid">
                                <% if(order.order_type !== 'PACKING_SLIP') { %>
                                <div style="margin-top: 25px;">
                                    <% if(order.document_tenant_configuration?.print_total_quantity && order.order_type === 'SALES_ORDER'){ %>
                                    <div style="width: 100%; font-size: 10px; margin-bottom: 5px;">
                                        Total Quantity: 
                                        <b style="font-size: 10px;">
                                          <%
                                          var totalQty =  order?.sales_order_lines?.reduce((total, item) => {
                                            if (item.is_bundle && item.bundle_products?.length && !order.calculate_total_quantity_with_parent_quantity_for_bundle_products) {
                                              const bundleTotal = item.bundle_products.reduce((bundleTotal, bundleItem) => {
                                                return bundleTotal + (bundleItem.quantity || 0);
                                              }, 0);
                                              return total + bundleTotal;
                                            }
                                            return total + (item.quantity || 0) + (item.free_quantity || 0);
                                          }, 0) %>
                                          <%= toFixedQuantity(totalQty, 2) %>
                                        </b>
                                      </div>
                                      <%}%>
                                    <div style="width: 100%; font-size: 10px;">
                                        Total In Words: <b style="font-size: 10px;">
                                            <% if(order?.hide_selling_price == true || order?.hide_selling_price == 'true') { %>
                                                XXXXXX
                                            <%} else { %>
                                            <%= order.order_total_in_words %>
                                            <% } %>
                                        </b>
                                    </div>
                                    <% if(order.remarks){ %>
                                        <div style="width: 90%; font-size: 12px;margin-top: 5px;">
                                            Remarks : <b>
                                                <%= order.remarks %>
                                            </b>
                                        </div>
                                        <% } %>
                                            <%- await
                                                render_custom_fields(order.custom_fields,'SECTION-B', 'SALES_ORDER'
                                                ,order.org_id, ` <div
                                                style="width: 90%; font-size: 12px;margin-top: 5px;">
                                                {{field_name}} : <b>{{field_value}}</b>
                                </div>
                                `)%>
                                <div>
                                    <% if (order.document_tenant_configuration?.print_bank_details) { %>
                                        <% if (order?.bank_details) { %>
                                            <div style="margin-top: 25px;" class="div-to-next-page">
                                                <div>
                                                    <p style="font-size: 11px; font-weight: bold; color: <%= order.tenant_configuration.doc_primary_colour %>;">
                                                        Bank Details
                                                    </p>
                                                    <pre style="font-family: Arial; margin-top: 2px; line-height: 13px; font-size: 10px;"><%= order?.bank_details %></pre>
                                                </div>
                                            </div>
                                        <% } %>
                                    
                                        <% if (order?.qr_code) { %>
                                            <div style="margin-top: 10px;">
                                                <img src="<%= order?.qr_code %>" alt="QR Code" style="width: 100px; height: 100px;">
                                            </div>
                                        <% } %>
                                    <% } %>
                                    
                                </div>
                                <% } %>
                                <div>
                                    <% if(order.terms_and_conditions){ %>
                                        <div style="margin-top: 25px;" class="div-to-next-page">
                                            <div>
                                                <p
                                                    style="font-size: 14px; font-weight: bold; color: <%= order.tenant_configuration.doc_primary_colour%>;">
                                                    Terms and Conditions</p>
                                                <ol class="move-to-next-page"
                                                    style="line-height: 16px; font-size: 10px;list-style-type: none; padding-left: 0px; width:95%">
                                                    <% order.terms_and_conditions.split("\n").forEach(i=> {%>
                                                        <li>
                                                            <pre style="white-space: pre-wrap;font-family: Arial;"><%- i %></pre>
                                                        </li>
                                                        <%}) %>
                                                </ol>
                                            </div>
                                        </div>
                                        <% } %>
                                </div>
                            </div>
                            <% if(order.order_type !== 'PACKING_SLIP') { %>
                            <div style='margin-left: auto; line-height: 18px;  padding-top: 20px; font-size: 11px'
                                class="div-to-next-page">
                                <table style="padding: 0; border-collapse: collapse;">
                                    <tbody>

                                        <tr>
                                            <td style="white-space: nowrap; padding-right: 10px"><b>Sub Total</b></td>
                                            <td style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                <%= applyUserPolicy(order?.order_base_price, order?.currency_code, order?.organisation_info?.global_config?.settings?.price_precision, order?.hide_selling_price) %>
                                            </td>
                                        </tr>

                                        <tr>
                                            <td style="white-space: nowrap; padding-right: 10px"><b>Discount </b></td>
                                            <td style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                <%= applyUserPolicy(order?.order_discount, order?.currency_code, order?.organisation_info?.global_config?.settings?.price_precision, order?.hide_selling_price) %>
                                            </td>
                                        </tr>
                                        <%var order_charges=order.other_charges ?? []%>
                                        <% order_charges.forEach(function(charges) { %>
                                            <% if(charges?.tax_info) { %>
                                            <tr>
                                                <td style="white-space: nowrap; padding-right: 10px"><b>
                                                        <%= charges.charge_name%>
                                                    </b></td>
                                                <td
                                                    style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                    <%=new Intl.NumberFormat('en-IN', { style: 'currency' ,
                                                        currency: order?.currency_code
                                                        }).format(charges.charge_amount) %>
                                                </td>
                                            </tr>
                                            <% } %>
                                            <%})%>
                                        <%order.tax_info.forEach(function(tax){%>
                                            <tr>
                                                <td style="white-space: nowrap; padding-right: 10px"><b>
                                                        <%= tax.tax_type_name %>
                                                    </b></td>
                                                <td style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                <%= applyUserPolicy(tax?.tax_amount, order?.currency_code, order?.organisation_info?.global_config?.settings?.price_precision, order?.hide_selling_price) %>
                                                </td>
                                            </tr>
                                            <%})%>
                                                <%var order_charges=order.other_charges ?? []%>
                                                    <% order_charges.forEach(function(charges) { %>
                                                        <% if(!charges?.tax_info) { %>
                                                        <tr>
                                                            <td style="white-space: nowrap; padding-right: 10px"><b>
                                                                    <%= charges.charge_name%>
                                                                </b></td>
                                                            <td
                                                                style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                                <%=new Intl.NumberFormat('en-IN', { style: 'currency' ,
                                                                    currency: order?.currency_code
                                                                    }).format(charges.charge_amount) %>
                                                            </td>
                                                        </tr>
                                                        <% } %>
                                                        <%})%>
                                                            <% if (order.charge_1_name) { %>
                                                                <tr>
                                                                    <td
                                                                        style="white-space: nowrap; padding-right: 10px">
                                                                        <b>
                                                                            <%= order.charge_1_name %>
                                                                        </b>
                                                                    </td>
                                                                    <td
                                                                        style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                                        <%=new Intl.NumberFormat('en-IN', {
                                                                            style: 'currency' , currency:
                                                                            order?.currency_code
                                                                            }).format(order.charge_1_value) %>
                                                                    </td>
                                                                </tr>
                                                                <% } %>

                                                                    <% if (order.charge_2_name) { %>
                                                                        <tr>
                                                                            <td
                                                                                style="white-space: nowrap; padding-right: 10px">
                                                                                <b>
                                                                                    <%= order.charge_2_name %>:
                                                                                </b>
                                                                            </td>
                                                                            <td
                                                                                style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                                                <%= new Intl.NumberFormat('en-IN', {
                                                                                    style: 'currency' , currency:
                                                                                    order?.currency_code
                                                                                    }).format(order.charge_2_value) %>
                                                                            </td>
                                                                        </tr>
                                                                        <% } %>

                                                                            <tr>
                                                                                <td
                                                                                    style="white-space: nowrap; padding-right: 10px">
                                                                                    <b>Round Off</b>
                                                                                </td>
                                                                                <td
                                                                                    style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                                                    <%= new Intl.NumberFormat('en-IN', {
                                                                                        style: 'currency' , currency:
                                                                                        order?.currency_code
                                                                                        }).format(order.order_round_off)%>
                                                                                </td>
                                                                            </tr>

                                                                            <tr>
                                                                                <td
                                                                                    style="white-space: nowrap; border-top: 1px solid black;">
                                                                                    <b>Grand Total </b>
                                                                                </td>
                                                                                <td
                                                                                    style="white-space: nowrap; border-top: 1px solid black; padding-left: 10px; text-align: right">
                                                                                         <%= applyUserPolicy((order.order_grand_total + order.order_round_off), order?.currency_code, order?.organisation_info?.global_config?.settings?.price_precision, order?.hide_selling_price) %>
                                                                                </td>
                                                                            </tr>

                                    </tbody>
                                </table>
                                <%if(order?.document_tenant_configuration?.print_signature_input){%>
                                    <div style=" width: 100%; margin-top:10px;padding:0px 7px 5px 7px; border-radius: 4px; display:flex;flex-direction:column;align-items:center;background-color: <%= order.tenant_configuration.doc_secondary_colour%>;page-break-inside:avoid;">
                                        <div style="text-align: center;padding-top: 5px;font-size: 10px;"><%= order?.document_tenant_configuration?.signature_name||'' %></div>
                                        <b>
                                            <div style="height:36px;">
                                                <%if(order?.document_tenant_configuration?.doc_sign_base_64){%>
                          
                                                        <img style="height:30px; width:150px; object-fit: contain; margin-bottom:2px;"
                                                             src="<%= order.document_tenant_configuration?.doc_sign_base_64 %>"
                                                             alt="signature_img">
                                                <% } %>
                                            </div>
                                        </b>
                                        <div style="font-size: 10px;">Authorized Signatory</div>
                                    </div>
                                <%}%>
                            </div>
                            <% } %>


                        </div>
                        <div style="display: flex;margin-top: 20px;flex-wrap: wrap;" class="div-to-next-page">
                            <% if(order?.document_tenant_configuration?.print_product_images) {%>
                                <% let image_size=85 %>
                                    <% (order?.sales_order_lines||[]).forEach((product,_idx)=> { %>
                                        <% if (product?.product_sku_info?.assets?.[0]?.url) {%>
                                            <div
                                                style="position: relative;border: 1px solid grey;height: <%=image_size%>px; width:  <%=image_size%>px;margin: 2px;">
                                                <p style="position: absolute;bottom: -37px;left: 27px;">
                                                    <%= `${_idx+1}`%>
                                                </p><img src="<%= product?.product_sku_info?.assets?.[0]?.url %>"
                                                    width="<%=image_size%>" height="<%=image_size%>">
                                            </div>
                                            <% } %>
                                                <% (product?.bundle_products||[]).forEach((child_product,_idx_2)=> { %>
                                                    <div
                                                        style="position: relative;border: 1px solid grey;height: <%=image_size%>px; width:  <%=image_size%>px;margin: 2px;">
                                                        <p style="position: absolute;bottom: -37px;left: 27px;">
                                                            <%= `${_idx+1}.${_idx_2+1}`%>
                                                        </p><img
                                                            src="<%= child_product?.product_sku_info?.assets?.[0]?.url %>"
                                                            width="<%=image_size%>" height="<%=image_size%>">
                                                    </div>
                                                    <% }); %>
                                                        <% }); %>
                        </div>
                        <% } %>
                </div>
        </div>
</body>

</html>
