<div style="font-weight: 600;"
    class="document_page <%= (beautify==='true' || beautify===true) ? 'document_page_beautify' : ''%>">
    <% let uniqueFields = collectUniqueFields(invoice.invoice_lines, 'invoice_line_custom_fields') %>
    
    <div style="flex: 3; text-align: right; line-height: 15px; font-weight: normal;">
        <% if(invoice?.document_tenant_configuration?.print_prepared_by == true || invoice?.document_tenant_configuration?.print_prepared_by == 'true') {%>
          <div style="text-align: right; font-size: 10px">
            <% if(invoice?.created_by) {%>
              Prepared By: <%= invoice?.created_by_info?.first_name + invoice?.created_by_info?.last_name%>
            <%} %>
          </div>
        <%} %>
      
        <% if(invoice?.document_tenant_configuration?.print_prepared_at == true || invoice?.document_tenant_configuration?.print_prepared_at == 'true') {%>
          <div style="text-align: right; font-size: 10px">
            <% if(invoice?.created_at) {%>
              Prepared At: <%= new Date().formatDateTime(invoice?.created_at) %>
            <%} %>
          </div>
        <%} %>
    </div>

    <!-- title -->
    <div style="font-size: 26px; padding: 20px 0px 20px 0px" class="primary_color">
        <%= invoice?.invoice_type?.replaceAll('_', ' ')?.toProperCase() %> <%- invoice.status==='VOID' ?'<span style="color:red;font-size:14px">(CANCELLED)</span>':"" %>
    </div>

    <!-- SECTION-A -->
    <div style="display: flex;flex-flow: row wrap; justify-content: space-around;">
        <!-- left -->
        <div class="lv_table">
            <div class="lv_table_row">
                <div class="lv_table_label"><%= invoice?.invoice_type?.replaceAll('_', ' ')?.toProperCase() %> #</div>
                <div class="lv_table_value">
                    <%= invoice.invoice_number %>
                </div>
            </div>
            <% if(invoice.document_tenant_configuration?.print_status == true || invoice.document_tenant_configuration?.print_status == 'true') {%>
            <div class="lv_table_row">
                <div class="lv_table_label">Status</div>
                <div class="lv_table_value">
                    <%= invoice.secondary_status?.toProperCase() %>
                </div>
            </div>
            <%}%>
            <div class="lv_table_row">
                <div class="lv_table_label"><%= invoice?.invoice_type?.replaceAll('_', ' ')?.toProperCase() %> Date</div>
                <div class="lv_table_value">
                    <% if(invoice?.invoice_date) { %>
                        <% var invoice_date = new Date(invoice?.invoice_date); %>
                        <% var day = invoice_date?.getDate()?.toString()?.padStart(2, '0'); %>
                        <% var month = (invoice_date?.getMonth() + 1)?.toString()?.padStart(2, '0'); %>
                        <% var year = invoice_date?.getFullYear()?.toString()?.substring(2); %>
                        <%= `${day}-${month}-${year}` %>
                    <%}%>
                </div>
            </div>
        <% if(invoice.document_tenant_configuration?.print_due_date == true || invoice.document_tenant_configuration?.print_due_date == 'true') {%>
            <div class="lv_table_row">
                <div class="lv_table_label">Due Date</div>
                <div class="lv_table_value">
                    <%= new Date(invoice?.invoice_due_date)?.toDateString()?.substring(4); %>
                </div>
            </div>
            <%}%>
            <% if(invoice.document_tenant_configuration?.print_sales_order_number == true || invoice.document_tenant_configuration?.print_sales_order_number == 'true') {%>
                <% if (invoice?.sales_order_number) { %>
                    <div class=" lv_table_row">
                            <div class="lv_table_label">Sales Order No</div>
                            <div class="lv_table_value">
                                <%= invoice?.sales_order_number %>
                            </div>
                    </div>
                <%}%>
            <%}%>
            <% if (invoice?.shopify_payment_modes?.length) {%>
                <div class="lv_table_row">
                    <div class="lv_table_label">Payment Mode</div>
                    <div class="lv_table_value">
                        <%= invoice?.shopify_payment_modes?.[0]?.replaceAll("_"," ")?.toProperCase()%></div>
                </div>
            <% }%>
            <% if(invoice.document_tenant_configuration?.print_payment_terms == true || invoice.document_tenant_configuration?.print_payment_terms == 'true') {%>
            <% if (invoice?.payment_terms?.length && !invoice?.shopify_order_id) { %>
                    <div class=" lv_table_row">
                            <div class="lv_table_label">Payment Terms</div>
                            <div class="lv_table_value">
                                <%= (invoice?.payment_terms?.[0]?.due_days) ? `${invoice?.payment_terms?.[0]?.due_days}
                                    days Credit` : "On Delivery" %>
                            </div>
                    </div>
                    <%}%>
                    <%}%>
            <% if(invoice.document_tenant_configuration?.print_payment_remarks == true || invoice.document_tenant_configuration?.print_payment_remarks == 'true') {%>
                <% if (invoice?.payment_terms?.length && !invoice?.shopify_order_id) { %>
                    <div class=" lv_table_row">
                            <div class="lv_table_label">Payment Remarks</div>
                            <div class="lv_table_value">
                                <%= invoice?.payment_terms?.[0]?.remark %>
                            </div>
                    </div>
                <%}%>
            <%}%>
                    <% if(invoice?.org_currency_info?.currency_code == 'INR'){ %>
                        <div class=" lv_table_row">
                            <div class="lv_table_label">Place of Supply</div>
                            <div class="lv_table_value">
                                <%= invoice?.customer_info?.gst_number?.substring(0,2) %> <% if(invoice?.customer_info?.gst_number){%> - <%}%><%= invoice?.shipping_address_info?.state %>
                            </div>
                    </div>
                    <% } %>
                      <% if(invoice.document_tenant_configuration?.print_date_of_preparation == true || invoice.document_tenant_configuration?.print_date_of_preparation == 'true') {%>
                        <div class=" lv_table_row">
                            <div class="lv_table_label">Date of Preparation</div>
                            <div class="lv_table_value">
                            <% if(invoice.created_at){%>
                            <% var created_at = new Date(invoice?.created_at); %>
                            <% var day = created_at?.getDate()?.toString()?.padStart(2, '0'); %>
                            <% var month = (created_at?.getMonth() + 1)?.toString()?.padStart(2, '0'); %>
                            <% var year = created_at?.getFullYear()?.toString()?.substring(2); %>
                            <%= `${day}-${month}-${year}` %>
                            <%}%>
                            </div>
                    </div>
                        <%} %>
                        <% if ( invoice?.shopify_tracking_number) { %>
                            <div class="lv_table_row">
                                <div class="lv_table_label">Tracking Number</div>
                                <div class="lv_table_value">
                                    <%= invoice?.shopify_tracking_number %>
                                </div>
                            </div>
                            <% } %>
                                <% if ( invoice?.shopify_order_number) { %>
                                    <div class="lv_table_row">
                                        <div class="lv_table_label">Shopify Order #</div>
                                        <div class="lv_table_value">
                                            <%= invoice?.shopify_order_number %>
                                        </div>
                                    </div>
                                    <% } %>
                                        <% if ( invoice?.e_way_bills?.[0]?.ewb_no) { %>
                                            <div class="lv_table_row">
                                                <div class="lv_table_label">e-Way Bill</div>
                                                <div class="lv_table_value">
                                                    <%= invoice?.e_way_bills?.[0]?.ewb_no %>
                                                </div>
                                            </div>
                                            <% } %>
                                                <div>
                                                    <%- await
                                                        render_custom_fields(invoice.custom_fields,'SECTION-A', invoice?.invoice_type
                                                        ,invoice.org_id, ` <div class="lv_table_row">
                                                        <div class="lv_table_label">{{field_name}}</div>
                                                        <div class="lv_table_value">{{field_value}}</div>
                                                </div>
                                                `)%>

                    <% if((invoice?.document_tenant_configuration?.print_sales_person == true || invoice?.document_tenant_configuration?.print_sales_person == 'true')) {%>                        
                        <% if((invoice?.account_manager_id)) {%>
                                <div class=" lv_table_row">
                                    <div class="lv_table_label">Sales Person</div>
                                    <div class="lv_table_value">
                                        <%= invoice?.account_manager_info?.first_name || "" %> <%= invoice?.account_manager_info?.last_name || "" %>
                                    </div>
                                </div>  
                            <% }%>
                    <% }%>

                </div>

        </div>

        <!-- right -->
        <div style="flex: 3; margin-top: -50px; text-align: right;">
            <% var label_name = label ||'' %>
            <div style="font-size: 14px; margin-bottom: 5px;">
                <%= label_name || "" %>
            </div>
            <% if (title_logo_base_64) { %>
                <div style="flex: 1;">
                    <img style="height:<%=logo_height ? logo_height:50 %>px; object-fit: contain;margin-bottom: 5px;margin-top: 5px;"
                     src="<%= title_logo_base_64 %>">
                </div>
                <% } else { %>
                <div style="flex: 1; height: 65px;"></div>
                <% } %>
                <% let billing_address=invoice.tenant_billing_address_info || {} %>
                    <div style="font-size: 10px; line-height: 1.3;">
                        <div style="font-size: 14px; margin-bottom: 5px;">
                            <%= invoice?.tenant_info?.legal_name || invoice?.tenant_info?.tenant_name || "" %>
                        </div>
                        <div>
                        <%=billing_address.address1 || "" %>
                        </div>
                        <div style="margin-bottom: 8px;">
                            <%=billing_address.city || "" %>, <%= billing_address.state || "" %>
                                    <%= billing_address.postal_code || "" %>
                        </div>
                        <% if (invoice?.tenant_info?.gst_number ||
                                            invoice?.tenant_info?.gst_number) { %>
                        <div style="display: flex; align-items: center; justify-content: end;">
                        <div style="margin-right: 5px;">GSTIN:</div>
                        <div><%= invoice?.tenant_info?.gst_number || "" %></div>
                        </div>
                        <% } %>

                        <% if (invoice?.tenant_info?.identification_number?.filter(item=>
                            item?.is_printable)?.length) { %>
                                <% invoice?.tenant_info?.identification_number?.filter(item=>
                                item?.is_printable)?.map(item => { %>
                                <div style="display: flex; align-items: center; justify-content: end;">
                                    <div style="margin-right: 5px;"><%=item?.identification_name %>:</div>
                                    <div> <%=item?.identification_number || "" %></div>
                                    </div>
                                <% })%>
                        <% }%>

                        <!-- <% if((invoice?.document_tenant_configuration?.print_sales_person == true ||invoice?.document_tenant_configuration?.print_sales_person == 'true')) {%>
                            <div  style="display: flex; align-items: center; justify-content: end;">
                                <% if((invoice?.account_manager_info?.first_name || invoice?.account_manager_info?.last_name)) {%>
                                    <div style="margin-right: 5px;">Sales Person:</div>
                                <% }%>
                                <div><%= invoice?.account_manager_info?.first_name || "" %> <%= invoice?.account_manager_info?.last_name || "" %></div>
                            </div>
                        <% }%> -->

                    </div>
                    <% if(invoice.download_button_url) { %>
                    <div style="margin-top: 20px; margin-left: 100px">
                        <a class="download_button primary_bg_color" href="<%=invoice.download_button_url%>"
                            target="_blank">Download </a>
                    </div>
                <% }%>
        </div>
    </div>

    <!-- SECTION-B -->
    <% if((invoice.document_tenant_configuration?.print_bill_from_ship_from_address == true || invoice.document_tenant_configuration?.print_bill_from_ship_from_address == 'true') && (invoice?.bill_from_address_info || invoice?.ship_from_address_info)) {%>
        <div style="margin-top: 20px; margin-bottom: 30px;width: 100%; display: grid;grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));grid-column-gap: 10px;">
            <div style="padding: 15px; border-radius: 10px; height: 90% !important" class="secondary_bg_color">
                <div class="primary_color" style="font-size: 16px; font-weight: bold"> Bill From </div>
                        <div style="line-height: 18px; margin-top: 5px; font-size: 11px">
                            <%= invoice?.bill_from_address_info?.address1%> <br />
                            <%= invoice?.bill_from_address_info?.city%>, <%= invoice?.bill_from_address_info?.state%><br />
                            <%= invoice?.bill_from_address_info?.postal_code%><br />
                            <% if (invoice?.bill_from_address_info?.gst_number) { %>
                                <b>GSTIN:</b> <%= invoice?.bill_from_address_info?.gst_number %> <br />
                            <% } %>
                        </div>
                    </div>
                    <div style="padding: 15px; border-radius: 10px; height: 90% !important" class="secondary_bg_color">
                        <div class="primary_color" style="font-size: 16px; font-weight: bold">
                            Ship From
                        </div>
                        <div style="line-height: 18px; margin-top: 5px; font-size: 11px">
                            <%= invoice?.ship_from_address_info?.address1%> <br />
                            <%= invoice?.ship_from_address_info?.city%>, <%= invoice?.ship_from_address_info?.state%><br />
                            <%= invoice?.ship_from_address_info?.postal_code%><br />
                            <% if (invoice?.ship_from_address_info?.gst_number) { %>
                                <b>GSTIN:</b> <%= invoice?.ship_from_address_info?.gst_number %> <br />
                            <% } %>
                        </div>
                    </div>
        </div>
    <% } %>
    <% if (invoice?.invoice_type === 'INVOICE') { %>
        <div
            style="margin-top: 20px; margin-bottom: 30px;width: 100%; display: grid;grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));grid-column-gap: 10px;">
            <% if(invoice?.is_marketplace_invoice && invoice?.seller_info){%>
                <div
                    style="padding: 15px; border-radius: 10px; height: 90% !important;background-color: <%=configuration?.SECTION_B?.left_left_part?.background_color %>;">
                    <div
                        style="font-size: 16px; font-weight: bold; color: <%=configuration?.SECTION_B?.left_left_part?.color%>">
                        <%= configuration?.SECTION_B?.left_left_part?.title?.text%>
                    </div>
                    <div style="line-height: 18px; margin-top: 5px; font-size: 11px">
                        <%= invoice?.seller_info?.seller_name || invoice?.seller_info?.alias_name || "" %> <br />
                            <% if(invoice?.seller_info?.office_address_info?.address1){ %>
                                <%= invoice?.seller_info?.office_address_info?.address1%> <br />
                                    <% } %>
                                        <% if(invoice?.seller_info?.office_address_info?.city &&
                                            invoice?.seller_info?.office_address_info?.state) {%>
                                            <%= invoice?.seller_info?.office_address_info?.city%>, <%=
                                                    invoice?.seller_info?.office_address_info?.state%>
                                                    <%}%>
                                                        <% if(invoice?.seller_info?.office_address_info?.postal_code) {%>
                                                            <%= invoice?.seller_info?.office_address_info?.postal_code%>
                                                                <br />
                                                                <% } %>
                                                                    <% if (invoice?.seller_info?.gst_number) { %>
                                                                        <b>GSTIN:</b>
                                                                        <%= invoice?.seller_info?.gst_number %> <br />
                                                                            <% } %>
                                                                                <% if (invoice?.seller_info?.email_id_1 ) {
                                                                                    %>
                                                                                    <b>Email:</b>
                                                                                    <%= invoice?.seller_info?.email_id_1 %>
                                                                                        <br />
                                                                                        <% } %>
                                                                                            <% if (
                                                                                                invoice?.seller_info?.mobile_1
                                                                                                ) { %>
                                                                                                <b>Phone:</b>
                                                                                                <%= invoice?.seller_info?.mobile_1
                                                                                                    %> <br />
                                                                                                    <% } %>
                    </div>
                </div>
                <% } %>
    
                    <div style="padding: 15px; border-radius: 10px; height: 90% !important" class="secondary_bg_color">
                        <div class="primary_color" style="font-size: 16px; font-weight: bold">
                            <%= configuration?.SECTION_B?.left_part?.title?.text%>
                        </div>
                        <div style="line-height: 18px; margin-top: 5px; font-size: 11px">
                            <%= invoice?.customer_info?.legal_name || invoice?.customer_info?.customer_name ||
                                invoice?.customer_info?.customer_info?.customer_name || invoice?.customer_info?.alias_name
                                || "" %> <br />
                                <%= invoice?.billing_address_info?.address1%> <br />
                                    <%= invoice?.billing_address_info?.city%>, <%= invoice?.billing_address_info?.state%>
                                            <%= invoice?.billing_address_info?.postal_code%><br />
                                                <% if (invoice?.customer_info?.gst_number) { %>
                                                    <b>GSTIN:</b>
                                                    <%= invoice?.customer_info?.gst_number %> <br />
                                                        <% } %>
                                                            <% if (invoice?.customer_info?.email_address ||
                                                                invoice?.customer_info?.customer_info?.email_address) { %>
                                                                <b>Email:</b>
                                                                <%= invoice?.customer_info?.email_address ||
                                                                    invoice?.customer_info?.customer_info?.email_address %>
                                                                    <br />
                                                                    <% } %>
                                                                        <% if ( invoice?.customer_info?.phone_number ||
                                                                            invoice?.customer_info?.customer_info?.phone_number)
                                                                            { %>
                                                                            <b>Phone:</b>
                                                                            <%= invoice?.customer_info?.phone_number ||
                                                                                invoice?.customer_info?.customer_info?.phone_number
                                                                                %> <br />
                                                                                <% } %>
                        </div>
                    </div>
                    <div style="padding: 15px; border-radius: 10px; height: 90% !important" class="secondary_bg_color">
                        <div class="primary_color" style="font-size: 16px; font-weight: bold">
                            <%=configuration?.SECTION_B?.right_part?.title?.text%>
                        </div>
                        <div style="line-height: 18px; margin-top: 5px; font-size: 11px">
                            <% if(invoice?.document_tenant_configuration?.print_party_name==true
                                                    ||invoice?.document_tenant_configuration?.print_party_name=='true' ) {%>
                            <%= invoice?.customer_info?.legal_name || invoice?.customer_info?.customer_name ||
                                invoice?.customer_info?.customer_info?.customer_name || invoice?.customer_info?.alias_name
                                || "" %> <br />
                                <% } %>
                                <%= invoice?.shipping_address_info?.address1%> <br />
                                    <%= invoice?.shipping_address_info?.city%>, <%= invoice?.shipping_address_info?.state %>
                                            <%= invoice?.shipping_address_info?.postal_code%><br />
                                                <% if ( invoice?.shopify_shipping_phone_number) { %>
                                                    <b>Phone:</b>
                                                    <%= invoice?.shopify_shipping_phone_number %> <br />
                                                        <% } %>
                        </div>
                    </div>
        </div>
    <% } %>
    <% else if (invoice?.invoice_type === 'CONSUMPTION_ORDER') { %>
        <div
            style="margin-top: 20px; margin-bottom: 30px;width: 100%; display: grid;grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));grid-column-gap: 10px;">
                <div
                    style="padding: 15px; border-radius: 10px; height: 90% !important;background-color: <%=configuration?.SECTION_B?.left_left_part?.background_color %>;">
                    <div
                        style="font-size: 16px; font-weight: bold; color: <%=configuration?.SECTION_B?.left_left_part?.color%>">
                        Ship To
                    </div>
                    <div style="line-height: 18px; margin-top: 5px; font-size: 11px">
                        <%= invoice?.customer_info?.customer_name || invoice?.customer_info?.legal_name || "" %> <br />
                            <% if(invoice?.shipping_address_info?.address1){ %>
                                <%= invoice?.shipping_address_info?.address1%> <br />
                                    <% } %>
                                        <% if(invoice?.shipping_address_info?.city &&
                                            invoice?.shipping_address_info?.state) {%>
                                            <%= invoice?.shipping_address_info?.city%>, <%=
                                                    invoice?.shipping_address_info?.state%>
                                                    <%}%>
                                                        <% if(invoice?.shipping_address_info?.postal_code) {%>
                                                            <%= invoice?.shipping_address_info?.postal_code%>
                                                                <br />
                                                                <% } %>
                                                                    <% if (invoice?.customer_info?.gst_number) { %>
                                                                        <b>GSTIN:</b>
                                                                        <%= invoice?.customer_info?.gst_number %> <br />
                                                                            <% } %>
                                                                                <% if (invoice?.customer_info?.email_address ) {
                                                                                    %>
                                                                                    <b>Email:</b>
                                                                                    <%= invoice?.customer_info?.email_address %>
                                                                                        <br />
                                                                                        <% } %>
                                                                                            <% if (
                                                                                                invoice?.customer_info?.phone_number
                                                                                                ) { %>
                                                                                                <b>Phone:</b>
                                                                                                <%= invoice?.customer_info?.phone_number
                                                                                                    %> <br />
                                                                                                    <% } %>
                    </div>
                </div>
    
                    <div style="padding: 15px; border-radius: 10px; height: 90% !important" class="secondary_bg_color">
                        <div class="primary_color" style="font-size: 16px; font-weight: bold">
                            Ship From
                        </div>
                        <div style="line-height: 18px; margin-top: 5px; font-size: 11px">
                            <%= invoice?.tenant_info?.legal_name || invoice?.tenant_info?.tenant_name || "" %> <br />
                                <%= invoice?.ship_from_address_info?.address1%> <br />
                                    <%= invoice?.ship_from_address_info?.city%>, <%= invoice?.ship_from_address_info?.state%>
                                            <%= invoice?.ship_from_address_info?.postal_code%><br />
                                                <% if (invoice?.tenant_info?.gst_number) { %>
                                                    <b>GSTIN:</b>
                                                    <%= invoice?.tenant_info?.gst_number %> <br />
                                                        <% } %>
                                                            <% if (invoice?.tenant_info?.email_address ||
                                                                invoice?.tenant_info?.email_address) { %>
                                                                <b>Email:</b>
                                                                <%= invoice?.tenant_info?.email_address ||
                                                                    invoice?.tenant_info?.email_address %>
                                                                    <br />
                                                                    <% } %>
                                                                        <% if ( invoice?.tenant_info?.phone_number ||
                                                                            invoice?.tenant_info?.phone_number)
                                                                            { %>
                                                                            <b>Phone:</b>
                                                                            <%= invoice?.tenant_info?.phone_number ||
                                                                                invoice?.tenant_info?.phone_number
                                                                                %> <br />
                                                                                <% } %>
                        </div>
                    </div>
        </div>
    <% } %>

    <!-- SECTION-C -->
    <table class="lines_table">
        <thead class="primary_color" style="border: 1px solid">
            <tr class="primary_bg_color" style="color: #ffffff; text-align: left; font-weight: bold;">
                <th class="lines_table_header" style="width: 10px"> # </th>
                <th class="lines_table_header">Item & Description</th>
                <th class="lines_table_header">HSN/SAC</th>
                <th class="lines_table_header">Qty</th>
                <th class="lines_table_header">UQC</th>
                <% if (uniqueFields?.length > 0) { %>
                    <% uniqueFields.forEach(field => { %>
                        <th class="lines_table_header"><%= field.field_name %></th>
                    <% }) %>
                <% } %>
                <% if (invoice?.invoice_type === 'INVOICE') { %>
                    <th class="lines_table_header">Price/Unit</th>
                <% } %>
              <% if(invoice.document_tenant_configuration?.print_discount == true || invoice.document_tenant_configuration?.print_discount == 'true') {%>
                <th class="lines_table_header">Discount</th>
              <%}%>
                <% if (invoice?.invoice_type === 'INVOICE') { %>
                    <th class="lines_table_header">Tax</th>
                    <th style="padding: 2px 5px; white-space: nowrap">Item Total</th>
                <% } %>
            </tr>
        </thead>
        <tbody>
            <% invoice.invoice_lines.forEach(function(product,index) { %>
                <tr>
                    <td class="lines_table_rows" style="text-align: center;">
                        <%= index+1 %>
                    </td>
                    <td class="lines_table_rows">
                        <div style="width: 170px;">
                            <% if((invoice?.document_tenant_configuration?.print_item_code==true
                            ||invoice?.document_tenant_configuration?.print_item_code=='true' ) &&
                            product?.product_sku_info?.ref_product_code) {%>
                                <div style="font-size: 10px;  font-weight: 500; margin-bottom: 2px;">
                              <%= product?.product_sku_info?.ref_product_code %>
                            </div>
                            <% } %>
                              <% if((invoice?.document_tenant_configuration?.print_internal_sku_code==true
                                ||invoice?.document_tenant_configuration?.print_internal_sku_code=='true' ) &&
                                product?.product_sku_info?.internal_sku_code) {%>
                                <div style="font-size: 10px;  font-weight: 500; margin-bottom: 2px;">
                                  <%= product?.product_sku_info?.internal_sku_code %>
                                </div>
                                <% } %>
                            <div style="font-weight: bold;padding-bottom: 2px;"><%= product.product_sku_name %></div>
                            <% if(product.product_sku_info?.product_type == 'BUNDLE') { %>
                                <div style="font-size: 10px; border: 1px solid black; padding: 2px; margin-left: 2px; margin-top: 3px;border-radius: 2px; width: 20%; text-align: center; color: <%= invoice.tenant_configuration.doc_primary_colour%> !important">
                                    Bundle
                                </div>
                                <% } %>
                            <% if( product?.product_sku_info?.product_category_info ) { %>
                                <% if((invoice?.document_tenant_configuration?.print_category_status =='Child Category' )) {%>
                                    <div><span style="font-weight:bold ;">Category - </span> <%= product?.product_sku_info?.product_category_info?.product_category_name %></div>
                                <% } %>
                                <% if((invoice?.document_tenant_configuration?.print_category_status =='Parent Category' )) {%>
                                    <div><span style="font-weight: bold;">Category - </span>  <%= product?.product_sku_info?.product_category_info?.category_path?.[0] %></div>
                                <% } %>
                                <% if((invoice?.document_tenant_configuration?.print_category_status =='Full Category Path' )) {%>
                                    <div><span style="font-weight:bold ;">Category - </span>  <%= product?.product_sku_info?.product_category_info?.category_path?.join('/') %></div>
                                <% } %>
                            <% } %>
                                <% if (product.remarks && configuration?.print_line_remarks) { %>
                                    </br><span class="primary_color" style="font-size: 10px;">
                                        <%- product?.remarks || "" %>
                                    </span>
                                    <% }%>

                                        <% product.fl_product_batches=product?.bundle_products?.length===1 ?
                                            product?.bundle_products?.[0]?.fl_product_batches :
                                            product?.fl_product_batches %>
                                            <% if ((product?.product_batches?.length ||
                                                product?.fl_product_batches?.length) &&
                                                invoice?.document_tenant_configuration?.print_bundled_products) { %>
                                                <div class="primary_color"
                                                    style="font-size: 10px; margin-top: 5px; font-weight: 500;">

                                                    <% if(product?.product_batches?.length) { %>
                                                        <% product?.product_batches.forEach(function(pbtch) { %>
                                                            <div class="primary_color"
                                                                style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                                <% if (pbtch?.custom_batch_number) { %>
                                                                    Batch #<%= pbtch?.custom_batch_number %> </br>
                                                                <% } %>
                                                                <% if(pbtch?.lot_number){ %>
                                                                    Lot #<%= pbtch?.lot_number %> </br>
                                                                <% } %>
                                                                <% if(pbtch?.expiry_date && (pbtch?.expiry_date< '2099-01-01' )){ %>
                                                                    Expiry  <%= new Date(pbtch?.expiry_date).customFormat(product?.product_sku_info?.expiry_date_format) %></br>
                                                                <% } %>
                                                                <% if(pbtch?.manufacturing_date) { %>
                                                                    Mfg Date  <%= new Date(pbtch?.manufacturing_date).customFormat(product?.product_sku_info?.manufacturing_date_format) %><br>
                                                                <%}%>
                                                                Quantity <%= toFixedQuantity(pbtch.quantity, product?.uom_info?.[0]?.precision) %> <%= product?.uom_info?.[0]?.uqc?.toProperCase()|| 'Nos' %></br>
                                                                <% if (pbtch?.custom_fields?.length > 0) { %>
                                                                    <% let batchCustomFields = pbtch?.custom_fields; %>
                                                                    <% batchCustomFields?.forEach(field => { %>
                                                                        <% if((field?.is_printable ==true || field?.is_printable=='true') &&(field?.is_active ==true || field?.is_active=='true') ) { %> 
                                                                        <div style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                                            <%= field?.field_name %> - <%= field?.field_value %>
                                                                        </div>
                                                                        <%} %>
                                                                    <% }); %>
                                                                <% } %>                           
                                                            </div>
                                                            <% }) %>
                                                                <% } else if(product?.fl_product_batches?.length) {%>

                                                                    <% product?.fl_product_batches.forEach(function(pbtch)
                                                                        { %>
                                                                        <div class="primary_color"
                                                                            style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                                            <% if (pbtch?.custom_batch_number) { %>
                                                                                Batch #<%= pbtch?.custom_batch_number %>
                                                                                    </br>
                                                                                    <% } %>
                                                                                        <% if(pbtch?.lot_number){ %>
                                                                                            Lot #<%= pbtch?.lot_number
                                                                                                %> </br>
                                                                                                <% } %>
                                                                                                    <% if(pbtch?.expiry_date
                                                                                                        &&
                                                                                                        (pbtch?.expiry_date
                                                                                                        < '2099-01-01'
                                                                                                        )){ %>
                                                                                                        Expiry  <%= new Date(pbtch?.expiry_date).customFormat(product?.product_sku_info?.expiry_date_format) %>
                                                                                                            </br>
                                                                                                            <% } %>
                                                                                                            <% if(pbtch?.manufacturing_date) { %>
                                                                                                                Manufacturing Date  <%= new Date(pbtch?.manufacturing_date).customFormat(product?.product_sku_info?.manufacturing_date_format) %>
                                                                                                                <br>
                                                                                                            <%}%>
                                                                                                                Quantity
                                                                                                                <%= toFixedQuantity(pbtch.quantity, product?.uom_info?.[0]?.precision) %> <%= product?.uom_info?.[0]?.uqc?.toProperCase()|| 'Nos' %>
                                                                                                                    <%= product?.uom_info?.[0]?.uqc?.toProperCase()
                                                                                                                        || 'Nos'
                                                                                                                        %>
                                                                                                                        </br>
                                                                                                                        
                                                                        </div>
                                                                        <% }) %>
                                                                            <% } %>

                                                </div>
                                                <% } %>


                        </div>
                    </td>
                    <td class="lines_table_rows">
                        <%= product?.hsn_code ||"" %>
                    </td>
                    <td class="lines_table_rows">
                        <div>
                            <%= toFixedQuantity(product.quantity, product?.uom_info?.[0]?.precision) %>
                        </div>
                        <%if(product.free_quantity){%>
                            <div>
                                <span class="primary_color" style="font-size: 10px;"><%=`+ ${product.free_quantity} Free`%></span>
                            </div>
                            <%}%>
                    </td>
                    <td class="lines_table_rows">
                        <%= product?.uom_info?.[0].uqc.toProperCase() || 'Nos' %>
                    </td>
                    <% if (product?.invoice_line_custom_fields?.length > 0 && uniqueFields?.length > 0) { %>
                        <% let ilCustomFields = product?.invoice_line_custom_fields; %>
                        <% uniqueFields.forEach(field => { %>
                            <% let matchingRow = ilCustomFields.find(row => row.cf_id === field.cf_id); %>
                            <% if(matchingRow?.field_type == 'DATE' && matchingRow?.field_value?.includes('T')){ %>
                                <td class="lines_table_rows"><%= matchingRow?.field_value?.split('T')?.[0] || '-'  %></td>
                              <% } else {%>
                                <td class="lines_table_rows"><%= matchingRow?.field_value || '-' %></td>
                              <%} %>
                        <% }); %>
                    <% } %>
                    <% if (invoice?.invoice_type === 'INVOICE') { %>
                        <td class="lines_table_rows">
                            <%= applyUserPolicy(product?.unit_price, invoice?.currency_code, invoice?.organisation_info?.global_config?.settings?.price_precision, invoice?.hide_selling_price) %>
                        </td>
                    <% } %>
                    <% if(invoice.document_tenant_configuration?.print_discount == true || invoice.document_tenant_configuration?.print_discount == 'true') {%>
                    <td class="lines_table_rows">
                        <%= toFixedQuantity(product?.line_discount_percentage || 0,2) %>%
                    </td>
                    <%}%>
                    <% if (invoice?.invoice_type === 'INVOICE') { %>
                        <td class="lines_table_rows">
                            <%= toFixedQuantity(product.tax_info.tax_value,2) %>%
                        </td>
                        <td class="lines_table_rows">
                            <%= applyUserPolicy(product?.line_total_price, invoice?.currency_code, invoice?.organisation_info?.global_config?.settings?.price_precision, invoice?.hide_selling_price) %>
                        </td>
                    <% } %>
                </tr>

                <% if(invoice?.document_tenant_configuration?.print_bundled_products &&
                    product?.bundle_products?.length>1) {%>
                    <% (product?.bundle_products||[]).forEach(function(child_product,child_index) { %>
                        <tr>
                            <td class="lines_table_rows" style="text-align: center;">
                                <%= `${index+1}.${child_index+1}` %>
                            </td>
                            <td class="lines_table_rows">
                                <%= child_product.product_sku_info.ref_product_code %>
                            </td>
                            <td class="lines_table_rows">
                                <div style="width: 170px;">
                                    <%= child_product.product_sku_name %>
                                        <% if (child_product?.product_batches?.length &&
                                            configuration?.print_line_batches) { %>
                                            <div class="primary_color"
                                                style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                <% child_product?.product_batches.forEach(function(pbtch) { %>
                                                    <div class="primary_color"
                                                        style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                        <% if (pbtch?.custom_batch_number) { %>
                                                            Batch #<%= pbtch?.custom_batch_number %> </br>
                                                                <% } %>
                                                                    <% if(pbtch?.lot_number){ %>
                                                                        Lot #<%= pbtch?.lot_number %> </br>
                                                                            <% } %>
                                                                                <% if(pbtch?.expiry_date &&
                                                                                    (pbtch?.expiry_date < '2099-01-01'
                                                                                    )){ %>
                                                                                    Expiry  <%= new Date(pbtch?.expiry_date).customFormat(child_product?.product_sku_info?.expiry_date_format) %>
                                                                                        </br>
                                                                                        <% } %>
                                                                                            Quantity <%=
                                                                                                pbtch.quantity%>
                                                                                                <%= child_product?.uom_info?.[0]?.uqc?.toProperCase()
                                                                                                    || 'Nos' %>
                                                                                                    </br>
                                                    </div>
                                                    <% }) %>
                                            </div>
                                            <% } %>
                                                <% if (!child_product?.product_batches?.length &&
                                                    child_product?.fl_product_batches?.length &&
                                                    configuration?.print_line_batches) { %>
                                                    <div class="primary_color"
                                                        style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                        <% child_product?.fl_product_batches.forEach(function(pbtch) {
                                                            %>
                                                            <div class="primary_color"
                                                                style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                                <% if (pbtch?.custom_batch_number) { %>
                                                                    Batch #<%= pbtch?.custom_batch_number %> </br>
                                                                        <% } %>
                                                                            <% if(pbtch?.lot_number){ %>
                                                                                Lot #<%= pbtch?.lot_number %> </br>
                                                                                    <% } %>
                                                                                        <% if(pbtch?.expiry_date &&
                                                                                            (pbtch?.expiry_date
                                                                                            < '2099-01-01' )){ %>
                                                                                            Expiry  <%= new Date(pbtch?.expiry_date).customFormat(child_product?.product_sku_info?.expiry_date_format) %>
                                                                                                </br>
                                                                                                <% } %>
                                                                                                    Quantity <%=
                                                                                                        pbtch.quantity%>
                                                                                                        <%= child_product?.uom_info?.[0]?.uqc?.toProperCase()
                                                                                                            || 'Nos' %>
                                                                                                            </br>
                                                            </div>
                                                            <% }) %>
                                                    </div>
                                                    <% } %>
                                </div>
                            </td>
                            <td class="lines_table_rows">
                                <%= child_product?.product_sku_info?.hsn_code ||"" %>
                            </td>
                            <td class="lines_table_rows">
                                <%= toFixedQuantity(child_product.quantity, child_product?.uom_info?.[0]?.precision) %>
                            </td>
                            <td class="lines_table_rows">
                                <%= child_product?.uom_info?.[0].uqc.toProperCase() || 'Nos' %>
                            </td>
                            <% if (product?.invoice_line_custom_fields && product?.invoice_line_custom_fields?.length > 0) { %>
                                <% let ilCustomFields = product?.invoice_line_custom_fields %>
                                <% ilCustomFields.forEach(ilcf => { %>
                                  <% if((ilcf.is_printable == true || ilcf.is_printable == 'true')&& ilcf.field_type !== 'ATTACHMENT') { %>
                                    <td class="lines_table_rows"></td>
                                  <% } %>
                                <% }) %>
                            <% } %>
                            <td class="lines_table_rows"> </td>
                            <td class="lines_table_rows"> </td>
                            <td class="lines_table_rows"> </td>
                            <td class="lines_table_rows"> </td>
                        </tr>
                        <% }) %>
                            <% }%>
                                <% }) %>
        </tbody>
    </table>

    <!-- SECTION-D -->
    <% if (invoice?.invoice_type === 'INVOICE') { %>
        <div style="display: flex;">
            <div style="margin-top: 10px;">
                <% if(invoice.document_tenant_configuration?.print_total_quantity){ %>
                <div style="width: 100%; font-size: 12px; margin-bottom: 5px;">
                    Total Quantity: 
                    <b style="font-size: 12px;">
                        <%
                        var totalQty = invoice?.invoice_lines?.reduce((total, item) => {
                            if (item.is_bundle && item.bundle_products?.length && !invoice.calculate_total_quantity_with_parent_quantity_for_bundle_products) {
                            const bundleTotal = item.bundle_products.reduce((bundleTotal, bundleItem) => {
                                return bundleTotal + (bundleItem.quantity || 0);
                            }, 0);
                            return total + bundleTotal;
                            }
                            return total + (item.quantity || 0) + (item.free_quantity || 0);
                        }, 0);
                        %>
                        <%= toFixedQuantity(totalQty, 2) %>
                    </b>
                </div>
                <%}%>
                
                <div style="width: 100%; font-size: 12px;"> Total In Words: <b>
                    <% if(invoice?.hide_selling_price == true || invoice?.hide_selling_price == 'true') { %>
                        XXXXXX
                    <% } else { %>
                        <%= invoice.invoice_total_in_words || "" %>
                    <% } %>
                    </b></div>
                <% if ( invoice?.e_way_bills?.[0]?.ewb_no) { %>
                    <div style="margin-top: 20px; margin-bottom: 30px;width: 100%; display: grid;grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));grid-column-gap: 10px; border-radius: 10px;"
                        class="secondary_bg_color">
                        <div style="padding: 15px; border-radius: 10px; height: 90% !important;">
                            <div
                                style="font-size: 16px; font-weight: bold; color: <%=configuration?.SECTION_B?.left_left_part?.color%>">
                                Transport Information</div>
                            <div style="line-height: 16px; margin-top: 5px; font-size: 11px; display: flex; gap:50px;">
                                <div style="line-height: 18px; margin-top: 5px; font-size: 11px">
                                    <b>Transporter Name:</b>
                                    <%= invoice?.e_way_bills?.[0]?.transporter_info?.seller_name || "-" %>
                                        <br />
                                        <b>Vehicle Number:</b>
                                        <%= invoice?.e_way_bills?.[0]?.vehicle_details?.[0]?.vehicle_number || "-" %>
                                            <br />
                                            <b>Transportation Mode:</b>
                                            <%= invoice?.e_way_bills?.[0]?.vehicle_details?.[0]?.transportation_mode || "-"
                                                %>
                                                <br />
                                                <b>Transporter GST:</b>
                                                <%= invoice?.e_way_bills?.[0]?.transporter_info?.gst_number || "-" %>
                                                    <br />
                                </div>
                                <div style="line-height: 18px; margin-top: 5px; font-size: 11px">
                                    <b>Transportation Doc Date:</b>
                                    <%= invoice?.e_way_bills?.[0]?.vehicle_details?.[0]?.transportation_doc_date || "-" %>
                                        <br />
                                </div>
                            </div>
                        </div>
                    </div>
                    <% } %>

                        <% if(custom_template.rendered_html_section_d){ %>
                            <%- custom_template.rendered_html_section_d %>
                                <% } %>

                                    <% if(invoice.remarks){ %>
                                        <div style="width: 90%; font-size: 12px;margin-top: 5px;">
                                            Remarks : <b>
                                                <%= invoice.remarks %>
                                            </b>
                                        </div>
                                        <% } %>
                                        <% if((invoice?.shopify_note) && invoice?.document_tenant_configuration?.print_shopify_remarks){ %>
                                        <div style="width: 90%; font-size: 12px;margin-top: 5px;">
                                            Shopify Remarks : 
                                                <b>
                                                    <%= invoice.shopify_note %>
                                                </b>
                                        </div>
                                        <% } %>
                                            <%- await render_custom_fields(invoice.custom_fields,'SECTION-B', invoice?.invoice_type ,
                                                invoice.org_id, ` <div style="width: 90%; font-size: 12px;margin-top: 5px;">
                                                {{field_name}} : <b>{{field_value}}</b>
            </div>
            `)%>
        </div>
    
        <div style='margin-left: auto; line-height: 18px;  padding-top: 10px; font-size: 11px'>
            <table style="padding: 0; border-collapse: collapse; width: 100%">
                <tbody>
                    <tr>
                        <td style="white-space: nowrap; padding-right: 10px"><b>Total </b></td>
                        <td style="white-space: nowrap; padding-left: 10px; text-align: right">
                            <%= applyUserPolicy(invoice?.invoice_base_price, invoice?.currency_code, invoice?.organisation_info?.global_config?.settings?.price_precision, invoice?.hide_selling_price) %>
                        </td>
                    </tr>

                    <tr>
                        <td style="white-space: nowrap; padding-right: 10px"><b>Discount </b></td>
                        <td style="white-space: nowrap; padding-left: 10px; text-align: right">
                            <%= applyUserPolicy(invoice?.discount_amount, invoice?.currency_code, invoice?.organisation_info?.global_config?.settings?.price_precision, invoice?.hide_selling_price) %>
                        </td>
                    </tr>
                    <%var invoice_charges=invoice.other_charges ?? []%>
                    <% invoice_charges.forEach(function(charges) { %>
                        <% if(charges?.tax_info) { %>
                        <tr>
                            <td style="white-space: nowrap; padding-right: 10px">
                                <b>
                                    <%= charges.charge_name%>
                                </b>
                            </td>
                            <td
                                style="white-space: nowrap; padding-left: 10px; text-align: right">
                                <%= formatCurrency(charges.charge_amount,
                                    invoice?.currency_code) %>
                            </td>
                        </tr>
                        <% } %>
                    <% })%>
                    <%invoice.tax_info.forEach(function(tax){%>
                        <tr>
                            <td style="white-space: nowrap; padding-right: 10px"><b>
                                    <%= tax.tax_type_name %>
                                </b></td>
                            <td style="white-space: nowrap; padding-left: 10px; text-align: right">
                            <%= applyUserPolicy(tax?.tax_amount, invoice?.currency_code, invoice?.organisation_info?.global_config?.settings?.price_precision, invoice?.hide_selling_price) %>
                            </td>
                        </tr>
                    <%})%>
                    <% if(invoice?.tcs_info){ %>
                        <tr>
                            <td style="white-space: nowrap; padding-right: 10px"><b>TCS </b></td>
                            <td style="white-space: nowrap; padding-left: 10px; text-align: right">
                                <%= formatCurrency(invoice.tcs_info.tcs_amount, invoice?.currency_code) %>
                            </td>
                        </tr>
                    <% }%>
                    <% if(invoice?.tds_info){ %>
                        <tr>
                            <td style="white-space: nowrap; padding-right: 10px"><b>TDS </b></td>
                            <td style="white-space: nowrap; padding-left: 10px; text-align: right">
                                (-)<%= formatCurrency(invoice.tds_info.tds_amount, invoice?.currency_code)
                                    %>
                            </td>
                        </tr>
                    <% }%>
                    <%var invoice_charges=invoice.other_charges ?? []%>
                    <% invoice_charges.forEach(function(charges) { %>
                        <% if(!charges?.tax_info) { %>
                        <tr>
                            <td style="white-space: nowrap; padding-right: 10px">
                                <b>
                                    <%= charges.charge_name%>
                                </b>
                            </td>
                            <td
                                style="white-space: nowrap; padding-left: 10px; text-align: right">
                                <%= formatCurrency(charges.charge_amount,
                                    invoice?.currency_code) %>
                            </td>
                        </tr>
                        <% } %>
                    <% })%>
                    <% if (invoice.charge_1_name) { %>
                                <tr>
                                    <td style="white-space: nowrap; padding-right: 10px">
                                        <b>
                                            <%= invoice.charge_1_name %>:
                                        </b>
                                    </td>
                                    <td
                                        style="white-space: nowrap; padding-left: 10px; text-align: right">
                                        <%= formatCurrency(invoice.charge_1_value,
                                            invoice?.currency_code) %>
                                    </td>
                                </tr>
                            <% }%>
                    <tr>
                        <td style="white-space: nowrap; padding-right: 10px">
                            <b>Round Off</b>
                        </td>
                        <td
                            style="white-space: nowrap; padding-left: 10px; text-align: right">
                            <%= formatCurrency(invoice.invoice_round_off,
                                invoice?.currency_code) %>
                        </td>
                    </tr>
                    <tr>
                        <td style="white-space: nowrap;"><b>Grand Total</b></td>
                        <td
                            style="white-space: nowrap; padding-left: 10px; text-align: right">
                            <b>
                                <%= applyUserPolicy((invoice?.invoice_grand_total + invoice?.invoice_round_off), invoice?.currency_code, invoice?.organisation_info?.global_config?.settings?.price_precision, invoice?.hide_selling_price) %>
                            </b>
                        </td>
                    </tr>
                    <%if(invoice.credits_applied){%>
                        <tr>
                            <td
                                style="white-space: nowrap; padding-right: 10px">
                                <b>Credits Applied </b>
                            </td>
                            <td
                                style="white-space: nowrap; padding-left: 10px; text-align: right">
                                (-) <%= applyUserPolicy(invoice?.credits_applied, invoice?.currency_code, invoice?.organisation_info?.global_config?.settings?.price_precision, invoice?.hide_selling_price) %>
                            </td>
                        </tr>
                    <%}%>
                    <%if(invoice.total_payment_made){%>
                        <tr>
                            <td
                                style="white-space: nowrap; padding-right: 10px">
                                <b>Payment Made </b>
                            </td>
                            <td
                                style="white-space: nowrap; padding-left: 10px; text-align: right">
                                (-) <%= applyUserPolicy(invoice?.total_payment_made, invoice?.currency_code, invoice?.organisation_info?.global_config?.settings?.price_precision, invoice?.hide_selling_price) %>
                            </td>
                        </tr>
                    <%}%>
                    <% if((invoice.invoice_grand_total + invoice.invoice_round_off -
                        invoice.credits_applied -
                        invoice.total_payment_made)>=0) {%>
                        <tr>
                            <td
                                style="white-space: nowrap; border-top: 2px solid black;">
                                <b>Balance Due </b>

                            </td>
                            <td
                                style="white-space: nowrap; border-top: 2px solid black; padding-left: 10px; text-align: right">
                                <b>
                                    <%= applyUserPolicy(((invoice?.invoice_grand_total ||0) + (invoice.invoice_round_off ||0) - (invoice?.credits_applied ||0) - (invoice.total_payment_made || 0)), invoice?.currency_code, invoice?.organisation_info?.global_config?.settings?.price_precision, invoice?.hide_selling_price) %>
                                </b>
                            </td>
                        </tr>
                    <% }%>



                </tbody>
            </table>
            <%if(invoice?.document_tenant_configuration?.print_signature_input){%>
                <div style="margin-top:10px;padding:0px 7px 5px 7px; border-radius: 4px; display:flex;flex-direction:column;align-items:center;" class="secondary_bg_color">
                    <div style="text-align: center;padding-top: 5px;font-size: 10px;"><%= invoice?.document_tenant_configuration?.signature_name||'' %></div>
                    <b>
                        <div style="height:36px;">
                            <%if(invoice?.document_tenant_configuration?.doc_sign_base_64){%>

                                    <img style="height:30px; width:150px; object-fit: contain; margin-bottom:2px;"
                                        src="data:image/png;base64,<%= invoice.document_tenant_configuration?.doc_sign_base_64 %>"
                                        alt="signature_img">
                            <% } %>
                        </div>
                    </b>
                    <div style="font-size: 10px;">Authorized Signatory</div>
                </div>
            <%}%>
        </div>
    <% } %>
</div>

<% if (invoice.irn) { %>
    <div style="padding: 10px 0px;
                    margin: 15px 0px;
                    border-top: 1px solid #9e9e9e;
                    border-bottom: 1px solid #9e9e9e;">
        <div style="display: flex;">
            <div>
                <img style="height: 100px;" src="<%= invoice.irn_uri %>" alt="">
            </div>
            <div style="padding: 0px 10px;
                        font-size: 12px;display: flex; flex-direction: column;">
                <div>
                    <div style="margin-bottom: 5px">
                        <div style="display: inline-block;">
                            IRN:
                        </div>
                        <div style="display: inline-block;">
                            <%= invoice.irn %>
                        </div>
                    </div>
                    <div style="margin-bottom: 5px">
                        <div style="display: inline-block;">
                            Ack No.:
                        </div>
                        <div style="display: inline-block;">
                            <%= invoice.ack_number %>
                        </div>
                    </div>
                    <div style="margin-bottom: 5px">
                        <div style="display: inline-block;">
                            Ack Date:
                        </div>
                        <div style="display: inline-block;">
                            <%= new Date(invoice.ack_date).toDateString().substring(4) %>
                        </div>
                    </div>
                    <% if (invoice?.ewb_no) { %>
                        <div style="margin-bottom: 5px">
                            <div style="display: inline-block;">
                                e-Way Bill #:
                            </div>
                            <div style="display: inline-block;">
                                <%= invoice?.ewb_no %>
                            </div>
                        </div>
                        <% }%>
                </div>
                <div style="margin-top: auto;margin-bottom: 5px;"">
                        E-Invoicing detail(s) generated from the Government's e-Invoicing system
                    </div>
                    </div>
                </div>
            </div>
        </div>
    <% } %>

<div <% if (invoice?.invoice_type === 'CONSUMPTION_ORDER') { %>
    style="padding-left: 25px;"
<% } %>
>
    <% if (invoice.document_tenant_configuration?.print_bank_details) { %>
        <% if (invoice?.bank_details) { %>
            <div style="margin-top: 25px;" class="div-to-next-page">
                <div>
                    <p style="font-size: 11px; font-weight: bold; color: <%= invoice.tenant_configuration.doc_primary_colour %>;">
                        Bank Details
                    </p>
                    <pre style="font-family: Arial; margin-top: 2px; line-height: 13px; font-size: 10px; font-weight: 500;"><%= invoice?.bank_details %></pre>
                </div>
            </div>
        <% } %>
    
        <% if (invoice?.qr_code) { %>
            <div style="margin-top: 10px;">
                <img src="<%= invoice?.qr_code %>" alt="QR Code" style="width: 100px; height: 100px;">
            </div>
        <% } %>
    <% } %>

                    <% if(invoice.document_tenant_configuration?.customer_notes){ %>
                        <div style=" margin-top: 10px; font-size: 12px; padding: 5px;" class="div-to-next-page">
                            <p class="primary_color" style="font-size: 13px; font-weight: bold;"> Customer Note
                            </p>
                            <div style="font-size: 10px;line-height: 15px;"> <% invoice.document_tenant_configuration?.customer_notes.split("\n").forEach(i=> {%>
                                <div style="font-weight: 500; line-height: 19px; font-size: 11px;"><%=i %></div>
                            <%})%>
                        </div>
                        </div>
                    <% } %>
                    <% if(invoice.terms_and_conditions){ %>
                        <div style=" margin-top: 10px; font-size: 12px; padding: 5px;" class="div-to-next-page">
                    <p class="primary_color" style="font-size: 13px; font-weight: bold;"> Terms and Conditions</p>
                    <div style="font-weight: 500; line-height: 19px; font-size: 11px;">
                        <%- invoice.terms_and_conditions %>
                    </div>
                </div>
                <% } else if(custom_template.rendered_default_t_and_c) { %>
                    <div style="margin-top: 10px; font-size: 12px;" class="div-to-next-page">
                        <p class="primary_color" style="font-size: 16px; font-weight: bold;"> Terms and
                            Conditions</p>
                        <div style="line-height: 16px;">
                            <%- custom_template.rendered_default_t_and_c %>
                        </div>
                    </div>
                    <% } %>
                                <div style="display: flex;margin-top: 0px;flex-wrap: wrap;">
                                    <% if(invoice?.document_tenant_configuration?.print_product_images) {%>
                                        <% let image_size=85 %>
                                            <% (invoice?.invoice_lines||[]).forEach((product,_idx)=> { %>
                                                <% if (product?.product_sku_info?.assets?.[0]?.url) {%>
                                                    <div
                                                        style="position: relative;border: 1px solid grey;height: <%=image_size%>px; width:  <%=image_size%>px;margin: 2px;">
                                                        <p style="position: absolute;bottom: -37px;left: 27px;">
                                                            <%= `${_idx+1}`%>
                                                        </p><img
                                                            src="<%= product?.product_sku_info?.assets?.[0]?.url %>"
                                                            width="<%=image_size%>" height="<%=image_size%>">
                                                    </div>
                                                    <% } %>
                                                        <%
                                                            (product?.bundle_products||[]).forEach((child_product,_idx_2)=>
                                                            { %>
                                                            <div
                                                                style="position: relative;border: 1px solid grey;height: <%=image_size%>px; width:  <%=image_size%>px;margin: 2px;">
                                                                <p style="position: absolute;bottom: -50px;left: 27px;">
                                                                    <%= `${_idx+1}.${_idx_2+1}`%>
                                                                </p><img
                                                                    src="<%= child_product?.product_sku_info?.assets?.[0]?.url %>"
                                                                    width="<%=image_size%>" height="<%=image_size%>">
                                                            </div>
                                                            <% }); %>
                                                                <% }); %>
                                                                <% } %>
                                </div>
                                <%if(invoice?.document_tenant_configuration?.print_attachments && invoice?.attachments?.length > 0){%>
                                    <div style="page-break-inside: avoid;">
                                  <h3 class="primary_color">Attachments</h3>
                                  <% invoice?.attachments?.forEach(function(inp){ %>
                                    <% if(inp.type === 'image/png' || inp.type === 'image/jpeg' ) {%>
                                            <div style="margin-bottom: 10px;">
                                              <img src="<%= inp.url %>" alt="<%= inp.name %>" style="max-width: 100%; max-height: 300px;padding-top: 15px;object-fit: contain;">
                                            </div>                
                                    <%}%>
                                 <% }) %>
                                 </div>
                                 <%}%>
                               
            </div>
        </div>
    </div>
