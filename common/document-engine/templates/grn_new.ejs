<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Procuzy</title>
    <style>
        body {
            font-family: Arial, Helvetica, sans-serif;
            margin: 0;
        }
        p {
            font-size: 10px;
        }
        .page {
            page-break-inside: avoid;
            box-sizing: border-box;
        }
        .table-container {
            border-collapse: collapse;
            margin-left: auto;
            margin-right: auto;
            width: 100% !important;
        }
        .table-container-inner{
            width: 100%;
            border-collapse: collapse;
        }
        .footer {
            position: fixed;
            bottom: <%= page_bottom_padding ||0 %>px;
            overflow: hidden;
        }
        .border {
            position: fixed;
            top: <%= page_top_padding || 0%>px;
            left: 0;
            border: 1px solid black;
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            page-break-after: always;
            page-break-inside: avoid;
            box-sizing: border-box;
            z-index: -1;
        }
        .terms-wrapper p{
            margin-top: 0 !important;
            margin-bottom: 0 !important;
            line-height: 12px !important;
            font-size: 10px !important;
        }
        .document-copy-count{
            font-size: 9px;
            text-align: right;
            font-weight: bold;
            padding: 4px 2px;
        }
        .document-copy-count_wrapper{
            width: calc(100% + 2px);
            margin-left: 1px;
            margin-top: -1px;
            border: 1px solid white;
            border-bottom: 1px solid black
        }
    </style>
</head>

<body class="page">
    <div class="border" style="height: calc(100% - <%= (page_bottom_padding + page_top_padding) || 0 %>px)"></div>
    <% let footerHeight = 50;
    if(Number(grn.discount_amount) !== 0) footerHeight += 13;
    if(grn.tax_info) footerHeight += (grn.tax_info?.length * 13);
    if(grn.tcs_info || grn.tds_info) footerHeight += 13;
    if(grn.other_charges) footerHeight += (grn?.other_charges?.length * 13);
    if(parseFloat(grn.charge_1_value) !== 0 && !grn?.freight_tax_info) footerHeight += 13;
    if(parseFloat(grn.grn_round_off) !== 0) footerHeight += 13;
    if(grn.document_tenant_configuration?.terms_and_conditions_type == 'Compact'){
      footerHeight += 140;
    } else {
        if(grn.document_tenant_configuration?.customer_notes) footerHeight += 60;
        if(grn?.document_tenant_configuration?.print_signature_input) footerHeight += 80;
    } 
    %>
    <% let uniqueFields = collectUniqueFields(grn.grn_lines, 'grn_line_custom_fields') %>
    <%  let uniqueChildTaxes = getUniqueChildTaxes(grn.grn_lines) %>
    <% var label_name = label ||'' %>
    <table class="table-container" style="margin-top: <%= page_top_padding || 0%>px;">
        <tr>
            <td style="padding: 0px">
                <div class="header">
                    <table class="table-container-inner">
                        <% if(print_letter_head == true || print_letter_head == 'true') {%>
                            <tr class="document-copy-count_wrapper">
                                <td colspan="4" style="font-weight: bold;font-size: 9px;text-align: end;">
                                <%= label_name %>
                                </td>
                            </tr>
                        <%} else{%>                        
                            <tr>
                                <div style="position: relative;">
                                    <div style="font-size: 22px; width: 100%; text-align: center; margin-top: 5px; margin-bottom: 5px;">
                                        <%= grn?.document_tenant_configuration?.document_title || 'Goods Receiving Note' %>
                                    </div>
                                    <div style="position: absolute; right: 10px; top: 0; line-height: 14px">
                                      <% if(grn?.document_tenant_configuration?.print_prepared_by == true || grn?.document_tenant_configuration?.print_prepared_by == 'true') {%>
                                        <div style="text-align: right; font-size: 9px; ">
                                          <% if(grn?.created_by) {%>
                                            Prepared By: <%= grn?.created_by_info?.first_name + grn?.created_by_info?.last_name%>
                                          <%} %>
                                        </div>
                                      <%} %>
                                    
                                      <% if(grn?.document_tenant_configuration?.print_prepared_at == true || grn?.document_tenant_configuration?.print_prepared_at == 'true') {%>
                                        <div style="text-align: right; font-size: 9px; ">
                                          <% if(grn?.created_at) {%>
                                            Prepared At: <%= new Date().formatDateTime(grn?.created_at) %>
                                          <%} %>
                                        </div>
                                      <%} %>
                                    </div>
                                    <div class="document-copy-count" style="position: relative; right: 10px; font-weight: bold; padding: 4px 2px; "><%= label_name %></div>
                                </div>

                            </tr>
                            <tr style="border-bottom: 1px solid black;">
                                <td colspan="4">
                                    <%let billing_address=grn?.tenant_info?.default_billing_address_info || {}%>
                                    <div style="display: flex; align-items: center; justify-content: center; padding: 5px; width: calc(100% - 10px);">
                                        <div style="line-height: 13px;">
                                            <div style="font-size: 16px; font-weight: bold; margin-bottom: 5px;">
                                                <% if (grn?.document_tenant_configuration?.print_organisation_name) { %>
                                                    <%= grn?.organisation_info?.organisation_name %>
                                                        <% } else { %>
                                                            <%= grn?.tenant_info?.legal_name ||
                                                        grn?.tenant_info?.tenant_name || "" %>
                                                        <% } %>
                                            </div>
                                            <div style="font-size: 10px; line-height: 13px">
                                                <div>
                                                    <%=billing_address.address1 || "" %>
                                                </div>
                                                <div>
                                                    <%=billing_address.city || "" %>, <%= billing_address.state || "" %>
                                                </div>
                                                <div>
                                                    <%= billing_address?.postal_code || "" %>, <%= billing_address?.country || "" %>
                                                </div>
                                            </div>
                                        </div>
                                        <div style="padding-right: 0px; text-align: right; margin-left: auto;">
                                            <div>
                                                <% if(grn.tenant_configuration.logo_base_64) { %>
                                                        <div style="text-align: right;">
                                                            <img style="height:<%=logo_height ? logo_height:50 %>px; object-fit: contain; margin-bottom: 5px;margin-top: 5px;" src="<%= grn.tenant_configuration.logo_base_64 %>" alt="tenant_img" />
                                                        </div>
                                                <% } else if(grn?.organisation_info?.org_logo &&
                                                        grn?.organisation_info?.org_logo !=`[{"url":"[]"}]`) { %>
                                                        <div style="text-align: right;">
                                                            <img style="height:<%=logo_height ? logo_height:50 %>px; object-fit: contain; margin-bottom: 5px;margin-top: 5px;" src="<%= grn.organisation_info.org_logo %>" alt="organisation_img" />
                                                        </div>
                                                <% } else { %>
                                                    <div style="flex: 1;">

                                                    </div>
                                                <% } %>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <%}%>
                        <tr>
                            <td colspan="2" style="border-right: 1px solid black; line-height: 13px; padding: 4px 5px; vertical-align: top;width: 50%; font-size: 10px; font-weight: 700;">
                                <div style="display: flex;">
                                    <div style="width: 150px;">GRN Number #</div>
                                    <div style="width: 150px;">
                                        <%= grn?.grn_number || "-" %>
                                    </div>
                                </div>
                                <% if(grn.document_tenant_configuration?.print_status == true || grn.document_tenant_configuration?.print_status == 'true') {%>
                                    <div style="display: flex;">
                                        <div style="width: 150px;">Status</div>
                                        <div style="width: 150px;">
                                            <%= grn.status?.toProperCase() %>
                                        </div>
                                    </div>
                                <%}%>
                                <div style="display: flex;">
                                    <div style="width: 150px;">GRN Date</div>
                                    <div style="width: 150px;">
                                        <%= new Date().formatDateInTimezone(grn?.grn_date_time) %>
                                    </div>
                                </div>
                                <% if (grn.invoice_number) {%>
                                <div style="display: flex;">
                                    <div style="width: 150px;">Invoice Number</div>
                                    <div style="width: 150px;">
                                        <%= grn.invoice_number %>
                                    </div>
                                </div>
                                <% }%>
                                
                                <% if (grn.invoice_date) { %>
                                    <div style="display: flex;">
                                      <div style="width: 150px;">Invoice Date</div>
                                      <div style="width: 150px;">
                                        <%= new Date().formatDateInTimezone(grn?.invoice_date) %>
                                      </div>
                                    </div>
                                  <% } %>
                                  
                                  <% if (grn.po_number) { %>
                                    <div style="display: flex;">
                                      <div style="width: 150px;">PO Number</div>
                                      <div style="width: 150px;">
                                        <%= grn?.po_number %>
                                      </div>
                                    </div>
                                  <% } %>
                                  
                                  <% if (grn.po_date) { %>
                                    <div style="display: flex;">
                                      <div style="width: 150px;">PO Date</div>
                                      <div style="width: 150px;">
                                        <%= new Date().formatDateInTimezone(grn?.po_date) %>
                                      </div>
                                    </div>
                                  <% } %>
                                  
                                  <% if (grn.grn_due_date) { %>
                                    <div style="display: flex;">
                                      <div style="width: 150px;">Payment Due Date</div>
                                      <div style="width: 150px;">
                                        <%= new Date().formatDateInTimezone(grn?.grn_due_date) %>
                                      </div>
                                    </div>
                                  <% } %>
                            </td>
                            <td colspan="2" style="line-height: 13px; padding: 4px 5px; vertical-align: top; width: 50%; font-size: 10px; font-weight: 700;">
                               
                                <%- await render_custom_fields(grn.custom_fields,'SECTION-A', 'GOOD_RECEIVING_NOTES' ,grn.org_id , ` <div style="display: flex;">
                                      <div style="width: 150px;">{{field_name}}</div>
                                      <div style="width: 150px;">{{field_value}}</div>
                                      </div>
                                      `)%>
                                    <% if(grn?.document_tenant_configuration?.print_labels && grn?.tags) {%>
                                        <div style="display: flex; font-size: 10px; font-weight: 700; ">
                                            <div style="width: 150px;"><%= grn?.document_tenant_configuration?.label_name_input%></div>
                                            <div style="width: 150px;"><%= grn?.tags?.join(', ') %></div>
                                        </div>
                                    <%}%>
            </td>
        </tr>
        <tr style="border: 1px solid black; border-bottom: none; background-color: #efefef;">
            <%if(grn?.tenant_seller_info) {%>
            <td colspan="2" style="font-weight: bold; padding: 2px 5px; font-size: 10px;width: 50%;">Bill From</td>
            <%}else{%>
            <td colspan="2" style="font-weight: bold; padding: 5px; font-size: 10px;width: 50%;"></td>
            <%}%>
            <%if(grn?.stock_transfers){%>
                <td colspan="2" style="font-weight: bold; margin: auto; padding: 5px; border-left: 1px solid black; font-size: 10px;width: 50%;">Source Unit</td>
             <%}%>
            <td colspan="2" style="font-weight: bold; margin: auto; padding: 4px 5px; border-left: 1px solid black; font-size: 10px; width: 50%;">Bill To</td>
        </tr>
        <tr>
            <% if(grn?.tenant_seller_info?.seller_name) { %>
            <td colspan="2" style="border-top: 1px solid black;padding: 4px 5px;  width: 50%; line-height: 13px;font-size: 10px;vertical-align: top">
                <div style=" font-weight: bold;">
                    <%= grn?.tenant_seller_info?.seller_name %>
                </div>
                <% if(grn?.seller_address_info?.address1 !=null &&
                    (grn.seller_address_info.address1).trim().length>0){ %>
                        <div >
                            <%= grn?.seller_address_info?.address1 || "" %>
                        </div>
                    <% } %>
                <% if(grn?.seller_address?.city && grn?.seller_address?.state) {%>
                    <div >
                        <%= grn?.seller_address?.city %>, <%= grn?.seller_address?.state %>
                    </div>
                    <%}%>
                <% if(grn?.seller_address?.postal_code) {%>
                    <div >
                        <%= grn?.seller_address?.postal_code %>
                    </div>
                    <% } %>
                <% if (grn?.tenant_seller_info?.email_id_1 ) { %>
                    <div ><b>Email:</b>
                        <%= grn?.tenant_seller_info?.email_id_1||""  %>
                    </div>
                    <% } %>
                <% if (grn?.tenant_seller_info?.mobile_1) { %>
                    <div ><b>Phone:</b>
                        <%= grn?.tenant_seller_info?.mobile_1  %>
                    </div>
                    <% } %>
                <% if (grn?.tenant_seller_info?.gst_number !=null && (grn?.tenant_seller_info?.gst_number).trim().length>0) { %>
                    <div ><b>GSTIN:</b>
                        <%= grn?.tenant_seller_info?.gst_number  %>
                    </div>
                    <% } %>
                <% if (grn?.tenant_info?.identification_number?.filter(item=>
                item?.is_printable)?.length) { %>
                <% grn?.tenant_info?.identification_number?.filter(item=>
                        item?.is_printable)?.map(item => { %>
                        <div>
                            <b>
                            <%=item?.identification_name %>:
                            </b>
                            <%=item?.identification_number || "" %>
                        </div>
                    <% })%>
                <% }%>
            </td>
            <%} else {%>
                <td colspan="2" style="border-top: 1px solid black;padding: 5px;  width: 50%; line-height: 15px;font-size: 10px;vertical-align: top">
                </td>
                <% } %>
                <%if(grn?.stock_transfers){%>
                <% source_department_info=grn?.stock_transfers[0]?.source_department_info %>
                <td colspan="2"
                    style="border-top: 1px solid black; padding: 5px;border-left: 1px solid black; line-height: 15px;font-size: 10px;vertical-align: top;width: 50%;">
                    <div style="font-weight: bold;">
                    <%= source_department_info?.tenant_name||  "" %>
                    </div>
                    <div >
                    <%= source_department_info?.tenant_shipping_address_info?.address1 %>
                    </div>
                    <div >
                    <%= source_department_info?.tenant_shipping_address_info?.city %>,
                    <%=source_department_info?.tenant_shipping_address_info?.state %>
                    </div>
                    <% if(source_department_info?.tenant_shipping_address_info?.postal_code) {%>
                    <div >
                        <%= source_department_info?.tenant_shipping_address_info?.postal_code %>
                    </div>
                    <% } %>
                </td>
                <%}  else {%>
                <td colspan="2" style="border-top: 1px solid black;padding: 5px;  width: 50%; line-height: 15px;font-size: 10px;vertical-align: top;border-left: 1px solid black;">
                    <div style=" font-weight: bold;">
                        <%= grn?.tenant_info?.legal_name || grn?.tenant_info?.tenant_name || "" %>
                    </div>
                    <% if(grn?.tenant_billing_address?.address1 !=null &&
                            (grn.tenant_billing_address.address1).trim().length>0){ %>
                    <div >
                        <%= grn?.tenant_billing_address?.address1 || "" %>
                    </div>
                    <% } %>
                    <% if(grn?.tenant_billing_address?.city && grn?.tenant_billing_address?.state) {%>
                    <div >
                        <%= grn?.tenant_billing_address?.city %>, <%= grn?.tenant_billing_address?.state %>
                    </div>
                    <%}%>
                    <% if(grn?.tenant_billing_address?.postal_code) {%>
                    <div >
                        <%= grn?.tenant_billing_address?.postal_code %>
                    </div>
                    <% } %>
                </td>
                <%}%>
        </tr>
            </table>
                </div>
                <% const sellerType = grn?.tenant_seller_info?.seller_type || grn?.seller_type %>
                <table style="width: 100%; border-collapse: collapse; border-bottom: 1px solid black">
                    <thead>
                    <tr style="background-color: #efefef; border: 1px solid black; border-bottom: none">
                        <th
                                style="text-align: left; font-size: 10px; padding: 4px 5px;width: 40px;">
                            S No.
                        </th>
                        <th
                                style="text-align: left;font-size: 10px; padding: 4px 5px; border-left: 1px solid black; ">
                            Item & Description</th>
                        <th
                                style="text-align: left;font-size: 10px; padding: 4px 5px;border-left: 1px solid black;">
                            HSN/SAC</th>
                        <th
                                style="text-align: left;font-size: 10px; padding: 4px 5px;border-left: 1px solid black;">
                            Quantity</th>
                        <% if (grn?.allow_grn_to_create_ap_invoice) { %>
                            <th style="text-align: left;font-size: 10px; padding: 4px 5px;border-left: 1px solid black;">
                                Invoice Quantity
                            </th>
                        <% } %>
                        <th
                                style="text-align: left;font-size: 10px; padding: 4px 5px;border-left: 1px solid black;">
                            Unit</th>
                            <% if (uniqueFields?.length > 0) { %>
                                <% uniqueFields.forEach(field => { %>
                                    <th style="text-align: left; font-size: 10px; padding: 4px 5px; border-left: 1px solid black;">
                                        <%= field.field_name %>
                                    </th>
                                <% }) %>
                            <% } %>
                        <% if (!grn?.allow_grn_to_create_ap_invoice) { %>
                            <th style="text-align: left;font-size: 10px; padding: 4px 5px;border-left: 1px solid black;">
                                Rate</th>
                                <% if(grn.discount_amount !== 0) {%>
                                    <th
                                            style="text-align: left;font-size: 10px; padding: 5px;border-left: 1px solid black; ">
                                    Discount</th>
                                <%}%>
                            <% if (!(hide_tax_for_overseas && sellerType === 'OVERSEAS')) { %>
                                <% if(grn?.document_tenant_configuration?.print_tax == true || grn?.document_tenant_configuration?.print_tax == 'true') {%>
                                    <% if (uniqueChildTaxes?.length) { %>
                                        <% uniqueChildTaxes.forEach((tax, index) => { %>
                                            <th colspan="4" 
                                            style="text-align: center;font-size: 10px;border-left: 1px solid black;width: 150px; padding: 0px">
                                            <div style="padding: 4px 5px;">
                                                <%= tax?.tax_type_name  %>
                                            </div>
                                            <table width="100%"
                                                style="text-align: left;border-top: 1px solid black;height: 100%; border-spacing: 0px">
                                                <tr>
                                                    <th style="text-align: center;font-size: 10px; border-right: 1px solid black; width: 30%">%</th>
                                                    <th style="text-align: center;font-size: 10px; padding: 5px">Amount</th>
                                                </tr>
                                            </table>
                                            </th>
                                        <% }) %>
                                    <% } %>  
                                    <%}%>
                                <% if(grn.document_tenant_configuration?.print_tax == false || grn.document_tenant_configuration?.print_tax == 'false') {%>

                                    <th style="text-align: left;font-size: 10px; padding: 4px 5px;border-left: 1px solid black;">
                                        Tax
                                    </th>
                                <%} %>
                            <% } %>
                            <th
                                    style="text-align: left;font-size: 10px; padding: 4px 5px;border-left: 1px solid black;">
                                Amount
                            </th>
                        <% } %>
                    </tr>
                    </thead>
                    <tbody>
                    <% grn?.grn_lines.forEach(function(product,index) {%>
                        <tr style="page-break-inside: avoid;">
                            <td style="border-top: 1px solid black;font-size: 10px; padding: 4px 5px;">
                                <%= index+1 %>
                            </td>
                            <td style="border-top: 1px solid black; font-size: 10px; border-left: 1px solid black;padding: 4px 5px; font-weight: 600px;">
                                <% if((grn?.document_tenant_configuration?.print_product_images == true ||grn?.document_tenant_configuration?.print_product_images == 'true')  && product?.product_sku_info?.assets?.[0]?.url) {%>
                                    <div style="display: flex;align-items: center;">
                                        <div style="border: 1px solid grey;margin-right: 2px;">
                                            <img
                                                    src="<%= product?.product_sku_info?.assets?.[0]?.url %>"
                                                    width="50px" height="50px">
                                        </div>
                                        <div>
                                            <% if((grn?.document_tenant_configuration?.print_item_code==true
                            ||grn?.document_tenant_configuration?.print_item_code=='true' ) &&
                            product?.product_sku_info?.ref_product_code) {%>
                                <div style="font-size: 10px;  font-weight: 500; margin-bottom: 2px;">
                              <%= product?.product_sku_info?.ref_product_code %>
                            </div>
                            <% } %>
                              <% if((grn?.document_tenant_configuration?.print_internal_sku_code==true
                                ||grn?.document_tenant_configuration?.print_internal_sku_code=='true' ) &&
                                product?.product_sku_info?.internal_sku_code) {%>
                                <div style="font-size: 10px;  font-weight: 500; margin-bottom: 2px;">
                                  <%= product?.product_sku_info?.internal_sku_code %>
                                </div>
                                <% } %>
                                            <div style="font-weight: bold;padding-bottom: 2px;"> <%= product?.product_sku_info?.product_sku_name %> <br>
                                            </div>
                                        </div>
                                    </div>
                                <%} else {%>
                                    <% if((grn?.document_tenant_configuration?.print_item_code==true
                                    ||grn?.document_tenant_configuration?.print_item_code=='true' ) &&
                                    product?.product_sku_info?.ref_product_code) {%>
                                        <div style="font-size: 10px;  font-weight: 500; margin-bottom: 2px;">
                                      <%= product?.product_sku_info?.ref_product_code %>
                                    </div>
                                    <% } %>
                                      <% if((grn?.document_tenant_configuration?.print_internal_sku_code==true
                                        ||grn?.document_tenant_configuration?.print_internal_sku_code=='true' ) &&
                                        product?.product_sku_info?.internal_sku_code) {%>
                                        <div style="font-size: 10px;  font-weight: 500; margin-bottom: 2px;">
                                          <%= product?.product_sku_info?.internal_sku_code %>
                                        </div>
                                        <% } %>
                                    <div style="font-weight: bold;padding-bottom: 2px;"> <%= product?.product_sku_info?.product_sku_name %> <br>
                                    </div>
                                <%} %>
                                <% if( product?.product_sku_info?.product_category_info ) { %>
                                    <% if((grn?.document_tenant_configuration?.print_category_status =='Child Category' )) {%>
                                        <div><span style="font-weight:bold ;">Category - </span> <%= product?.product_sku_info?.product_category_info?.product_category_name %></div>
                                    <% } %>
                                    <% if((grn?.document_tenant_configuration?.print_category_status =='Parent Category' )) {%>
                                        <div><span style="font-weight: bold;">Category - </span>  <%= product?.product_sku_info?.product_category_info?.category_path?.[0] %></div>
                                    <% } %>
                                    <% if((grn?.document_tenant_configuration?.print_category_status =='Full Category Path' )) {%>
                                        <div><span style="font-weight:bold ;">Category - </span>  <%= product?.product_sku_info?.product_category_info?.category_path?.join('/') %></div>
                                    <% } %>
                                <% } %>
                                <% if(product.remarks)  { %>
                                    <div> <%- product.remarks %> <br>
                                    </div>
                                <% } %>
                                <div>
                                    <% if (product?.product_batches?.length > 0 && (grn?.document_tenant_configuration?.print_batch_information == true || grn?.document_tenant_configuration?.print_batch_information == 'true')) { %>
                                        <% product?.product_batches.forEach(function(pbtch) { %>
                                            <div style="line-height: 13px;font-size: 9px;">
                                                <% if (pbtch?.custom_batch_number) { %>
                                                    <div style="display: flex;">
                                                        <div style="font-weight: bold;">Batch</div>
                                                        <div>#&nbsp;<%= pbtch?.custom_batch_number %></div>
                                                    </div>
                                                <% } %>
                                                <% if(pbtch?.lot_number){ %>
                                                    <div style="display: flex;">
                                                        <div style="font-weight: bold;"> Lot 
                                                        </div> #&nbsp;<%= pbtch?.lot_number %>
                                                        </div>
                                                    </div>
                                                <% } %>
                                                <% if(pbtch?.expiry_date &&(pbtch?.expiry_date< '2099-01-01' )){ %>
                                                <div style="display: flex;">
                                                    <div style="font-weight: bold;">Expires on &nbsp;</div>
                                                    <div>
                                                        <%= new Date(pbtch?.expiry_date).customFormat(product?.product_sku_info?.expiry_date_format) %>
                                                    </div>
                                                </div>
                                                <% } %>
                                                <% if(pbtch?.manufacturing_date){ %>
                                                    <div style="display: flex;">
                                                        <div style="font-weight: bold;">Mfg Date &nbsp;</div>
                                                        <div>
                                                            <%= new Date(pbtch?.manufacturing_date).customFormat(product?.product_sku_info?.manufacturing_date_format) %>
                                                        </div>
                                                    </div>
                                                    <% } %>
                                                <div style="display: flex;">
                                                    <div style="font-weight: bold;"> Quantity &nbsp;</div>
                                                    <div>
                                                        <%= toFixedQuantity(pbtch.quantity, product?.uom_info?.[0]?.precision) %> <%= product?.uom_info?.[0]?.uqc?.toProperCase()|| 'Nos' %>
                                                    </div>
                                                </div>
                                                <% if (pbtch?.custom_fields?.length >0) { %>
                                                    <% let batchCustomFields = pbtch?.custom_fields; %>
                                                    <% batchCustomFields?.forEach(field => { %>
                                                        <% if((field?.is_printable ==true || field?.is_printable=='true') && (field?.is_active ==true || field?.is_active=='true') ) { %> 
                                                            <div style="display: flex;">
                                                                <div style="font-weight: bold;"> <%= field?.field_name %> &nbsp;</div>
                                                                <% if(field?.field_type == 'DATE' && field?.field_value?.includes('T')) { %> 
                                                                    <div><%= field?.field_value?.split('T')?.[0]%></div>
                                                                <% } else { %>
                                                                <div><%= field?.field_value %></div>
                                                                <% } %>
                                                            </div>
                                                        <%} %>
                                                    <% }) %>
                                                <% } %>
                                            </div>
                                        <% }) %>
                                    <% } %>
                                </div>
                            </td>
                            <td style="border-top: 1px solid black; font-size: 10px; border-left: 1px solid black; padding: 4px 5px;">
                                <%= product.product_sku_info.hsn_code ||"" %>
                            </td>
                            <td style="border-top: 1px solid rgb(35, 26, 26); font-size: 10px; border-left: 1px solid black; padding: 4px 5px;">
                                <%= toFixedQuantity(product.received_qty, product?.uom_info?.[0]?.precision) %>
                                <% if (product.secondary_uom_qty) { %>
                                    <br>
                                    <br>
                                    <span style="color: #888;">
                                      <%= toFixedQuantity(product.secondary_uom_qty, product?.product_sku_info?.secondary_uom_info?.precision) %>
                                    </span> 
                                 <% } %>         
                            </td>
                            <% if (grn?.allow_grn_to_create_ap_invoice) { %>
                                <td style="border-top: 1px solid rgb(35, 26, 26); font-size: 10px; border-left: 1px solid black; padding: 4px 5px;">
                                    <%= toFixedQuantity(product?.invoice_quantity, product?.uom_info?.[0]?.precision) %>
                                </td>
                            <% } %>
                            <td style="border-top: 1px solid black; font-size: 10px; border-left: 1px solid black; padding: 4px 5px;">
                                <%= product.uom_info?.[0]?.uqc ||"" %>
                                <% if (product.secondary_uom_qty) { %>
                                    <br>
                                    <br>
                                    <span style="color: #888;">
                                      <%= product?.product_sku_info?.secondary_uom_info?.uqc || 'Nos' %>
                                    </span>
                                    <% } %>
                            </td>
                            <% if (product?.grn_line_custom_fields?.length > 0 && uniqueFields?.length > 0) { %>
                                <% let ilCustomFields = product.grn_line_custom_fields; %>
                                <% uniqueFields.forEach(field => { %>
                                    <% let matchingRow = ilCustomFields.find(row => row.cf_id === field.cf_id); %>
                                    <% if(matchingRow?.field_type == 'DATE' && matchingRow?.field_value?.includes('T')){ %>
                                        <td style="border-top: 1px solid black; font-size: 10px; border-left: 1px solid black; padding: 4px 5px;"><%= matchingRow?.field_value?.split('T')?.[0] || '-'  %></td>
                                      <% } else {%>
                                        <td style="border-top: 1px solid black; font-size: 10px; border-left: 1px solid black; padding: 4px 5px;"><%= matchingRow?.field_value || '-' %></td>
                                      <%} %>
                                <% }); %>
                            <% } %>
                            <% if (!grn?.allow_grn_to_create_ap_invoice) { %>
                                <td style="border-top: 1px solid black; font-size: 10px; border-left: 1px solid black; padding: 5px;">
                                    <%= applyUserPolicy(product?.offer_price, grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                    </td>                
                                    <% if(grn.discount_amount !== 0) {%>
                                    <td style="border-top: 1px solid black; font-size: 10px; border-left: 1px solid black; padding: 5px;">
                                        <%= product?.line_discount_percentage || 0 %>%
                                    </td>
                                <%}%>
                                <% if (!(hide_tax_for_overseas && sellerType === 'OVERSEAS')) { %>
                                    <% if(grn.document_tenant_configuration?.print_tax == true || grn.document_tenant_configuration?.print_tax == 'true') {%>
                                        <% if (uniqueChildTaxes?.length) { %>
                                            <% uniqueChildTaxes.forEach((tax, index) => { %>
                                                <td colspan="4" 
                                                    style="text-align: center; font-size: 10px; border-left: 1px solid black; border-top: 1px solid black; padding: 0px">
                                                    <table width="100%" style="height: 100%; border-spacing: 0px">
                                                        <tr>
                                                            <td style="font-size: 10px; text-align: center; padding-left: 3px; border-right: 1px solid black; width: 30%; word-wrap: break-word;">
                                                                <% 
                                                                    const matchingTax = product?.tax_info?.child_taxes?.find(t => t.tax_type_name === tax.tax_type_name);
                                                                    if (matchingTax) { 
                                                                %>
                                                                    <%= matchingTax.tax_value %>
                                                                <% } else { %> 
                                                                    -
                                                                <% } %>
                                                            </td>
                                                            <td style="font-size: 10px; text-align: center; padding-left: 3px; word-wrap: break-word;">
                                                                <% if (matchingTax?.tax_amount) { %>
                                                                    <%= applyUserPolicy(product?.tax_info?.child_taxes[0]?.tax_amount, grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                                                <% } else { %>
                                                                    -
                                                                <% } %>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            <% }) %>
                                        <% } %>   
                                    <%}%>
                                    <% if(grn.document_tenant_configuration?.print_tax == false || grn.document_tenant_configuration?.print_tax == 'false') {%>
                                        <td style="border-top: 1px solid black; text-align: left;font-size: 10px; padding: 4px 5px;border-left: 1px solid black;">
                                            <%= product?.tax_info?.tax_value %>%
                                        </td>
                                    <%} %>
                                <% } %>
                                <td
                                        style="border-top: 1px solid black; font-size: 10px; border-left: 1px solid black; padding: 4px 5px;">
                                        <% var line_price=product.received_qty * product.offer_price %>
                                        <%line_price -=product?.line_discount_amount || 0%>
                                        <% var productFinalPrice=line_price * (1+(product?.tax_info?.tax_value/100)) %>
                                        <%= applyUserPolicy(productFinalPrice, grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                </td>
                            <% } %>
                        </tr>
                    <% }) %>
                    <% if(grn?.freight_tax_info && grn?.charge_1_value > 0) {%>
                        <tr style="page-break-inside: avoid;">
                            <td style="border-top: 1px solid black;" />
                            <td style="border-top: 1px solid black; font-size: 10px; border-left: 1px solid black;padding: 4px 5px; font-weight: 600;">
                                Freight
                            </td>
                            <td style="border-top: 1px solid black; border-left: 1px solid black; font-size: 10px; padding: 5px">
                                <%= grn?.freight_sac_code %>
                            </td>
                            <td style="border-top: 1px solid black; border-left: 1px solid black; font-size: 10px; padding: 5px" />
                            <% if (grn?.allow_grn_to_create_ap_invoice) { %>
                                <td style="border-top: 1px solid black; border-left: 1px solid black; font-size: 10px; padding: 5px" />
                            <% } %>
                            <td style="border-top: 1px solid black; border-left: 1px solid black; font-size: 10px; padding: 5px" />
                              <% uniqueFields.forEach(field => { %>
                                <td style="border-top: 1px solid black; font-size: 10px; border-left: 1px solid black; padding: 4px 5px;">
                                </td>
                            <% }); %>
                            <% if (!grn?.allow_grn_to_create_ap_invoice) { %>
                                <td style="border-top: 1px solid black; font-size: 9px; border-left: 1px solid black; padding: 4px 5px;">
                                    <%= renderCurrency(grn?.charge_1_value, grn.currency_code,grn?.organisation_info?.global_config?.settings?.price_precision || 2) %>
                                </td>
                                <% if(grn.discount_amount !== 0) {%>
                                    <td style="border-top: 1px solid black; font-size: 9px; border-left: 1px solid black; padding: 4px 5px;" />
                                <% } %>
                                <% if (!(hide_tax_for_overseas && sellerType === 'OVERSEAS')) { %>
                                    <% if(grn?.document_tenant_configuration?.print_tax == true || grn?.document_tenant_configuration?.print_tax == 'true') {%>
                                        <% if(grn?.freight_tax_info?.child_taxes?.length==1) { %>
                                        <td colspan="4"
                                            style="text-align: center;font-size: 9px;border-left: 1px solid black;border-top: 1px solid black; padding: 0px">
                                            <table width="100%" style="height: 100%; border-spacing: 0px">
                                            <tr>
                                                <td style="font-size: 9px; text-align: center; padding-left: 3px; border-right: 1px solid black; width: 30%; word-wrap: break-word;">
                                                <%= grn?.freight_tax_info?.child_taxes[0]?.tax_value %>
                                                </td>
                                                <td style="font-size: 9px; text-align: center; padding-left: 3px;  word-wrap: break-word;">
                                                <%= formatCurrency(grn?.freight_tax_info?.child_taxes[0]?.tax_amount , grn.currency_code)%>
                                                </td>
                                            </tr>
                                            </table>
                                        </td>
                                        <% } %>
                                        <% if(grn?.freight_tax_info?.child_taxes?.length==2) { %>
                                        <td colspan="4"
                                            style="text-align: center;font-size: 9px;border-left: 1px solid black;border-top: 1px solid black; padding: 0px">
                                            <table width="100%" style="height: 100%; border-spacing: 0px">
                                            <tr>
                                                <td style=" font-size: 9px; text-align: center; border-right: 1px solid black;width: 30%;vertical-align: top">
                                                <%= grn?.freight_tax_info?.child_taxes[0]?.tax_value %>
                                                </td>
                                                <td style=" font-size: 9px; text-align: center;vertical-align: top">
                                                <%= formatCurrency(grn?.freight_tax_info?.child_taxes[0]?.tax_amount , grn?.currency_code)%>
                                                </td>
                                            </tr>
                                            </table>
                                        </td>
                                        <td colspan="4"
                                            style="text-align: center;font-size: 9px;border-left: 1px solid black; border-top:1px solid black; padding: 0px">
                                            <table width="100%" style="height: 100%; border-spacing: 0px">
                                            <tr>
                                                <td style=" font-size: 9px; text-align: center; border-right: 1px solid black;width: 30%;vertical-align: top">
                                                <%= grn?.freight_tax_info?.child_taxes[1]?.tax_value %>
                                                </td>
                                                <td style=" font-size: 9px; text-align: center;vertical-align: top">
                                                <%= formatCurrency(grn?.freight_tax_info?.child_taxes[1]?.tax_amount , grn.currency_code)%>
                                                </td>
                                            </tr>
                                            </table>
                                        </td>
                                        <%}%>
                                        <% } %>
                                    <% if(grn.document_tenant_configuration?.print_tax == false || grn.document_tenant_configuration?.print_tax == 'false') {%>
                                        <td style="border-top: 1px solid black; text-align: left;font-size: 10px; padding: 4px 5px;border-left: 1px solid black;">
                                            <%= grn?.freight_tax_info?.tax_value %>%
                                        </td>
                                    <%} %>
                                <% } %>
                                <td style="border-top: 1px solid black; font-size: 10px; border-left: 1px solid black; padding: 4px 5px;">
                                    <%= formatCurrency(grn?.charge_1_value * (1 + (grn?.freight_tax_info?.tax_value / 100)), grn.currency_code) %>
                                </td>
                            <% } %>
                        </tr>
                    <% } %>
                    </tbody>
                </table>
            </td>
        </tr>
        <% if(grn.document_tenant_configuration?.print_sub_total_section_every_page == true || grn.document_tenant_configuration?.print_sub_total_section_every_page == 'true') { %>
            <tfoot>
                <tr>
                    <td style="padding: 0px">
                        <div class="footer-space" style="height: <%= footerHeight > 200 ? footerHeight : 200 %>px !important;" />
                    </td>
                </tr>
            </tfoot>
        <%}%>
    </table>
    <% if(grn.document_tenant_configuration?.print_sub_total_section_every_page == true || grn.document_tenant_configuration?.print_sub_total_section_every_page == 'true') { %>
        <div class="footer" style="border-bottom: 1px solid black; height: <%= footerHeight > 200 ? footerHeight : 200 %>px; margin-top: 10px">
    <%} else {%>
        <div style="border-bottom: 1px solid black; margin-top: 10px;page-break-inside: avoid;">
    <%} %>
        <table class="table-container-inner" style="border-top: 1px solid black;">
            <tr>
                <td colspan="3" style="padding: 4px 7px; vertical-align: top; width:75%;">
                    <div style="line-height: 13px">
                        <% if (!grn?.allow_grn_to_create_ap_invoice) { %>
                            <span style="font-size: 10px; margin-right: 5px">Total In Words: </span>
                            <span style="font-weight: bold; font-size: 10px;">
                                <% if(grn?.hide_cost_price == true || grn?.hide_cost_price == 'true') { %>
                                    XXXXXX
                                <% } else { %>
                                    <%= totalInWords(grn?.grn_grand_total, grn?.org_currency_info?.currency_code || 'INR') || "Zero" %>
                                <% } %>
                            </span>
                        <% } %>
                    </div>
                </td>
                <!-- calculation -->
                <td colspan="1" style="border-left: 1px solid black; font-size: 10px; vertical-align: top; padding: 0;">
                    <% if (!grn?.allow_grn_to_create_ap_invoice) { %>
                        <div style="padding: 4px 5px; line-height: 13px">
                            <div style="width: 100%; display: flex;">
                                <div style="font-weight: 600; width: 110px; ">Sub Total</div>
                                <div style="padding-right: 5px;width: 90px;">
                                    <%= applyUserPolicy((grn?.grn_base_price + (grn.freight_tax_id ? grn.charge_1_value: 0 )), grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>                           
                                </div>
                            </div>
                            <% if(grn.discount_amount !== 0) {%>
                                <div style="width: 100%; display: flex;">
                                    <div style="font-weight: 600; width: 110px; ">Discount </div>
                                    <div style="padding-right: 5px;width: 90px; margin-left: -11.5px">
                                        <%= applyUserPolicy(grn?.discount_amount , grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                    </div>
                                </div>
                            <% } %>
                            <%var grn_charges=grn.other_charges ?? []%>
                            <% grn_charges.forEach(function(charges) { %>
                                <% if(charges?.tax_info) { %>
                                <div style="width: 100%; display: flex;">
                                <div style="font-weight: 600px; width: 110px; padding-bottom: 3px;">
                                    <%= charges.charge_name%>
                                </div>
                                <div style="padding-right: 10px;width: 90px;  ">
                                    <%= formatCurrency(charges.charge_amount,grn.currency_code) %>
                                </div>
                                </div>
                                <% } %>
                            <%})%>
                            <% if (!(hide_tax_for_overseas && sellerType === 'OVERSEAS')) { %>
                                <% grn.tax_info.forEach(function(tax){%>
                                    <div style="width: 100%; display: flex;">
                                        <div style="font-weight: 600; width: 110px;">
                                            <%= tax.tax_type_name %>
                                        </div>
                                        <div style="padding-right: 5px; width: 90px; ">
                                            <%= applyUserPolicy(tax?.tax_amount, grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                        </div>
                                    </div>
                                <%})%>
                            <% } %>
                            <% if(grn?.tcs_info){ %>
                                <div style="width: 100%; display: flex;">
                                    <div style="font-weight: 600; width: 110px;">TCS</div>
                                    <div style="padding-right: 5px; width: 90px; ">
                                        <%= formatCurrency(grn.tcs_info.tcs_amount, grn.currency_code) %>
                                    </div>
                                </div>
                            <% }%>
                            <% if(grn?.tds_info){ %>
                                <div style="width: 100%; display: flex;">
                                    <div style="font-weight: 600; width: 110px;">
                                        TDS
                                    </div>
                                    <div style="padding-right: 5px;  width: 90px; margin-left: -3px">
                                        -<%= formatCurrency(grn.tds_info.tds_amount, grn.currency_code) %>
                                    </div>
                                </div>
                            <% }%>
                            <%var grn_charges=grn.other_charges ?? []%>
                            <% grn_charges.forEach(function(charges) { %>
                                <% if(!charges?.tax_info) { %>
                                <div style="width: 100%; display: flex;">
                                <div style="font-weight: 600px; width: 110px; padding-bottom: 3px;">
                                    <%= charges.charge_name%>
                                </div>
                                <div style="padding-right: 10px;width: 90px;  ">
                                    <%= formatCurrency(charges.charge_amount,grn.currency_code) %>
                                </div>
                                </div>
                                <% } %>
                            <%})%>
                            <% if (grn.charge_1_name && parseFloat(grn.charge_1_value) !== 0 && !grn?.freight_tax_info) { %>
                                <div style="width: 100%; display: flex;">
                                    <div style="font-weight: 600; width: 110px;">
                                        <%= grn.charge_1_name %>
                                    </div>
                                    <div style="padding-right: 5px;width: 90px;  ">
                                        <%= formatCurrency(grn.charge_1_value,grn.currency_code) %>
                                    </div>
                                </div>
                            <% } %>
                            <% if (grn.charge_2_name && parseFloat(grn.charge_2_value) !== 0 && !grn?.freight_tax_info) { %>
                                <div style="width: 100%; display: flex;">
                                    <div style="font-weight: 600; width: 110px;">
                                        <%= grn.charge_2_name %>
                                    </div>
                                    <div style="padding-right: 5px;width: 90px;  ">
                                        <%= formatCurrency(grn.charge_2_value,grn.currency_code) %>
                                    </div>
                                </div>
                            <% } %>
                            <% if (parseFloat(grn.grn_round_off) !== 0) { %>
                                <div style="width: 100%; display: flex;">
                                    <div style="font-weight: 600; width: 110px;">
                                        Round Off </div>
                                    <div style="padding-right: 5px;width: 90px;  ">
                                        <%= formatCurrency(grn.grn_round_off,grn?.currency_code) %>
                                    </div>
                                </div>
                            <% } %>
                            <div style="width: 100%; display: flex;">
                                <div style="font-weight: 600; width: 110px;">
                                    Grand Total</div>
                                <div style="padding-right: 5px;width: 90px;  ">
                                    <%= applyUserPolicy(grn?.grn_grand_total , grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                </div>
                            </div>
                            <% if(Number(grn?.total_payment_made) > 0){ %>
                                <div style="width: 100%; display: flex;">
                                    <div style="font-weight: 600; width: 110px;">
                                        Payment Made
                                    </div>
                                    <div style="padding-right: 5px;width: 90px;">
                                        <% if(grn?.total_payment_made){ %>
                                            (-)
                                        <% } %>
                                        <%= applyUserPolicy(grn?.total_payment_made , grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                    </div>
                                </div>
                            <%}%>
                            <% if(Number(grn?.debit_note_info[0].document_db_total) > 0) {%>
                                <div style="width: 100%; display: flex;">
                                <div style="font-weight: 600px; width: 110px;padding-bottom: 3px;">
                                    Credits Applied
                                </div>
                                <div style="padding-right: 10px;width: 90px;  ">
                                    <% if(grn?.debit_note_info[0].document_db_total){ %>
                                    (-)
                                    <% } %>
                                    <%= applyUserPolicy(grn?.debit_note_info[0].document_db_total , grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                </div>
                                </div>
                            <% }%>
                        </div>
                        <div style="display: flex; padding: 4px 5px; border-top:1px solid black;  <%= grn.document_tenant_configuration?.print_sub_total_section_every_page ? 'border-bottom: 1px solid black;' : '' %>">
                            <div style="font-weight: bold; width: 110px;">Total Due</div>
                            <div style="padding-right: 5px; width: 90px;">
                                <%= applyUserPolicy((grn?.grn_grand_total - grn?.debit_note_info[0]?.document_db_total - grn?.total_payment_made) , grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                            </div>
                        </div>
                    <% } %>
                    <% if(grn?.document_tenant_configuration?.print_signature_input) {%>
                        <div style="text-align: center; padding-bottom: 5px; border:1px solid black; border-width: 1px 0px 1px 0px; border-top:0px solid black;">
                            <div style="text-align: center;padding-top: 5px; font-size: 9px"><%= grn?.document_tenant_configuration?.signature_name %></div>
                            <% if(grn?.document_tenant_configuration?.doc_sign_base_64) {%>
                                <div style="height: 35px;">
                                    <img style="height:30px; width:200px; object-fit: contain; margin-bottom:10px;"
                                         src="<%= grn.document_tenant_configuration?.doc_sign_base_64 %>"
                                         alt="" />
                                </div>
                            <% }else{ %>
                                <div style="width: 200px;height: 34px;"></div>
                            <%} %>
                            <p style="margin: unset; font-size: 9px;">Authorized Signatory</p>
                        </div>
                    <%}%>
                </td>
            </tr>
        </table>
    </div>
    <% if(grn?.remark){ %>
        <table class="table-container" style="border-top: 0px solid black;">
        <tr>
            <td colspan="4" style="border-left: 1px solid black; border-bottom: 1px solid black;font-size: 10px; vertical-align: top;border-right: 1px solid black;padding:5px 5px;">
                    <div style="font-weight: bold; font-size: 10px;">REMARKS</div>
                      <% grn?.remark?.split("\n").forEach(i=> {%>
                            <div style="font-size: 9px;padding: 2px;"><%=i %></div>
                        <%}) %>
            </td>
        </tr>
        </table>
    <% } %>
</body>

</html>