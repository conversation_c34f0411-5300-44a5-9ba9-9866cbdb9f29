<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Procuzy</title>
    <style>
        @media print {
            .move-to-next-page {
                page-break-after: always;
            }
        }

        @media print {
            .div-to-next-page {
                page-break-inside: avoid;
            }
        }

        @media print {
            .footer-text {
                display: none;
            }

            .footer-text:last-child {
                display: block;
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                text-align: center;
                padding: 10px;
            }
        }
    </style>
</head>

<body style="overflow-x: hidden; width: 100%; font-family: Arial; padding: 0px; margin: 0">
    <% let uniqueFields = collectUniqueFields(grn.grn_lines, 'grn_line_custom_fields') %> 

    <div style="line-height: 15px">
        <% if(grn?.document_tenant_configuration?.print_prepared_by == true || grn?.document_tenant_configuration?.print_prepared_by == 'true') {%>
          <div style="text-align: right; padding-right: 30px;font-size: 10px">
            <% if(grn?.created_by) {%>
              Prepared By: <%= grn?.created_by_info?.first_name + grn?.created_by_info?.last_name%>
            <%} %>
          </div>
        <%} %>
      
        <% if(grn?.document_tenant_configuration?.print_prepared_at == true || grn?.document_tenant_configuration?.print_prepared_at == 'true') {%>
          <div style="text-align: right; padding-right: 30px;font-size: 10px">
            <% if(grn?.created_at) {%>
              Prepared At: <%= new Date().formatDateTime(grn?.created_at) %>
            <%} %>
          </div>
        <%} %>
      </div>
    <% if(grn.beautify) { %>
        <div
            style="padding-right: 30px; width: <%= grn.width%>;margin: <%= grn.margin%>;box-shadow: <%= grn.shadow%>;padding-left: 30px; padding-top: 0px; padding-bottom: 0px;">
            <% } else {%>
                <div style="padding-right: 30px; padding-left: 30px; padding-top: 0px; padding-bottom: 0px;">
                    <% } %>
                        <div
                            style="font-size: 26px; color: <%= grn.tenant_configuration.doc_primary_colour%>; padding: 20px 0px 20px 0px">
                            <%= grn?.document_tenant_configuration?.document_title || 'Goods Receiving Note' %> </div>
                        <div style="display: flex;flex-flow: row wrap; justify-content: space-around;">
                            <div style="flex: 5;">
                                <div style="display: flex; font-size:12px; font-weight: 700; line-height: 20px">
                                    <div style="width: 150px; color: #939393;">GRN Number #</div>
                                    <div style="width: 150px;">
                                        <%= grn?.grn_number || "-" %>
                                    </div>
                                </div>
                                <% if(grn.document_tenant_configuration?.print_status == true || grn.document_tenant_configuration?.print_status == 'true') {%>
                                    <div style="display: flex; font-size:10px; font-weight: 700; line-height: 20px">
                                        <div style="width: 150px; color: #939393;">Status</div>
                                        <div style="width: 150px;">
                                            <%= grn.status?.toProperCase() %>
                                        </div>
                                    </div>
                                <%}%>
                                <div style="display: flex; font-size:12px; font-weight: 700;line-height: 20px">
                                    <div style="width: 150px; color: #939393;">GRN Date</div>
                                    <div style="width: 150px;">
                                        <%= new Date().formatDateInTimezone(grn?.grn_date_time) %>                                  
                                    </div>
                                </div>

                                <% if(grn.invoice_number) { %>
                                    <div style="display: flex; font-size:12px; font-weight: 700; line-height: 20px">
                                        <div style="width: 150px; color: #939393;">Invoice Number</div>
                                        <div style="width: 150px;">
                                            <%= grn?.invoice_number %>
                                        </div>
                                    </div>
                                    <% } %>

                                        <% if(grn.invoice_date) { %>
                                            <div
                                                style="display: flex; font-size:12px; font-weight: 700; line-height: 20px">
                                                <div style="width: 150px; color: #939393;">Invoice Date</div>
                                                <div style="width: 150px;">
                                                    <%= new Date().formatDateInTimezone(grn?.invoice_date) %>                                  
                                                </div>
                                            </div>
                                            <% } %>
                                                <div
                                                    style="display: flex; font-size:12px; font-weight: 700; line-height: 20px">
                                                    <div style="width: 150px; color: #939393;">PO Number</div>
                                                    <div style="width: 150px;">
                                                        <%= grn?.po_number %>
                                                    </div>
                                                </div>
                                                <%if(grn.po_date) {%>
                                                    <div
                                                        style="display: flex; font-size:12px; font-weight: 700; line-height: 20px">
                                                        <div style="width: 150px; color: #939393;">PO Date</div>
                                                        <div style="width: 150px;">
                                                            <%= new Date().formatDateInTimezone(grn?.po_date) %>                                  
                                                        </div>
                                                    </div>
                                                    <%}%>
                                                        <%if(grn.grn_due_date) {%>
                                                            <div
                                                                style="display: flex; font-size:12px; font-weight: 700; line-height: 20px">
                                                                <div style="width: 150px; color: #939393;">Payment Due
                                                                    Date </div>
                                                                <div style="width: 150px;">
                                                                    <%= new Date().formatDateInTimezone(grn?.grn_due_date) %>                                  
                                                                </div>
                                                            </div>
                                                            <%}%>
                                                                <%- await
                                                                    render_custom_fields(grn.custom_fields,'SECTION-A', 'GOOD_RECEIVING_NOTES'
                                                                    , grn.org_id,` <div style="display: flex; font-size: 12px; font-weight: 700;line-height: 20px ">
                                                                        <div style="width: 150px;color: #939393;">{{field_name}}</div>
                                                                        <div style="width: 150px;">{{field_value}}</div>
                                                                        </div>
                                                                      `)%>
                            </div>

                            <div style="flex: 5; margin-top: -50px; text-align: right;">
                                <% if(grn.tenant_configuration.logo_base_64) { %>
                                    <div style="flex: 1;">
                                        <img style="height:<%=logo_height ? logo_height:50 %>px; object-fit: contain;margin-bottom: 5px;margin-top: 5px;"
                                            src="<%= grn.tenant_configuration.logo_base_64 %>" alt="tenant_img">
                                    </div>
                                    <% } else if(grn?.organisation_info?.org_logo) { %>
                                        <div style="flex: 1;">
                                            <img style="height:<%=logo_height ? logo_height:50 %>px; object-fit: contain;margin-bottom: 5px;margin-top: 5px;"
                                                src="<%= grn.organisation_info.org_logo %>" alt="organisation_img">
                                        </div>
                                        <% } else { %>
                                            <div style="flex:1; margin-top:90px;">

                                            </div>
                                            <% } %>
                                                <%var billing_address=grn?.tenant_info?.default_billing_address_info ||
                                                    {}%>
                                                    <div
                                                        style="font-size: 10px; text-align: right; font-weight: 600;line-height: 1.3;">
                                                        <h3 style="line-height: 0;">
                                                            <% if (grn?.document_tenant_configuration?.print_organisation_name) { %>
                                                                <%= grn?.organisation_info?.organisation_name %>
                                                                    <% } else { %>
                                                                        <%= grn?.tenant_info?.legal_name ||
                                                                    grn?.tenant_info?.tenant_name || "" %>
                                                                    <% } %>
                                                        </h3>
                                                        <%=billing_address?.address1||""%><br />
                                                            <%=billing_address?.city|| "" %>,
                                                                <%=billing_address?.state ||""%>
                                                                    <%=billing_address?.postal_code||""%>
                                                                        <br /><br />
                                                                        <b>GSTIN:</b>
                                                                        <%= grn?.tenant_info?.gst_number || "" %>
                                                                            <br />
                                                                            <% if (grn?.tenant_info?.identification_number?.filter(item=>
                                                                                item?.is_printable)?.length) { %>
                                                                                <% grn?.tenant_info?.identification_number?.filter(item=>
                                                                                item?.is_printable)?.map(item => { %>
                                                                                    <div style="font-size: 10px;margin-top: 5px;">
                                                                                    <b>
                                                                                        <%=item?.identification_name %>:
                                                                                    </b>
                                                                                    <%=item?.identification_number || "" %>
                                                                                    </div>
                                                                                <% })%>
                                                                            <% }%>
                                                    </div>

                                                    <% if(grn.download_button_url) { %>
                                                        <div style="margin-top: 20px; margin-left: 100px">
                                                            <a class="link"
                                                                style="text-decoration:none;padding:8px 15px;margin-top: 100px;border:none;border-radius:5px;font-weight:400;color:white;background-color: <%= grn.tenant_configuration.doc_primary_colour%>;"
                                                                href="<%=grn.download_button_url%>"
                                                                target="_blank">Download
                                                            </a>
                                                        </div>
                                                        <% }%>

                            </div>
                        </div>

                        <%if(grn?.tenant_seller_info) {%>
                            <div style="margin-top: 20px; display: flex; width: 100%; margin-bottom: 30px">
                                <div style="width: 49.5%; margin-right: 1%;">
                                    <div
                                        style="background-color: <%= grn.tenant_configuration.doc_secondary_colour%>; padding: 15px; border-radius: 10px; height: 90% !important">
                                        <div
                                            style="font-size: 16px; color: <%= grn.tenant_configuration.doc_primary_colour%>; font-weight: bold">
                                            Vendor Information</div>
                                        <div style="line-height: 18px; margin-top: 5px; font-size: 10px">
                                            <%= grn?.tenant_seller_info?.seller_name %> <br />
                                                <% if(grn?.seller_address_info?.address1 !=null &&
                                                    (grn.seller_address_info.address1).trim().length>0) { %>
                                                    <%= grn?.seller_address_info?.address1 || "" %> <br />
                                                        <%= grn?.seller_address_info?.city || "" %>,
                                                            <%=grn?.seller_address_info?.state || "" %>
                                                                <%= grn?.seller_address_info?.postal_code || "" %><br />
                                                                    <% } %>
                                                                        <% if(grn?.tenant_seller_info?.gst_number !=null
                                                                            &&
                                                                            (grn.tenant_seller_info.gst_number).trim().length>
                                                                            0) { %>
                                                                            <b>GSTIN:</b>
                                                                            <%= grn?.tenant_seller_info?.gst_number
                                                                                || "" %> <br />
                                                                                <% } %>
                                                                                    <% if(grn?.tenant_seller_info?.email_id_1)
                                                                                        { %>
                                                                                        <b>Email:</b>
                                                                                        <%= grn?.tenant_seller_info?.email_id_1
                                                                                            || "" %> <br />
                                                                                            <% } %>
                                                                                                <% if(grn?.tenant_seller_info?.mobile_1)
                                                                                                    { %>
                                                                                                    <b>Phone:</b>
                                                                                                    <%= grn?.tenant_seller_info?.mobile_1
                                                                                                        || "" %>
                                                                                                        <% } %>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <%}%>
                                <%if(grn?.stock_transfers){%>
                                    <% source_department_info=grn?.stock_transfers[0]?.source_department_info %>
                                        <div style="margin-top: 20px; display: flex; width: 100%; margin-bottom: 30px">
                                            <div style="width: 49.5%; margin-right: 1%;">
                                                <div
                                                    style="background-color: <%= grn.tenant_configuration.doc_secondary_colour%>; padding: 15px; border-radius: 10px; height: 90% !important">
                                                    <div
                                                        style="font-size: 16px; color: <%= grn.tenant_configuration.doc_primary_colour%>; font-weight: bold">
                                                        Source Unit</div>
                                                    <div style="line-height: 18px; margin-top: 5px; font-size: 10px">
                                                        <%= source_department_info?.tenant_name %> <br />
                                                            <%= source_department_info?.tenant_shipping_address_info?.address1
                                                                %> <br />
                                                                <%= source_department_info?.tenant_shipping_address_info?.city
                                                                    %>,
                                                                    <%=source_department_info?.tenant_shipping_address_info?.state
                                                                        %>
                                                                        <%= source_department_info?.tenant_shipping_address_info?.postal_code
                                                                            %><br />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <%}%>
                                        <% const sellerType = grn?.tenant_seller_info?.seller_type || grn?.seller_type %>
                                            <table
                                                style="width: 100%; font-size: 10px; border-radius: 5px 5px 0 0; border-collapse: collapse">
                                                <thead
                                                    style="border: 1px solid <%= grn.tenant_configuration.doc_primary_colour%>">
                                                    <tr
                                                        style="background-color: <%= grn.tenant_configuration.doc_primary_colour%>; color: #ffffff; text-align: left; font-weight: bold;">
                                                        <th style="padding: 12px 5px; white-space: nowrap; width: 10px">
                                                            #
                                                        </th>
                                                        <th style="padding: 12px 5px; white-space: nowrap">
                                                            Item & Description
                                                        </th>
                                                        <th style="padding: 12px 5px; white-space: nowrap">
                                                            HSN/SAC
                                                        </th>
                                                        <th style="padding: 12px 5px; white-space: nowrap">
                                                            Qty
                                                        </th>
                                                        <% if (grn?.allow_grn_to_create_ap_invoice) { %>
                                                            <th style="padding: 12px 5px; white-space: nowrap">
                                                                Invoice Qty
                                                            </th>
                                                        <% } %>
                                                        <th style="padding: 12px 5px; white-space: nowrap">
                                                            UQC
                                                        </th>
                                                        <% if (uniqueFields?.length > 0) { %>
                                                            <% uniqueFields.forEach(field => { %>
                                                                <th style="padding: 12px 5px; white-space: nowrap"><%= field.field_name %></th>
                                                            <% }) %>
                                                        <% } %>
                                                        <% if (!grn?.allow_grn_to_create_ap_invoice) { %>
                                                            <th style="padding: 12px 5px; white-space: nowrap">
                                                                Price/Unit
                                                            </th>
                                                            <th style="padding: 12px 5px; white-space: nowrap">
                                                                Rate
                                                            </th>
                                                            <th style="padding: 12px 3px; white-space: nowrap">
                                                                Discount
                                                            </th>
                                                            <% if (!(hide_tax_for_overseas && sellerType === 'OVERSEAS')) { %>
                                                                <th style="padding: 12px 5px; white-space: nowrap">
                                                                    Tax
                                                                </th>
                                                            <% } %>
                                                            <th style="padding: 2px 5px; white-space: nowrap">
                                                                Item Total
                                                            </th>
                                                        <% } %>
                                                    </tr>
                                                </thead>

                                                <tbody>
                                                    <% var totalWithGST=0; var taxableAmount=0; var totalDiscount=0; var
                                                        finalAmountBeforeDiscount=0; var sNo=0;%>
                                                        <% grn?.grn_lines?.forEach(function(product) { %>
                                                            <% sNo +=1; %>
                                                                <tr>

                                                                    <td
                                                                        style="padding: 2px 3px; border: 1px solid #d7d7d7; text-align: center; height:50px;">
                                                                        <%= sNo %>
                                                                    </td>
                                                                    <td
                                                                        style=" padding: 5px; border: 1px solid #d7d7d7">
                                                                        <div style="width: 170px;">
                                                                            <% if((grn?.document_tenant_configuration?.print_product_images == true ||grn?.document_tenant_configuration?.print_product_images == 'true')  && product?.product_sku_info?.assets?.[0]?.url) {%>
                                                                                <div style="display: flex;align-items: center;">
                                                                                  <div style="border: 1px solid grey;margin-right: 2px;">
                                                                                  <img style="object-fit: contain;"
                                                                                  src="<%= product?.product_sku_info?.assets?.[0]?.url %>"
                                                                                  width="50px" height="50px">
                                                                                </div>
                                                                                <div>
                                                                                    <% if((grn?.document_tenant_configuration?.print_item_code==true
                                                                                    ||grn?.document_tenant_configuration?.print_item_code=='true' ) &&
                                                                                    product?.product_sku_info?.ref_product_code) {%>
                                                                                      <div style="font-size: 10px;  font-weight: 500; margin-bottom: 2px;">
                                                                                      <%= product?.product_sku_info?.ref_product_code %>
                                                                                    </div>
                                                                                    <% } %>
                                                                                      <% if((grn?.document_tenant_configuration?.print_internal_sku_code==true
                                                                                        ||grn?.document_tenant_configuration?.print_internal_sku_code=='true' ) &&
                                                                                        product?.product_sku_info?.internal_sku_code) {%>
                                                                                        <div style="font-size: 10px;  font-weight: 500; margin-bottom: 2px;">
                                                                                          <%= product?.product_sku_info?.internal_sku_code %>
                                                                                        </div>
                                                                                        <% } %>
                                                                                    <div style="font-weight: bold;padding-bottom: 2px;"> <%=  product?.product_sku_info?.product_sku_name %>
                                                                                    </div>
                                                                                </div>
                                                                                </div>
                                                                              <%} else {%>
                                                                                <% if((grn?.document_tenant_configuration?.print_item_code==true
                                                                                ||grn?.document_tenant_configuration?.print_item_code=='true' ) &&
                                                                                product?.product_sku_info?.ref_product_code) {%>
                                                                                  <div style="font-size: 10px;  font-weight: 500; margin-bottom: 2px;">
                                                                                  <%= product?.product_sku_info?.ref_product_code %>
                                                                                </div>
                                                                                <% } %>
                                                                                  <% if((grn?.document_tenant_configuration?.print_internal_sku_code==true
                                                                                    ||grn?.document_tenant_configuration?.print_internal_sku_code=='true' ) &&
                                                                                    product?.product_sku_info?.internal_sku_code) {%>
                                                                                    <div style="font-size: 10px;  font-weight: 500; margin-bottom: 2px;">
                                                                                      <%= product?.product_sku_info?.internal_sku_code %>
                                                                                    </div>
                                                                                    <% } %>
                                                                               <div style="font-weight: bold;padding-bottom: 2px;"> <%=  product?.product_sku_info?.product_sku_name %>
                                                                              </div>
                                                                              <%} %>
                                                                              <% if( product?.product_sku_info?.product_category_info ) { %>
                                                                                    <% if((grn?.document_tenant_configuration?.print_category_status =='Child Category' )) {%>
                                                                                        <div><span style="font-weight:bold ;">Category - </span> <%= product?.product_sku_info?.product_category_info?.product_category_name %></div>
                                                                                    <% } %>
                                                                                    <% if((grn?.document_tenant_configuration?.print_category_status =='Parent Category' )) {%>
                                                                                        <div><span style="font-weight: bold;">Category - </span>  <%= product?.product_sku_info?.product_category_info?.category_path?.[0] %></div>
                                                                                    <% } %>
                                                                                    <% if((grn?.document_tenant_configuration?.print_category_status =='Full Category Path' )) {%>
                                                                                        <div><span style="font-weight:bold ;">Category - </span>  <%= product?.product_sku_info?.product_category_info?.category_path?.join('/') %></div>
                                                                                    <% } %>
                                                                                <% } %>
                                                                                <% if(product.remarks ) { %>
                                                                                    </br><span
                                                                                        style="font-size: 10px; color: <%= grn.tenant_configuration.doc_primary_colour%> !important">
                                                                                        <%- product?.remarks %>
                                                                                    </span>
                                                                                    <% } else {%>
                                                                                        <% } %>
                                                                                            <% if
                                                                                                (!product?.product_batches.length
                                                                                                ) { %>
                                                                                                <% } else { %>
                                                                                                    <div
                                                                                                        style="font-size: 10px; margin-top: 5px; font-weight: 500; color: <%= grn.tenant_configuration.doc_primary_colour%> !important">
                                                                                                        <% product?.product_batches.forEach(function(pbtch){ %>
                                                                                                            <div style="font-size: 10px; margin-top: 5px; font-weight: 500; color: <%= grn.tenant_configuration.doc_primary_colour%> !important">
                                                                                                                <% if(pbtch?.custom_batch_number){ %>
                                                                                                                    Batch #<%= pbtch?.custom_batch_number%>,</br>
                                                                                                                <% }%>
                                                                                                                 <% if(pbtch?.lot_number){%>
                                                                                                                    Lot #<%= pbtch?.lot_number%>,</br>
                                                                                                                <% }%>
                                                                                                                <% if(pbtch?.expiry_date < '2099-01-01'){%>
                                                                                                                    Expires on <%= new Date(pbtch?.expiry_date).customFormat(product?.product_sku_info?.expiry_date_format) %>,</br>
                                                                                                                <% }%>
                                                                                                                    Quantity - <%= pbtch.quantity%> <%= product?.uom_info?.[0]?.uqc.toProperCase() || 'Nos'%>
                                                                                                                <% if (pbtch?.custom_fields?.length > 0) { %>
                                                                                                                <% let batchCustomFields = pbtch?.custom_fields; %>
                                                                                                                <% batchCustomFields?.forEach(field => { %>
                                                                                                                    <% if((field?.is_printable ==true || field?.is_printable=='true') &&(field?.is_active ==true || field?.is_active=='true') ) { %> 
                                                                                                                    <div style="font-size: 10px; margin-top: 5px; font-weight: 500;">
                                                                                                                        <%= field?.field_name %> - <%= field?.field_value %>
                                                                                                                    </div>
                                                                                                                    <%} %>
                                                                                                                <% }); %>
                                                                                                            <% } %>
                                                                                                            </div>
                                                                                                            <% }) %>
                                                                                                    </div>
                                                                                                <% } %>
                                                                        </div>
                                                                    </td>
                                                                    <td
                                                                        style="padding: 2px 5px; border: 1px solid #d7d7d7">
                                                                        <%= product?.product_sku_info?.hsn_code %>
                                                                    </td>
                                                                    <td
                                                                        style="padding: 2px 5px; border: 1px solid hsl(0, 0%, 84%)">
                                                                        <%= toFixedQuantity(product.received_qty,
                                                                            product?.uom_info?.[0]?.precision) %>
                                                                        <% if (product.secondary_uom_qty) { %>
                                                                        <br>
                                                                        <br>
                                                                        <span style="color: #888;">
                                                                            <%= toFixedQuantity(product.secondary_uom_qty, product?.product_sku_info?.secondary_uom_info?.precision) %>
                                                                        </span>
                                                                        <% } %> 
                                                                    </td>
                                                                    <% if (grn?.allow_grn_to_create_ap_invoice) { %>
                                                                        <td style="padding: 2px 5px; border: 1px solid #d7d7d7">
                                                                            <%= toFixedQuantity(product?.invoice_quantity, product?.uom_info?.[0]?.precision) %>
                                                                        </td>
                                                                    <% } %>
                                                                    <td
                                                                        style="padding: 2px 5px; border: 1px solid #d7d7d7">
                                                                        <%= product?.uom_info?.[0]?.uqc.toProperCase()
                                                                            || 'Nos' %>
                                                                        <% if (product.secondary_uom_qty) { %>
                                                                        <br>
                                                                        <br>
                                                                        <span style="color: #888;">
                                                                            <%= product?.product_sku_info?.secondary_uom_info?.uqc || 'Nos' %>
                                                                        </span>
                                                                        <% } %>
                                                                    </td>
                                                                    <% if (product?.grn_line_custom_fields?.length > 0 && uniqueFields?.length > 0) { %>
                                                                        <% let ilCustomFields = product?.grn_line_custom_fields; %>
                                                                        <% uniqueFields.forEach(field => { %>
                                                                            <% let matchingRow = ilCustomFields.find(row => row.cf_id === field.cf_id); %>
                                                                            <% if(matchingRow?.field_type == 'DATE' && matchingRow?.field_value?.includes('T')){ %>
                                                                                <td style="padding: 2px 5px; border: 1px solid #d7d7d7;"><%= matchingRow?.field_value?.split('T')?.[0] || '-'  %></td>
                                                                              <% } else {%>
                                                                                <td style="padding: 2px 5px; border: 1px solid #d7d7d7;"><%= matchingRow?.field_value || '-' %></td>
                                                                              <%} %>
                                                                        <% }); %>
                                                                    <% } %>
                                                                    <% if (!grn?.allow_grn_to_create_ap_invoice) { %>
                                                                        <td style="padding: 2px 5px; border: 1px solid #d7d7d7;">
                                                                            <%= applyUserPolicy(product?.offer_price, grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                                                        </td>
                                                                        <td
                                                                            style="padding: 2px 5px; border: 1px solid #d7d7d7">
                                                                            <% var line_price=product.received_qty *
                                                                                product.offer_price %>
                                                                                <% finalAmountBeforeDiscount+=line_price; %>
                                                                            <%= applyUserPolicy(line_price, grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                                                        </td>
                                                                        <td
                                                                            style="padding: 2px 3px; border: 1px solid #d7d7d7;">
                                                                            <%= product?.line_discount_percentage || 0 %>%
                                                                        </td>
                                                                        <% if (!(hide_tax_for_overseas && sellerType === 'OVERSEAS')) { %>
                                                                            <td
                                                                                style="padding: 2px 3px; border: 1px solid #d7d7d7;">
                                                                                <%= product?.tax_info?.tax_value %>%
                                                                            </td>
                                                                        <% } %>
                                                                        <td
                                                                            style="padding: 2px 4px; border: 1px solid #d7d7d7">
                                                                            <%line_price -=product?.line_discount_amount ||
                                                                                0%>
                                                                                <% var productFinal=line_price *
                                                                                    (1+(product?.tax_info?.tax_value/100));
                                                                                    %>
                                                                                    <% totalWithGST +=productFinal; %>
                                                                                    <%= applyUserPolicy(productFinal, grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                                                        </td>
                                                                    <% } %>
                                                                </tr>
                                                                <% }) %>
                                                </tbody>
                                            </table>
                                            <div style="display: flex; page-break-inside: avoid;">
                                                <div style="margin-top: 25px;">
                                                    <div style="width: 100%; font-size: 12px;margin-top: 5px;">
                                                        <% if (!grn?.allow_grn_to_create_ap_invoice) { %>
                                                            Total In Words: <b>
                                                                <% if(grn?.hide_cost_price == true || grn?.hide_cost_price == 'true') { %>
                                                                    XXXXXX
                                                            <% } else{ %>
                                                                <%= totalInWords(grn?.grn_grand_total, grn?.org_currency_info?.currency_code || 'INR') || "Zero" %>
                                                            <% } %>
                                                            </b>
                                                        <% } %>
                                                    </div>
                                                    <%- await
                                                        render_custom_fields(grn?.custom_fields||[],'SECTION-B', 'GOOD_RECEIVING_NOTES'
                                                        , grn.org_id, `<div
                                                        style="width: 100%; font-size: 12px;margin-top: 5px;">
                                                        {{field_name}} <b>{{field_value}} </b>


                                                </div>`)%>
                                                <% if(grn?.remark){ %>
                                                    <div style="width: 90%; font-size: 12px;margin-top: 5px;">
                                                        Remarks : <b>
                                                            <%= grn?.remark %>
                                                        </b>
                                                    </div>
                                                    <% } %>
                                                        <div
                                                            style="display: inline-block; line-height: 18px; height: 55px; font-size: 12px;">

                                                        </div>

                                                        <div>
                                                            <% if(grn?.t_and_c){ %>
                                                                <div style="margin-top: 25px;">
                                                                    <div>
                                                                        <p
                                                                            style="font-size: 14px; color: <%= grn.tenant_configuration.doc_primary_colour%>;">
                                                                            Terms and Conditions</p>
                                                                        <ol class="move-to-next-page"
                                                                            style="line-height: 18px; font-size: 10px;list-style-type: none; padding-left: 10px; width: 100%">
                                                                            <% grn?.t_and_c?.split("\n").forEach(i=> {%>
                                                                                <li>
                                                                                    <pre
                                                                                        style="white-space: pre-wrap;"><%- i %></pre>
                                                                                </li>
                                                                                <%}) %>
                                                                        </ol>
                                                                    </div>
                                                                </div>
                                                                <% } %>
                                                        </div>
                                            </div>

                                            <div
                                                style='margin-left: auto; line-height: 18px;  padding-top: 20px; font-size: 10px;align-items: end;'>
                                                <% if (!grn?.allow_grn_to_create_ap_invoice) { %>
                                                    <table style="padding: 0; border-collapse: collapse;page-break-inside: avoid ;">
                                                        <tbody>
                                                            <tr>
                                                                <td style="white-space: nowrap; padding-right: 10px"><b>Sub
                                                                        Total </b></td>
                                                                <td
                                                                    style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                                    <%= applyUserPolicy(grn?.grn_base_price, grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="white-space: nowrap; padding-right: 10px">
                                                                    <b>Discount </b>
                                                                </td>
                                                                <td
                                                                    style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                                    <%= applyUserPolicy(grn?.discount_amount, grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                                                </td>
                                                            </tr>
                                                            <%var grn_charges=grn.other_charges ?? []%>
                                                            <% grn_charges.forEach(function(charges) { %>
                                                                    <% if(charges?.tax_info) { %>
                                                                        <tr>
                                                                    <td style="white-space: nowrap; padding-right: 10px">
                                                                        <b> <%= charges.charge_name%></b>
                                                                    </td>
                                                                    <td style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                                        <%=new Intl.NumberFormat('en-IN',{
                                                                            style: 'currency'
                                                                            , currency:
                                                                            grn?.currency_code
                                                                            }).format(charges.charge_amount)
                                                                            %>
                                                                    </td>
                                                                </tr>
                                                                <%} %>
                                                            <%})%>
                                                            <% if (!(hide_tax_for_overseas && sellerType === 'OVERSEAS')) { %>
                                                                <% grn?.tax_info?.forEach(function(tax) { %>
                                                                    <tr>
                                                                        <td style="white-space: nowrap; padding-right: 10px"><b>
                                                                                <%= tax.tax_type_name %>
                                                                            </b></td>
                                                                        <td
                                                                            style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                                        <%= applyUserPolicy(tax?.tax_amount, grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                                                        </td>
                                                                    </tr>
                                                                <% }) %>
                                                            <% } %>
                                                                    <% if(grn?.tcs_info){ %>
                                                                        <tr>
                                                                            <td
                                                                                style="white-space: nowrap; padding-right: 10px">
                                                                                <b>TCS
                                                                                </b>
                                                                            </td>
                                                                            <td
                                                                                style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                                                <%=new Intl.NumberFormat('en-IN', {
                                                                                    style: 'currency' , currency:
                                                                                    grn?.currency_code
                                                                                    }).format(grn?.tcs_info?.tcs_amount) %>
                                                                            </td>
                                                                        </tr>
                                                                        <% } %>
                                                                            <% if(grn?.tds_info){ %>
                                                                                <tr>
                                                                                    <td
                                                                                        style="white-space: nowrap; padding-right: 10px">
                                                                                        <b>TDS
                                                                                        </b>
                                                                                    </td>
                                                                                    <td
                                                                                        style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                                                        (-)<%=new Intl.NumberFormat('en-IN',
                                                                                            { style: 'currency' , currency:
                                                                                            grn?.currency_code
                                                                                            }).format(grn?.tds_info?.tds_amount)
                                                                                            %>
                                                                                    </td>
                                                                                </tr>
                                                                                <% } %>
                                                                                    <% if (grn.charge_1_name) { %>
                                                                                        <tr>
                                                                                            <td
                                                                                                style="white-space: nowrap; padding-right: 10px">
                                                                                                <b>
                                                                                                    <%= grn.charge_1_name %>
                                                                                                        
                                                                                                </b>
                                                                                            </td>
                                                                                            <td
                                                                                                style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                                                                <%=new
                                                                                                    Intl.NumberFormat('en-IN',
                                                                                                    { style: 'currency' ,
                                                                                                    currency:
                                                                                                    grn?.currency_code
                                                                                                    }).format(grn.charge_1_value)
                                                                                                    %>
                                                                                            </td>
                                                                                        </tr>
                                                                                    <% } %>
                                                                                    <% if (grn.charge_2_name) { %>
                                                                                        <tr>
                                                                                            <td
                                                                                                style="white-space: nowrap; padding-right: 10px">
                                                                                                <b>
                                                                                                    <%= grn.charge_2_name %>:
                                                                                                </b>
                                                                                            </td>
                                                                                            <td
                                                                                                style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                                                                <%=new Intl.NumberFormat('en-IN', {
                                                                                                    style: 'currency' , currency:
                                                                                                    grn?.currency_code
                                                                                                    }).format(grn.charge_2_value) %>
                                                                                            </td>
                                                                                        </tr>
                                                                                    <% } %>
                                                                                            <%var
                                                                                                grn_charges=grn.other_charges
                                                                                                ?? []%>
                                                                                                <% grn_charges.forEach(function(charges) { %>
                                                                                                        <% if(!charges?.tax_info) { %>
                                                                                                            <tr>
                                                                                                        <td style="white-space: nowrap; padding-right: 10px">
                                                                                                            <b> <%= charges.charge_name%></b>
                                                                                                        </td>
                                                                                                        <td style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                                                                            <%=new Intl.NumberFormat('en-IN',{
                                                                                                                style: 'currency'
                                                                                                                , currency:
                                                                                                                grn?.currency_code
                                                                                                                }).format(charges.charge_amount)
                                                                                                                %>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    <%} %>
                                                                                                    <%})%>
                                                                                                        <tr>
                                                                                                            <td
                                                                                                                style="white-space: nowrap; padding-right: 10px">
                                                                                                                <b>Round
                                                                                                                    Off</b>
                                                                                                            </td>
                                                                                                            <td
                                                                                                                style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                                                                                <% if
                                                                                                                    (grn?.grn_round_off
                                                                                                                    < 0) {
                                                                                                                    %> (-)
                                                                                                                    <% } %>
                                                                                                                        <%= new
                                                                                                                            Intl.NumberFormat('en-IN',
                                                                                                                            {
                                                                                                                            style: 'currency'
                                                                                                                            ,
                                                                                                                            currency:
                                                                                                                            grn?.currency_code
                                                                                                                            }).format(Math.abs(grn?.grn_round_off))%>
                                                                                                            </td>
                                                                                                        </tr>

                                                                                                        <tr>
                                                                                                            <td
                                                                                                                style="white-space: nowrap; padding-right: 10px;">
                                                                                                                <b>Total (
                                                                                                                    <%= grn?.org_currency_info?.currency_code
                                                                                                                        ?
                                                                                                                        grn.org_currency_info.currency_code
                                                                                                                        : 'INR'
                                                                                                                        %>)
                                                                                                                </b>
                                                                                                            </td>
                                                                                                            <td
                                                                                                                style="white-space: nowrap; padding-left: 10px; text-align: right">
                                                                                                                    <%= applyUserPolicy(grn?.grn_grand_total, grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                                                                                            </td>
                                                                                                        </tr>

                                                                                                        <tr>
                                                                                                            <td
                                                                                                                style="white-space: nowrap; padding-right: 10px">
                                                                                                                <b>Payment
                                                                                                                    Made</b>
                                                                                                            </td>
                                                                                                            <td
                                                                                                                style="white-space: nowrap; padding-left: 10px; text-align: right;">
                                                                                                                <% if
                                                                                                                    (grn?.total_payment_made)
                                                                                                                    { %> (-)
                                                                                                                    <% } %>
                                                                                                                    <%= applyUserPolicy(grn?.total_payment_made, grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                                                                                            </td>
                                                                                                        </tr>

                                                                                                        <tr>
                                                                                                            <td
                                                                                                                style="white-space: nowrap; padding-right: 10px">
                                                                                                                <b>Credits
                                                                                                                    Applied</b>
                                                                                                            </td>
                                                                                                            <td
                                                                                                                style="white-space: nowrap; padding-left: 10px; text-align: right;">
                                                                                                                <% if
                                                                                                                    (grn?.debit_note_info[0].document_db_total)
                                                                                                                    { %> (-)
                                                                                                                    <% } %>
                                                                                                                    <%= applyUserPolicy(grn?.debit_note_info[0].document_db_total, grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                                                                                            </td>
                                                                                                        </tr>

                                                                                                        <tr>
                                                                                                            <td
                                                                                                                style="white-space: nowrap; border-top: 1px solid black;">
                                                                                                                <b>Total
                                                                                                                    Due(<%=
                                                                                                                        grn?.org_currency_info?.currency_code
                                                                                                                        ?
                                                                                                                        grn.org_currency_info.currency_code
                                                                                                                        : 'INR'
                                                                                                                        %>)
                                                                                                                </b>
                                                                                                            </td>
                                                                                                            <td
                                                                                                                style="white-space: nowrap; border-top: 1px solid black; padding-left: 10px; text-align: right">
                                                                                                                <%= applyUserPolicy((grn?.grn_grand_total  -  grn?.debit_note_info[0]?.document_db_total - grn?.total_payment_made), grn?.currency_code, grn?.organisation_info?.global_config?.settings?.price_precision, grn?.hide_cost_price) %>
                                                                                                            </td>
                                                                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                <% } %>
                                                <%if(grn?.document_tenant_configuration?.print_signature_input){%>
                                                    <div style=" width: 100%; margin-top:10px;padding:0px 7px 5px 7px; border-radius: 4px; display:flex;flex-direction:column;align-items:center;background-color: <%= grn.tenant_configuration.doc_secondary_colour%>;page-break-inside:avoid;">
                                                        <div style="text-align: center;padding-top: 5px;font-size: 10px;"><%= grn?.document_tenant_configuration?.signature_name||'' %></div>
                                                        <b>
                                                            <div style="height:36px;">
                                                                <%if(grn?.document_tenant_configuration?.doc_sign_base_64){%>
                                          
                                                                        <img style="height:30px; width:150px; object-fit: contain; margin-bottom:2px;"
                                                                             src="<%= grn.document_tenant_configuration?.doc_sign_base_64 %>"
                                                                             alt="signature_img">
                                                                <% } %>
                                                            </div>
                                                        </b>
                                                        <div style="font-size: 10px;">Authorized Signatory</div>
                                                    </div>
                                                <%}%>
                                            </div>
        </div>
</body>

</html>
