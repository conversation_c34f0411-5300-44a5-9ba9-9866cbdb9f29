const { bulkPushInventory } = require("../../integrations/services/unicommerce/Product/bulkPushInventory");
const { bulkPushBillsUsingCronJobs } = require("../../integrations/services/zoho/bill/bulkPushBillUsingCronJobs")
const { bulkPushInvoicesUsingCronJobs } = require("../../integrations/services/zoho/invoices/bulkPushInvoicesUsingCronJobs")
const { pullCustomerPayments } = require("../../integrations/services/zoho/invoices/pullCustomerPayments")
const { pullVendorPayments } = require("../../integrations/services/zoho/bill/pullVendorPayments");
const { sendDailyRunRateStatus } = require("../../product-sku/services/daily-run-rate/sendDailyRunRateStatus.service");
const { pullSalesOrders } = require("../../integrations/services/zoho/sales_orders/pullSalesOrders");
const { getAllExpiredBatches } = require("../../product-sku/services/batch/getAllExpiredBatchesAndMarkAsRejectedBatch");

exports.nodeCronRouter = async (job) => {
    const JOB_NAME = job.job_name;
    better.log({
        "service": "NODE_CRON_JOB",
        "job_status": "START",
        "job_name": job.job_name,
        "data": job.data,
        "node_cron_job_id": job.node_cron_job_id
    });
    try {
        switch (JOB_NAME) {
            case 'UNICOMMERCE_PUSH_INVENTORY':
                await bulkPushInventory(job.data)
                break;
            case 'SYNC_BILLS_AUTOMATICALLY':
                await bulkPushBillsUsingCronJobs(job)
                break;
            case 'SYNC_INVOICES_AUTOMATICALLY':
                await bulkPushInvoicesUsingCronJobs(job)
                break;
            case 'PULL_VENDOR_PAYMENTS_AUTOMATICALLY':
                await pullVendorPayments(job)
                break;
            case 'PULL_CUSTOMER_PAYMENTS_AUTOMATICALLY':
                await pullCustomerPayments(job)
                break;
            case 'SEND_DAILY_RUN_RATE_SKU_STATUS':
                await sendDailyRunRateStatus(job.data)
                break
            case 'PULL_SALES_ORDERS_AUTOMATICALLY':
                await pullSalesOrders({...job.data, from_data : job.last_run_at?.toISOString()?.split('T')?.[0]})
                break;
            case "EXPIRED_BATCH_QUEUE_PROCESSOR":
                await getAllExpiredBatches();
                break;
            default:
                throw new Error('Invalid Job')
        }
    } catch (error) {
        better.error({
            "service": "NODE_CRON_JOB",
            "job_status": "FAILED",
            "job_name": job.job_name,
            "data": job.data,
            "node_cron_job_id": job.node_cron_job_id,
            'error': error
        });
    }

    better.log({
        "service": "NODE_CRON_JOB",
        "job_status": "COMPLETED",
        "job_name": job.job_name,
        "data": job.data,
        "node_cron_job_id": job.node_cron_job_id
    });

}