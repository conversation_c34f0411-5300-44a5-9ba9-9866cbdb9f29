const { common_json_to_sheet } = require("../../../common/common_json_to_sheet");
const { db_r2, user_permissions } = require("../../../config");
const { REPORT_GROUPS, FILTER_TYPE_LIST, DOWNLOAD_METHODS, 
    formFilterValidator, generateReportFileName,
    skipMaskedKeys} = require("./_helpers");

let output_glossary = {
    "business_unit_name":"Business Unit Name",
    "procuzy_sku_code": "Procuzy SKU Code",
    "ref_product_code": "Ref. Product Code",
    "product_name": "Product Name",
    "hsn_code": "HSN Code",
    "total_available_quantity": "Available Quantity",
    "unit_of_measurement": "UOM",
    "rejected_quantity": "Rejected Qty",
    "reserved_quantity": "Reserved Qty",
    "qc_on_hold_qty": "QC On Hold Qty",
    "pending_in_qc_qty": "QC Pending Qty", 
    "avg_batch_cost_price": "Avg. Cost Price",
    "avg_batch_landed_cost": "Avg. Landed Price",
    "avg_batch_cost_price_with_gst": "Avg. Cost Price (With Tax)",
    "avg_batch_landed_cost_with_gst": "Avg. Landed Price (With Tax)",
    "tax_percentage": "Tax Percentage",
    "product_category": "Product Category"
};

exports.UI_COMPONENT = {
    "report_id": "stock-summary-by-location",
    "report_name": "Stock Summary by Location",
    "report_group": REPORT_GROUPS.inventory_report,
    "report_group_key": REPORT_GROUPS.inventory_report.key,
    "report_description": "See real-time inventory, cost, & product details for each location",
    "report_sample_url": "",
    "report_icon": "ShoppingOutlined",
    "report_filters": [{
        "label": "Select Business Unit",
        "type": FILTER_TYPE_LIST.DROPDOWN,
        "dropdown_values": [],
        "multi_select": true,
        "payload_key_name": "tenant_ids",
        "required": true
    },{
        "label": "Select Product Category",
        "type": FILTER_TYPE_LIST.CATEGORY_SELECTOR,
        "multi_select": true,
        "payload_key_name": "product_category_ids",
        "required": false
    },{
        "label": "Show Department Wise Qty in Separate Columns",
        "type": FILTER_TYPE_LIST.DROPDOWN,
        "dropdown_values": [{
            "name": "Yes",
            "value": "yes"
        },{
            "name": "No",
            "value": "no"
        }],
        "payload_key_name": "show_department_qty_column",
        "default_value": "no",
        "multi_select": false,
        "required": true
    }],
    "download_methods": [DOWNLOAD_METHODS.EMAIL, DOWNLOAD_METHODS.DIRECT_DOWNLOAD, DOWNLOAD_METHODS.SCHEDULED_REPORT],
    "glossary": output_glossary,
    "sheet_configuration": {
        "sheet_name": "Sheet 1",
        "header_row_style": {
            font: {
                color: { rgb: "FFFFFF" },
                bold: true,
            },
            fill: {
                fgColor: { rgb: "000000" },
            },
        }
    },
    "access_control": global.clone(user_permissions.product.READ),
    "download_file_name": "Stock Summary by Location"
}

exports.report_generator = async (_input, {
    validate_only=false
}={},client = db_r2) => {
    
    const queries = [];
    if (_input.org_id) queries.push(`t.org_id = ${_input.org_id}`);
    else throw new Error("org_id is required");
    _input.tenant_ids = global.comma_separated_to_array(_input?.tenant_ids);
    _input.product_category_ids = global.comma_separated_to_array(_input?.product_category_ids);
    formFilterValidator(this.UI_COMPONENT.report_filters,_input);

    queries.push(`tp.tenant_id in (${_input?.tenant_ids.join(',')})`);
    if (_input?.product_category_ids?.length){
        let separate_categories = _input.product_category_ids;
        let category_cases = []
        separate_categories.map(sc => category_cases.push(`pc.category_id_vector @> ARRAY[${sc}]`))
        queries.push(`(${category_cases.join(' OR ')})`);
    }
    queries.push(`tp.is_archive = false`);
    queries.push(`tp.is_active = true`);
    queries.push(`ps.product_type = 'STORABLE'`);

    if(validate_only) return true;

    let inventorySummaryQuery = `
        -- Stock summary location wise
        SELECT
            t.tenant_id, o.org_id, t.tenant_name as business_unit_name, t.default_store_id,
            tp.tenant_product_id, tp.product_sku_id,
            ps.product_sku_name as product_name,
            tp.quantity as total_available_quantity,
            ROUND(COALESCE(tp.pending_in_qc_qty, 0), 2) AS pending_in_qc_qty,
            (
                SELECT COALESCE(SUM(qc.original_qty), 0) AS qc_on_hold_qty
                from quality_checks qc
                JOIN tenant_product_batches tpb ON tpb.batch_id = qc.batch_id and tpb.tenant_product_id = tp.tenant_product_id
                where qc.is_qc_on_hold = true
            ),
            (
                select (coalesce(sum(br.reserved_qty), 0) - coalesce(sum(br.consumed_qty), 0)) as reserved_quantity
                from batch_reservations br
                where br.tenant_product_id = tp.tenant_product_id
            ),
            tp.avg_batch_cost_price,
            tp.avg_batch_landed_cost, tp.rejected_quantity,
            ps.hsn_code, ps.assets->'0' as product_image_link, ps.internal_sku_code as procuzy_sku_code,
            ps.ref_product_code, ps.description, ps.product_type,
            pc.category_path, pc.product_category_name as product_category,
            ps.uom_info->>'uqc' as unit_of_measurement,
            ots.tax_value as tax_percentage,
            org_config.inventory_config->'settings'->>'department_level_stock' as is_department_level_stock_enabled,
            org_config.inventory_config->'settings'->>'show_stock_to_other_department' as is_stock_visible_to_other_department_enabled,
            org_config.inventory_config->'settings'->>'negative_inventory_allowed' as is_negative_stock_enabled,
            tp.department_quantities
        FROM tenant_product tp
        JOIN product_sku ps on ps.product_sku_id = tp.product_sku_id
        JOIN tenant t ON tp.tenant_id = t.tenant_id
        JOIN organisation o ON t.org_id = o.org_id
        LEFT JOIN org_configuration org_config ON org_config.org_id = o.org_id
        LEFT JOIN product_category pc on pc.product_category_id = ps.product_category_id
        LEFT JOIN tax ots on ots.tax_id = ps.tax_id
        WHERE ${queries.join(" AND ")}
    `;
    let _result = await client.query(inventorySummaryQuery);
    let result = _result.rows;
    let output_glossary_cp = global.clone(output_glossary);    

    let default_value_map = {};
    result = result.map(i=>{
        i.total_available_quantity = global.CustomToFixed(i.total_available_quantity);
        i.avg_batch_cost_price = global.CustomToFixed(i.avg_batch_cost_price);
        i.avg_batch_landed_cost = global.CustomToFixed(i.avg_batch_landed_cost);
        i.rejected_quantity = global.CustomToFixed(i.rejected_quantity);
        i.reserved_quantity = global.CustomToFixed(i.reserved_quantity);
        i.tax_percentage = global.CustomToFixed(i.tax_percentage);
        i.avg_batch_cost_price_with_gst = i.avg_batch_cost_price * (1+ (i.tax_percentage/100));
        i.avg_batch_landed_cost_with_gst = i.avg_batch_landed_cost * (1+ (i.tax_percentage/100));
        i.avg_batch_cost_price_with_gst = global.CustomToFixed(i.avg_batch_cost_price_with_gst);
        i.avg_batch_landed_cost_with_gst = global.CustomToFixed(i.avg_batch_landed_cost_with_gst);
        if(i?.department_quantities?.length && _input?.show_department_qty_column?.[0]==='yes'){
            i.department_quantities.forEach(dept=>{
                if(dept.is_active && dept.quantity){
                    let clm_name = `Dept Qty. (${dept.alias_name})${dept.is_active ? '' : `(In Active)`}`;
                    output_glossary_cp[clm_name] = clm_name;
                    i[clm_name] = Number(dept.quantity);
                    default_value_map[clm_name] = 0;
                }
            });
        }
        return i;
    });
    result.forEach(i=>{
        Object.keys(default_value_map).forEach(j=>{
            i[j] =   i[j] || 0;
        });
    })

    let tenant_id_array = [...new Set(result.map(i=>i.tenant_id))];
    let spreadsheet_payload = tenant_id_array.map(tenant_id=>{
        let _sheet = result.filter(i=>i.tenant_id===tenant_id);
        return {
            sheet_name: `${tenant_id}`,
            sheet_json: _sheet
        }
    })
    output_glossary = await skipMaskedKeys(output_glossary, _input?.user_id);
    let final_xl_sheet = await common_json_to_sheet(spreadsheet_payload, {
        output_glossary: output_glossary_cp
    });
    return {
        file_base64: final_xl_sheet.base64,
        file_name: generateReportFileName(this.UI_COMPONENT.download_file_name, _input.from_date, _input.to_date, 'xlsx'),
    }
};