const { common_json_to_sheet } = require("../../../common/common_json_to_sheet");
const { db_r2, user_permissions } = require("../../../config");
const { REPORT_GROUPS, FILTER_TYPE_LIST, DOWNLOAD_METHODS, 
    formFilterValidator, generateReportFileName,
    skipMaskedKeys} = require("./_helpers");

let output_glossary = {
    "business_unit_name":"Business Unit Name",
    "department_name": "Department Name",
    "procuzy_sku_code": "Procuzy SKU Code",
    "ref_product_code": "Ref. Product Code",
    "product_name": "Product Name",
    "hsn_code": "HSN Code",
    "total_available_quantity": "Available Quantity",
    "unit_of_measurement": "UOM",
    "rejected_quantity": "Rejected Qty",
    "reserved_quantity": "Reserved Qty",
    "avg_batch_cost_price": "Avg. Cost Price",
    "avg_batch_landed_cost": "Avg. Landed Price",
    "avg_batch_cost_price_with_gst": "Avg. Cost Price (With Tax)",
    "avg_batch_landed_cost_with_gst": "Avg. Landed Price (With Tax)",
    "tax_percentage": "Tax Percentage",
    "product_category": "Product_category",
};

exports.UI_COMPONENT = {
    "report_id": "stock-summary-by-department",
    "report_name": "Stock Summary by Department",
    "report_group": REPORT_GROUPS.inventory_report,
    "report_group_key": REPORT_GROUPS.inventory_report.key,
    "report_description": "View real-time stock levels by department in each location",
    "report_sample_url": "",
    "report_icon": "ShoppingOutlined",
    "report_filters": [{
        "label": "Select Business Unit",
        "type": FILTER_TYPE_LIST.DROPDOWN,
        "dropdown_values": [],
        "multi_select": true,
        "payload_key_name": "tenant_ids",
        "required": true
    },{
        "label": "Select Department",
        "type": FILTER_TYPE_LIST.DEPARTMENT_SELECTOR,
        "multi_select": true,
        "payload_key_name": "department_ids",
        "required": false
    },{
        "label": "Select Product Category",
        "type": FILTER_TYPE_LIST.CATEGORY_SELECTOR,
        "multi_select": true,
        "payload_key_name": "product_category_ids",
        "required": false
    }],
    "download_methods": [DOWNLOAD_METHODS.EMAIL, DOWNLOAD_METHODS.DIRECT_DOWNLOAD, DOWNLOAD_METHODS.SCHEDULED_REPORT],
    "glossary": output_glossary,
    "sheet_configuration": {
        "sheet_name": "Sheet 1",
        "header_row_style": {
            font: {
                color: { rgb: "FFFFFF" },
                bold: true,
            },
            fill: {
                fgColor: { rgb: "000000" },
            },
        }
    },
    "access_control": global.clone(user_permissions.product.READ),
    "download_file_name": "Stock Summary by Department"
}

exports.report_generator = async (_input, {
    validate_only=false
}={},client = db_r2) => {
    
    const queries = [];
    if (_input.org_id) queries.push(`t.org_id = ${_input.org_id}`);
    else throw new Error("org_id is required");
    _input.tenant_ids = global.comma_separated_to_array(_input?.tenant_ids);
    _input.department_ids = global.comma_separated_to_array(_input?.department_ids);
    _input.product_category_ids = global.comma_separated_to_array(_input?.product_category_ids);
    formFilterValidator(this.UI_COMPONENT.report_filters,_input);

    queries.push(`tp.tenant_id in (${_input?.tenant_ids.join(',')})`);
    if(_input?.department_ids?.length) queries.push(`td.department_id in (${_input?.department_ids.join(',')})`);
    if (_input?.product_category_ids?.length){
        let separate_categories = _input.product_category_ids;
        let category_cases = []
        separate_categories.map(sc => category_cases.push(`pc.category_id_vector @> ARRAY[${sc}]`))
        queries.push(`(${category_cases.join(' OR ')})`);
    }
    queries.push(`tp.is_archive = false`);
    queries.push(`tp.is_active = true`);
    queries.push(`ps.product_type = 'STORABLE'`);

    if(validate_only) return true;

    let inventorySummaryQuery = `
    -- Stock summary department wise
        SELECT
            t.tenant_id, o.org_id, t.default_store_id,
            tp.tenant_product_id, tp.product_sku_id,
            td.department_id,
            t.tenant_name as business_unit_name,
            td.alias_name as department_name,
            ps.product_sku_name as product_name,
            SUM(CASE WHEN tpb.is_rejected_batch = FALSE THEN tpb.available_qty ELSE 0 END) AS total_available_quantity,
            (
                select (coalesce(sum(br.reserved_qty), 0) - coalesce(sum(br.consumed_qty), 0)) as reserved_quantity
                from batch_reservations br
                where br.tenant_product_id = tp.tenant_product_id
            ),
            tp.avg_batch_cost_price,
            tp.avg_batch_landed_cost,
            SUM(CASE WHEN tpb.is_rejected_batch = TRUE THEN tpb.available_qty ELSE 0 END) AS rejected_quantity,
            ps.hsn_code, ps.assets->'0' as product_image_link, ps.internal_sku_code as procuzy_sku_code,
            ps.ref_product_code, ps.description, ps.product_type,
            pc.category_path, pc.product_category_name as product_category,
            ps.uom_info->>'uqc' as unit_of_measurement,
            ots.tax_value as tax_percentage,
            org_config.inventory_config->'settings'->>'department_level_stock' as is_department_level_stock_enabled,
            org_config.inventory_config->'settings'->>'show_stock_to_other_department' as is_stock_visible_to_other_department_enabled,
            org_config.inventory_config->'settings'->>'negative_inventory_allowed' as is_negative_stock_enabled
        FROM tenant_product_batches tpb
        JOIN tenant_department td ON td.tenant_department_id = tpb.tenant_department_id
        RIGHT JOIN tenant_product tp On tp.tenant_product_id = tpb.tenant_product_id
        JOIN product_sku ps on ps.product_sku_id = tp.product_sku_id
        JOIN tenant t ON tp.tenant_id = t.tenant_id
        JOIN organisation o ON t.org_id = o.org_id
        JOIN org_configuration org_config ON org_config.org_id = o.org_id
        LEFT JOIN product_category pc on pc.product_category_id = ps.product_category_id
        INNER JOIN tax ots on ots.tax_id = ps.tax_id
        WHERE ${queries.join(" AND ")}
        GROUP BY td.tenant_department_id, o.org_id, t.tenant_id, tp.tenant_product_id,
                ps.product_sku_id, pc.product_category_id, org_config.org_configuration_id, ots.tax_id
    `;
    let _result = await client.query(inventorySummaryQuery);
    let result = _result.rows;

    result = result.map(i=>{
        i.total_available_quantity = global.CustomToFixed(i.total_available_quantity);
        i.avg_batch_cost_price = global.CustomToFixed(i.avg_batch_cost_price);
        i.avg_batch_landed_cost = global.CustomToFixed(i.avg_batch_landed_cost);
        i.rejected_quantity = global.CustomToFixed(i.rejected_quantity);
        i.reserved_quantity = global.CustomToFixed(i.reserved_quantity);
        i.tax_percentage = global.CustomToFixed(i.tax_percentage);
        i.avg_batch_cost_price_with_gst = i.avg_batch_cost_price * (1+ (i.tax_percentage/100));
        i.avg_batch_landed_cost_with_gst = i.avg_batch_landed_cost * (1+ (i.tax_percentage/100));
        i.avg_batch_cost_price_with_gst = global.CustomToFixed(i.avg_batch_cost_price_with_gst);
        i.avg_batch_landed_cost_with_gst = global.CustomToFixed(i.avg_batch_landed_cost_with_gst);
        return i;
    });

    let tenant_id_array = [...new Set(result.map(i=>i.tenant_id))];
    let spreadsheet_payload = tenant_id_array.map(tenant_id=>{
        let _sheet = result.filter(i=>i.tenant_id===tenant_id);
        return {
            sheet_name: `${tenant_id}`,
            sheet_json: _sheet
        }
    })
    output_glossary = await skipMaskedKeys(output_glossary, _input?.user_id);
    let final_xl_sheet = await common_json_to_sheet(spreadsheet_payload, {
        output_glossary
    });
    return {
        file_base64: final_xl_sheet.base64,
        file_name: generateReportFileName(this.UI_COMPONENT.download_file_name, _input.from_date, _input.to_date, 'xlsx'),
    }
};