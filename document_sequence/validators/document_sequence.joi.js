const Joi = require('joi');

exports.VALIDATORS = {
    CREATE_DOCUMENT_SEQUENCE: Joi.object({
        entity_name: Joi.string().required(),
        seq_prefix: Joi.string().required(),
        seq_counter: Joi.number().required(),
        is_default: Joi.boolean().default(false),
        tenant_id:Joi.number().required(),
        org_id: Joi.number().required(),
        seq_suffix:Joi.string().allow('').default('')
    }),
    UPDATE_DOCUMENT_SEQUENCE: Joi.object({
        seq_id: Joi.number().required(),
        entity_name: Joi.string().required(),
        seq_prefix: Joi.string().required(),
        seq_counter: Joi.number().required(),
        tenant_id:Joi.number().required(),
        org_id: Joi.number().required(),
        seq_suffix:Joi.string().allow('').default('')
    })
};
exports.createDocumentSequenceValidator = (_input) => __global_joi.validate(this.VALIDATORS.CREATE_DOCUMENT_SEQUENCE, _input);
exports.updateDocumentSequenceValidator = (_input) => __global_joi.validate(this.VALIDATORS.UPDATE_DOCUMENT_SEQUENCE, _input);