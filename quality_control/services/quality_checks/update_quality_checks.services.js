const { db } = require("../../../config");
const { batch_management } = require("../../../product-sku/services/batch-management");
const { updateQualityCheckValidator } = require("../../validators");
const { updateGRNQualityChecks } = require('./approvalFlows/good_receiving_note.approvalFlow');
const { updateMOQualityChecks } = require('./approvalFlows/manufacturing_order.approvalFlow');
const{ arnumber_generator } = require('../../../document_number_format/services/document_number_format/ar_number_generator');
const { sendRejectionNotification } = require('./sendRejectionNotification');
const { updateQcHoldStatus } = require("./updateQcHoldStatus.service");
const { createQualityChecks } = require("./create_quality_checks.services");
const { createManufacturingOrderAdjustment } = require("../../../production/services/mo_adjustment/createMoAdjustment.service");
const { updateReservedInventory } = require("../../../product-sku/services/batch-management/batch_reservations/updateReservedInventory");
const { CF_ENTITY } = require("../../../application/constants/customField.contant");
const { updateCustomFieldValue } = require("../../../application/services/customFieldValue/updateCustomFieldValue.service");
const { refreshSKUQuantities, refreshPendingQtyForQC } = require("../../../product-sku/services/product-sku");
/**
 * @typedef {Object} ReservationData
 * @property {number} reservation_id
 */

/**
 *      *******************************************
 *      **                                       **
 *      **    update quality checks              **
 *      **                                       **
 *      *******************************************
 *  Steps : -
 *  1. take all fields which is required to update qc rules from user
 *  2. then validate all fields by joi
 *  3. update whatever field user want to update
 */


function validateQualityCheck(qualityCheckData) {    // Extract relevant information from quality check data
    const additionalInputs = qualityCheckData.additional_inputs;

    // Function to validate a single input based on its pass condition
    function validateInput(input) {
        input
        const passCondition = input.pass_condition;
        let inputValue = input.value

        if (input.type =='DATE') {
                let dateParts = input.value?.split('T')[0].split('-'); 
                let date = new Date(dateParts[0], dateParts[1] - 1, dateParts[2]); 
                const currentDate = new Date(); // Current date
                const currentDateISO = currentDate.toISOString().split('T')[0]; 
                const currentDateParts = currentDateISO.split('-');
                const currentDateFormatted = new Date(currentDateParts[0], currentDateParts[1] - 1, currentDateParts[2]); 
                const differenceInMs = date - currentDateFormatted; 
                const days = Math.floor(differenceInMs / (1000 * 60 * 60 * 24));
                inputValue = Number(days);
        }

        if (passCondition && inputValue !== undefined && inputValue !== null) {
            passCondition
            switch (passCondition.rule) {
                case "<":
                    return inputValue < parseFloat(passCondition.value_1);
                case ">":
                    inputValue
                    return inputValue > parseFloat(passCondition.value_1);
                case "<=":
                    return inputValue <= parseFloat(passCondition.value_1);
                case ">=":
                    inputValue
                    return inputValue >= parseFloat(passCondition.value_1);
                case "=":
                    return inputValue === parseFloat(passCondition.value_1);
                case "!=":
                    return inputValue !== parseFloat(passCondition.value_1);
                case "BETWEEN":{
                    const value1 = parseFloat(passCondition.value_1);
                    const value2 = parseFloat(passCondition.value_2);
                    return inputValue >= value1 && inputValue <= value2;
                    }
                case "IN":{
                    const inValues = passCondition.value_1
                    return inValues.includes(inputValue.toString());
                }
                case "NOT IN":{
                    const notInValues = passCondition.value_1;
                    return !notInValues.includes(inputValue.toString());
                }
                // Add more cases for other pass condition rules if needed
                default:
                    return false;
            }
        }
        return true; // If no pass condition is specified, consider it as passing
    }

    // Validate each additional input
    const isAdditionalInputsValid = additionalInputs.every(validateInput);

    // Additional logic for other specific checks can be added here

    // Combine all checks and return the final result
    return isAdditionalInputsValid;
}

exports.updateQualityCheckFromDb = async (_input, user_id, client = null) => {
	_input = updateQualityCheckValidator(_input);
	client = await db.begin(client);

	try {
		let {
			quality_check_id,
			approved_qty,
			rejected_qty,
			rejected_batch,
            sample_quantity,
            original_qty,
            is_quantity_updated,
            on_hold_quantity,
            custom_fields
		} = _input;
        if(original_qty == 0 && on_hold_quantity > 0){
            let updated_qc = await updateQcHoldStatus({
                quality_check_id,
                status: 'ON_HOLD'
            },client)
            await client.commit();
            return updated_qc;
        }
        if(original_qty == 0)
            throw new Error("Original Quantity can not be zero").UI("Quantity to be tested can not be zero")
        if(is_quantity_updated && Number(sample_quantity) >=0 && Number(original_qty) >=0){
            await client.update("quality_checks",{sample_quantity, original_qty},{quality_check_id})
        }
        let tenant_product_ids= [];

		// Step 1: Check entity already verified or not
		let _quality_checks = await client.query(`
			SELECT 
                qc.*
			FROM quality_checks qc
			WHERE 
                qc.quality_check_id = ${quality_check_id}
		`);
		let quality_check = _quality_checks?.rows?.[0];
        if(quality_check.is_qc_on_hold)
            throw new Error("QC is on HOLD").UI("QC is on hold")

		if (!quality_check) 
            throw new Error().UI("Quality check does not exists or deleted.");

        quality_check.original_qty = Number(quality_check.original_qty);
        quality_check.additional_inputs = _input.additional_inputs;

        if(on_hold_quantity){
            let on_hold_qc = JSON.parse(JSON.stringify(quality_check));
            on_hold_qc.is_qc_on_hold = true;
            on_hold_qc.original_qty = on_hold_quantity;
            on_hold_qc.is_qc_retest = quality_check.is_qc_retest;
            if(quality_check.entity_name==='manufacturing_order'){
                let mo_adj;
                if (quality_check.entity_line_name == 'semi_finished_good') {
                    let _mo_adj = await client.query(`
                        SELECT * 
                        FROM manufacturing_order_line mol
                        INNER JOIN mo_adjustment moa ON mol.mo_line_id = moa.entity_id
                        WHERE 
                            mol.mo_line_id = ${quality_check.entity_line_id} and
                            mol.mo_id = ${quality_check.entity_id} and
                            moa.batch_id = ${quality_check.batch_id}
                    `);
                    mo_adj = _mo_adj?.rows?.[0];
                }
                else {
                    let _mo_adj = await client.query(`
                        SELECT * 
                        FROM manufacturing_order_finished_good mofg
                        INNER JOIN mo_adjustment moa ON mofg.mo_fg_id = moa.entity_id
                        WHERE 
                            mofg.mo_fg_id = ${quality_check.entity_line_id} and
                            mofg.mo_id = ${quality_check.entity_id} and
                            moa.batch_id = ${quality_check.batch_id}
                    `);
                    mo_adj = _mo_adj?.rows?.[0];
                }
                let mo_adjustment_id = mo_adj.mo_adjustment_id
                delete mo_adj.mo_adjustment_id;
                delete mo_adj.batch_id
                mo_adj.quantity = on_hold_quantity;
                mo_adj.is_qc_on_hold = true;
                await createManufacturingOrderAdjustment([mo_adj],user_id,client);
                await client.update("mo_adjustment",{
                    initial_quantity : Number(Number(mo_adj.initial_quantity) - Number(on_hold_quantity))
                },{
                    mo_adjustment_id
                })
            }else{
                // Default operation
                let operation = 'good_receiving_note';

                // Check if entity_id exists
                if (_input.entity_id) {
                    let result = await client.query(
                        `SELECT 
                            td1.tenant_id AS source_tenant_id, 
                            td2.tenant_id AS destination_tenant_id 
                        FROM good_receiving_note grn 
                        JOIN stock_transfer st ON st.stock_transfer_id = grn.grn_entity_id
                        JOIN tenant_department td1 ON td1.tenant_department_id = st.source_tenant_department_id
                        JOIN tenant_department td2 ON td2.tenant_department_id = st.destination_tenant_department_id
                        WHERE grn.grn_id = $1`, 
                        [_input.entity_id]
                    );

                    if (result.rows.length) {
                        let { source_tenant_id, destination_tenant_id } = result.rows[0];

                        operation = source_tenant_id == destination_tenant_id
                            ? 'good_receiving_note_internal_stock_transfer'
                            : 'good_receiving_note_external_stock_transfer';
                    } 
                    // If result is null or no GRN found, operation remains 'good_receiving_note' os it will be handled later
                }
                on_hold_qc.operation = operation

                await createQualityChecks(on_hold_qc,client)
            }
        }

        if (quality_check.qc_verification_type === 'AUTOMATIC') {
            const isQCPass = validateQualityCheck(quality_check);
            if (isQCPass) {
                approved_qty = quality_check.original_qty;
                rejected_qty = 0;
            } 
            else {
                rejected_qty = quality_check.original_qty;
                approved_qty = 0;
            }
        }

        let next_qc_date = null;
        let last_qc_date = new Date();
        
        if (quality_check.qc_validity_in_days)
            next_qc_date = new Date(Date.now() + quality_check.qc_validity_in_days*24*60*60*1000);
        
        let update_batch_object = {};
        update_batch_object = {
            lot_number: _input.lot_number,
            custom_batch_number: _input.custom_batch_number,
            cost_price: _input.cost_price,
            selling_price: _input.selling_price,
            mrp: _input.mrp,
            ar_number: _input?.ar_number,
            roll_no: _input?.roll_no,
            freight_cost: _input?.freight_cost,
            other_cost: _input?.other_cost,
            landed_cost: _input?.landed_cost || _input.cost_price,
            margin: _input?.margin,
            brand: _input?.brand,
            mfg_batch_no: _input?.mfg_batch_no,
            manufacturing_date: _input?.manufacturing_date,
            next_qc_date: next_qc_date,
            last_qc_date: last_qc_date,
            inventory_location_id: _input?.inventory_location_id,
        }

        if (approved_qty > 0) 
            update_batch_object.is_rejected_batch = false;
        
        // Step 2: Set tenant_product_batches
        let org_id;
        if  (JSON.stringify(update_batch_object) != '{}'){
            let result =await client.update("tenant_product_batches", update_batch_object, {
                batch_id: quality_check?.batch_id
            });
            let tenant_id = result.rows[0].tenant_id;
            let product_sku_id = result?.rows?.[0]?.product_sku_id;
            let org_config =await client.query(`
                SELECT org_config.inventory_config->'settings'->>'enable_auto_generation_of_ar_number' as enable_auto_generation_of_ar_number
                FROM org_configuration org_config
                JOIN tenant t ON t.org_id = org_config.org_id
                WHERE t.tenant_id = ${tenant_id}
                `)
                let product_result = await client.select('product_sku',{product_sku_id : product_sku_id})
                product_result = product_result?.rows?.[0];
                let enable_auto_generation_of_ar_number = org_config?.rows?.[0]?.enable_auto_generation_of_ar_number;
                if( (enable_auto_generation_of_ar_number == true || enable_auto_generation_of_ar_number =='true')){
                let automatic_ar_number = `${product_result?.ar_number_prefix}${product_result?.ar_number_counter}`
                if(automatic_ar_number == update_batch_object?.ar_number){
                    await arnumber_generator(product_result.product_sku_id, product_result.org_id)
                }
            }
            org_id = product_result.org_id;
        }
            
        // Step 3. Generate update object according to your entity
        let updateObject = {
			approved_by: user_id,
			additional_inputs: _input.additional_inputs,
			approved_at: new Date(),
			remark: _input.remark,
			measurement: _input.measurement,
			attachments: _input.attachments,
			rejected_qty: rejected_qty,
			entity_line_id: _input.entity_line_id,
			entity_id: _input.entity_id,
            modified_at: new Date(),
            status: 'COMPLETED',
            approved_qty: approved_qty
		};

		if (quality_check.entity_name==='good_receiving_note') {
            //. First fetch grn line by grn_id and grnLine Id
            let updated_quality_check = await updateGRNQualityChecks({
                quality_check: quality_check,
                approved_qty,
                rejected_qty,
                rejected_batch,
                user_id,
                updateObject,
                custom_fields
            }, client);

            if (updated_quality_check.rejected_batch_id) {
                updateObject.rejected_qty = rejected_qty;
                updateObject.rejected_batch_id = updated_quality_check.rejected_batch_id; 
            }
        }
        else if (quality_check.entity_name==='manufacturing_order') {
            //. If someone updating manufacturing order
            let rejected_batch_id = await updateMOQualityChecks({
                quality_checks: quality_check,
                approved_qty,
                rejected_qty,
                user_id,
                rejected_batch,
                ar_number: _input?.ar_number
            }, client); 

            if (rejected_batch_id) {
                updateObject.rejected_qty = rejected_qty;
                updateObject.rejected_batch_id = rejected_batch_id; 
            }
        }
        else if (quality_check.is_qc_retest) {
            // update reserved inventory
            let _reservation = await client.query(`
                select 
                    br.reservation_id
                from batch_reservations br 
                where br.reservation_entity_id = ${quality_check.quality_check_id} and 
                br.reservation_entity_name = 'QUALITY_CHECK' and 
                br.batch_id = ${quality_check.batch_id} and reserved_qty - consumed_qty > 0
            `)
            /**
             * @type {ReservationData}
             */
            let reservation = _reservation.rows[0]

            if(reservation?.reservation_id){
                await updateReservedInventory([{
                    reservation_id: reservation.reservation_id,
                    consumed_qty: (rejected_qty ?? 0) + (approved_qty ?? 0)
                }], client);
            }

            if (rejected_qty) {
                if (quality_check.original_qty === rejected_qty) {
                    let result = await client.update("tenant_product_batches", {
                        is_rejected_batch: true,
                    }, {
                        batch_id: quality_check?.batch_id
                    }) 
                    result = result.rows[0];
                    client.onCommit.push([refreshSKUQuantities,{tenant_product_id: result.tenant_product_id}]);
                    tenant_product_ids.push(result.tenant_product_id);
                }
                else {
                    let _tenant_product_batches = await client.query(`
                    SELECT 
                        tpb.*, 
                        ps.uom_id as product_uom_id 
                    FROM product_sku ps
                    JOIN tenant_product_batches tpb ON tpb.product_sku_id = ps.product_sku_id
                    WHERE 
                        tpb.batch_id = $1`,[quality_check?.batch_id]);
                    let __tenant_product_batch = _tenant_product_batches?.rows?.[0];
                    
                    let product_uom_id = __tenant_product_batch.product_uom_id;
                    delete __tenant_product_batch.product_uom_id;
                    
                    await batch_management([{
                        batch_id: quality_check?.batch_id,
                        quantity: global.negative(rejected_qty),
                        entity_name: 'quality_check',
                        reference_number: quality_check.qc_number,
                        entity_id: _input.quality_check_id,
                        uom_id: product_uom_id
                    }],client);
                    
                    delete __tenant_product_batch.batch_id;
                    
                    if (rejected_batch.custom_batch_number) 
                        __tenant_product_batch.custom_batch_number = rejected_batch.custom_batch_number;
                    
                    if (rejected_batch && !rejected_batch.batch_id) {
                        __tenant_product_batch.is_rejected_batch = true;
                        __tenant_product_batch.available_qty = 0;
                        let _created_bad_batch = await client.insert("tenant_product_batches", __tenant_product_batch);
                        let created_bad_batch = _created_bad_batch?.rows?.[0];
                        updateObject.rejected_batch_id = created_bad_batch.batch_id;
                        rejected_batch.batch_id = created_bad_batch.batch_id;
                    }

                    await batch_management([{
                        batch_id: rejected_batch.batch_id,
                        quantity: global.positive(rejected_qty),
                        entity_name: 'quality_check',
                        reference_number: quality_check.qc_number,
                        entity_id: _input.quality_check_id,
                        uom_id: product_uom_id
                    }], client);
                }
            }

            if (approved_qty) {
                if (quality_check.original_qty === approved_qty) {
                    let result = await client.update("tenant_product_batches",{
                        is_rejected_batch: false,
                    }, {
                        batch_id: quality_check?.batch_id
                    }) 
                    result = result.rows[0]
                    client.onCommit.push([refreshSKUQuantities,{tenant_product_id: result.tenant_product_id}]);
                    tenant_product_ids.push(result.tenant_product_id);
                }               
            }
        }
        else throw new Error("Invalid Entity Name for QC");

        if(quality_check.sample_quantity){
            if(!quality_check.is_qc_retest){
                let _tenant_product_batches = await client.query(`
                    SELECT 
                        tpb.*
                    FROM product_sku ps
                    JOIN tenant_product_batches tpb ON tpb.product_sku_id = ps.product_sku_id
                    WHERE 
                        tpb.batch_id = $1`,[quality_check?.batch_id]);
                    let __tenant_product_batch = _tenant_product_batches?.rows?.[0];
                    let product_uom_id = __tenant_product_batch.product_uom_id;
                    let entity_number;

                    if (quality_check.entity_name==='good_receiving_note') {
                        let _grnLine = await client.query(`
                            SELECT 
                              grnl.uom_id,grnl.uom_info,grn.grn_number
                            from good_receiving_note_line grnl
                            INNER JOIN good_receiving_note grn ON grn.grn_id = grnl.grn_id
                            WHERE 
                              grnl.grn_line_id = ${quality_check.entity_line_id} AND 
                              grnl.grn_id = ${quality_check.entity_id}
                        `);
                        let grnLine = _grnLine?.rows?.[0];
                        product_uom_id = grnLine.uom_id || grnLine?.uom_info?.[0]?.uom_id;
                        entity_number = grnLine.grn_number
                    }else if (quality_check.entity_name==='manufacturing_order' && quality_check.entity_line_name == 'semi_finished_good') {
                        let _mo_line = await client.query(`
                            SELECT mol.uom_id,mol.uom_info,mo.mo_number
                            FROM manufacturing_order_line mol
                            inner join manufacturing_order mo on mo.mo_id = mol.mo_id
                            WHERE 
                                mol.mo_line_id = ${quality_check.entity_line_id} and
                                mol.mo_id = ${quality_check.entity_id} 
                        `);
                        let moLine = _mo_line?.rows?.[0];
                        product_uom_id = moLine.uom_id || moLine?.uom_info?.uom_id;
                        entity_number = moLine.mo_number

                    }else if (quality_check.entity_name==='manufacturing_order') {
                        let _mo_fg = await client.query(`
                            SELECT mofg.uom_id, mofg.uom_info,mo.mo_number
                            FROM manufacturing_order_finished_good mofg
                            INNER JOIN manufacturing_order mo ON mofg.mo_id = mo.mo_id
                            WHERE 
                                mofg.mo_fg_id = ${quality_check.entity_line_id} and
                                mofg.mo_id = ${quality_check.entity_id} 
                        `);
                        let moFg = _mo_fg?.rows?.[0];
                        product_uom_id = moFg.uom_id || moFg?.uom_info?.uom_id;
                        entity_number = moFg.mo_number;
                    }
                    
                    delete __tenant_product_batch.product_uom_id;
                    delete __tenant_product_batch.batch_id;
                    delete __tenant_product_batch.batch_barcode;
                    __tenant_product_batch.available_qty = 0;
                    let _created_new_batch = await client.insert("tenant_product_batches", __tenant_product_batch);
                    let created_new_batch = _created_new_batch?.rows?.[0];

                    await batch_management([{
                        batch_id: created_new_batch.batch_id,
                        quantity: global.positive(quality_check.sample_quantity),
                        entity_name: quality_check.entity_name,
                        reference_number: entity_number,
                        entity_id: _input.quality_check_id,
                        uom_id: product_uom_id,
                        created_by: user_id,
                        remarks: "Sampling Inwards"
                    }], client);
                    await batch_management([{
                        batch_id: created_new_batch.batch_id,
                        quantity: global.negative(quality_check.sample_quantity),
                        entity_name: 'quality_check',
                        reference_number: quality_check.qc_number,
                        entity_id: _input.quality_check_id,
                        uom_id: product_uom_id,
                        created_by: user_id,
                        remarks: "Sampling Outwards"
                    }], client);
            }
            else{
                let _tenant_product_batches = await client.query(`
                    SELECT 
                        tpb.*, 
                        ps.uom_id as product_uom_id 
                    FROM product_sku ps
                    JOIN tenant_product_batches tpb ON tpb.product_sku_id = ps.product_sku_id
                    WHERE 
                        tpb.batch_id = $1`,[quality_check?.batch_id]);
                let __tenant_product_batch = _tenant_product_batches?.rows?.[0];
                
                let product_uom_id = __tenant_product_batch.product_uom_id;
                let _reservation = await client.query(`
                    select 
                        br.reservation_id
                    from batch_reservations br 
                    where br.reservation_entity_id = ${quality_check.quality_check_id} and 
                    br.reservation_entity_name = 'QUALITY_CHECK' and 
                    br.batch_id = ${quality_check.batch_id} and reserved_qty - consumed_qty > 0
                `)
                /**
                 * @type {ReservationData}
                 */
                let reservation = _reservation.rows[0]
    
                if(reservation?.reservation_id){
                    await updateReservedInventory([{
                        reservation_id: reservation.reservation_id,
                        consumed_qty: quality_check.sample_quantity
                    }], client);
                }
                await batch_management([{
                    batch_id: quality_check?.batch_id,
                    quantity: global.negative(quality_check.sample_quantity),
                    entity_name: 'quality_check',
                    reference_number: quality_check.qc_number,
                    entity_id: _input.quality_check_id,
                    uom_id: product_uom_id,
                    created_by: user_id,
                    remarks: "Sent For Sample"
                }],client);
            }
        }

        if(quality_check?.batch_id && custom_fields ){
            let customFieldsToBeInserted = [{
                entity_id: quality_check.batch_id,
                entity_name: CF_ENTITY.BATCH.name,
                custom_fields:custom_fields
            }]
            await updateCustomFieldValue(customFieldsToBeInserted,org_id, CF_ENTITY.BATCH.name, client)
        }
        if(updateObject?.rejected_batch_id && rejected_batch?.custom_fields ){
            let customFieldsToBeInserted = [{
                entity_id: updateObject?.rejected_batch_id,
                entity_name: CF_ENTITY.BATCH.name,
                custom_fields:rejected_batch?.custom_fields,
            }]
            await updateCustomFieldValue(customFieldsToBeInserted,org_id, CF_ENTITY.BATCH.name, client)
        }

		// Step 4: Update Quality Check
		let _update_qc = await client.update("quality_checks", updateObject, {
			quality_check_id: _input.quality_check_id,
		});
		_update_qc = _update_qc?.rows?.[0];
        
        if(updateObject?.rejected_batch_id){
            await sendRejectionNotification({rejected_batch_id:updateObject?.rejected_batch_id, user_id},client)
        }
        
        if(tenant_product_ids?.length>0){
            client.onCommit.push([refreshPendingQtyForQC,{tenant_product_ids: [...tenant_product_ids]}]);
        }
        client.onCommitIsSynchronous = true;
		await client.commit();

		return _update_qc;
	} catch (error) {
		await client.rollback();
		throw error;
	}
};
