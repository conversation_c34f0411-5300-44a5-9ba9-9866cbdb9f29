const Joi = require('joi');

exports.CREATE_PRODUCTION_RESOURCE_GROUP_VALIDATORS = Joi.object({
    resource_group_type: Joi.string().valid("MACHINE", "EQUIPMENT", "OTHER").default('MACHINE'),
    created_by: Joi.number().required(),
    org_id: Joi.number().required(),
    resource_group_name: Joi.string().required(),
    code: Joi.string().required(),
    description: Joi.string().required(),
    uom_id: Joi.number().required(),
    time_unit: Joi.string().valid('HOUR', 'MINUTE'),
    capacity: Joi.number().required(),
    shift: Joi.array(),
    status: Joi.string().valid("ACTIVE", "INACTIVE").default("ACTIVE"),
    supervisor: Joi.number().allow(null),
});

exports.UPDATE_PRODUCTION_RESOURCE_GROUP_VALIDATORS = Joi.object({
    resource_group_id: Joi.number().required(),
    resource_group_type: Joi.string().valid("MACHINE", "EQUIPMENT", "OTHER").default('MACHINE'),
    resource_group_name: Joi.string(),
    code: Joi.string().required(),
    description: Joi.string().required(),
    uom_id: Joi.number().required(),
    time_unit: Joi.string().valid('HOUR', 'MINUTE'),
    capacity: Joi.number().required(),
    shift: Joi.array(),
    status: Joi.string().valid("ACTIVE", "INACTIVE").default("ACTIVE"),
    supervisor: Joi.number().allow(null),
});

exports.CREATE_PRODUCTION_RESOURCE_VALIDATORS = Joi.object({
    status: Joi.string().valid(...["ONLINE", "OFFLINE", "BREAKDOWN"]),
    type: Joi.string().required(),
    capacity_per_hour: Joi.number().required(),
    created_by: Joi.number().required(),
    modified_by: Joi.number().required(),
    ideal_op_time_hrs_per_day: Joi.number(),
    ideal_time_efficiency: Joi.number(),
    org_id: Joi.number().required(),
    resource_name: Joi.string().required(),
    tenant_id: Joi.number().required(),
    resource_group_id: Joi.number().required(),
    seq_id: Joi.number().allow(null),
});

exports.UPDATE_PRODUCTION_RESOURCE_VALIDATORS = Joi.object({
    resource_id: Joi.number().required(),
    type: Joi.string().required(),
    status: Joi.string().valid(...["ONLINE", "OFFLINE", "BREAKDOWN"]),
    capacity_per_hour: Joi.number().required(),
    ideal_op_time_hrs_per_day: Joi.number(),
    ideal_time_efficiency: Joi.number(),
    modified_by: Joi.number().required(),
    resource_name: Joi.string().required(),
    occupancy_status: Joi.string(),
    resource_group_id: Joi.number()
});

exports.createProductionResourceGroupValidator = (_input) => __global_joi.validate(this.CREATE_PRODUCTION_RESOURCE_GROUP_VALIDATORS, _input);

exports.updateProductionResourceGroupValidator = (_input) => __global_joi.validate(this.UPDATE_PRODUCTION_RESOURCE_GROUP_VALIDATORS, _input);

exports.createProductionResourceValidator = (_input) => __global_joi.validate(this.CREATE_PRODUCTION_RESOURCE_VALIDATORS, _input);

exports.updateProductionResourceValidator = (_input) => __global_joi.validate(this.UPDATE_PRODUCTION_RESOURCE_VALIDATORS, _input);
