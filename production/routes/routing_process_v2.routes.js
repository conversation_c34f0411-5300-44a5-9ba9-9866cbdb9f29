//Production Resource Controllers
const {
    createProductionResourceController,
    getProductionResourceController,
    updateProductionResourceController,
    deleteProductionResourceController,
    createProductionResourceGroupController,
    updateProductionResourceGroupController,
    deleteProductionResourceGroupController,
    getProductionResourceGroupController
} = require('../controllers/routing_process/production_resources');

//Production Process Controllers
const {
    createProductionProcessController,
    getProductionProcessController,
    updateProductionProcessController,
    deleteProductionProcessController
} = require('../controllers/routing_process/production_processes')

//Template Production Route Controllers
const {
    createTemplateProductionRouteController,
    getTemplateProductionRouteController,
    updateTemplateProductionRouteController,
    deleteTemplateProductionRouteController
} = require('../controllers/routing_process/template_production_route')

// Production Route Controllers
const { 
    updateProductionRouteLineStatusController,
    updatePr<PERSON>inesController,
    getUsersListController,
    getPrLinesGroupByResourceController,
    getPrLinesGroupByResourceGroupController,
    getPrLinesGroupByAssigneeController
} = require('../controllers/routing_process/production_routes');

const { getJobWorkController } = require('../controllers/routing_process/production_routes/getJobWork.controller');
const { updateAssigneeController } = require('../controllers/routing_process/production_routes/updateAssignee.controller');
const { downloadJobWorkController } = require('../controllers/routing_process/production_routes/downloadJobWork.controller');


const { getJobWork } = require('../services/production_route_v2/getJobWork.service');
const { updateProductionRouteLineStatus } = require('../services/production_route_v2/updateProductionRouteLineStatus.service');

// Router - function
const router = require("express").Router();

// Authentication Middleware
const { authv1 } = require(process.env.PWD + '/common/middlewares/authentication');

//Production Resource Routers
router.post('/production_resource', authv1, createProductionResourceController);
router.delete('/production_resource', authv1, deleteProductionResourceController);
router.put('/production_resource', authv1, updateProductionResourceController);
router.get('/production_resource', authv1, getProductionResourceController);

// Production Resource Group Routers
router.post('/production_resource/group', authv1, createProductionResourceGroupController);
router.delete('/production_resource/group', authv1, deleteProductionResourceGroupController);
router.get('/production_resource/group', authv1, getProductionResourceGroupController);
router.put('/production_resource/group', authv1, updateProductionResourceGroupController);


//Production Job Works
router.get('/job_work', authv1, getJobWorkController);
router.put('/update_assignee', authv1, updateAssigneeController);


//Production Process Routers
router.post('/production_process', authv1, createProductionProcessController);
router.delete('/production_process', authv1, deleteProductionProcessController);
router.put('/production_process', authv1, updateProductionProcessController);
router.get('/production_process', authv1, getProductionProcessController);

//Template Production Route Routers
router.post('/template_production_route', authv1, createTemplateProductionRouteController);
router.delete('/template_production_route', authv1, deleteTemplateProductionRouteController);
router.put('/template_production_route', authv1, updateTemplateProductionRouteController);
router.get('/template_production_route', authv1, getTemplateProductionRouteController);

// Production Route Routers
router.put('/production_routes/status', authv1, updateProductionRouteLineStatusController);
router.put('/production_route_lines', authv1, updatePrLinesController);
router.get('/users', authv1, getUsersListController);
router.get('/jw_group_by_resource', authv1, getPrLinesGroupByResourceController);
router.get('/jw_group_by_resource_group', authv1, getPrLinesGroupByResourceGroupController);
router.get('/jw_group_by_assignee', authv1, getPrLinesGroupByAssigneeController);

router.get('/job_work_v2', authv1,  (req, res) => _EXPRESS.common_controller(req, res, getJobWork, 'query'));
router.put('/status_v2', authv1, (req, res) => _EXPRESS.common_controller(req, res, updateProductionRouteLineStatus, 'body'));
router.get('/job_work_v2/download', authv1, downloadJobWorkController);

// SHIFT
router.post('/shift', authv1, (req, res) => _EXPRESS.common_controller(req, res, createShift));
router.get('/shift', authv1, (req, res) => _EXPRESS.common_controller(req, res, getShiftsList, 'combined'));
router.get('/shift/:id', authv1, (req, res) => _EXPRESS.common_controller(req, res, getShift, 'combined'));
router.put('/shift/:id', authv1, (req, res) => _EXPRESS.common_controller(req, res, updateShift, 'combined'));
router.delete('/shift/:id', authv1, (req, res) => _EXPRESS.common_controller(req, res, deleteShift, 'combined'));

module.exports = router;
