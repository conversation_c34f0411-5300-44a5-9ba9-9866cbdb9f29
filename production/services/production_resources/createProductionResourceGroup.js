const { db } = require("../../../config");
const { createProductionResourceGroupValidator } = require('../../validators/production_resources.joi');

exports.createProductionResourceGroup = async (_input, client = null) => {
    
    client = await db.begin(client);

    try {

        let productionResourceGroupObj = createProductionResourceGroupValidator(_input);

        const orgId = productionResourceGroupObj.org_id;
        const supervisor = productionResourceGroupObj.supervisor;

        // CHECK IF THE SUPERVISOR IS A REGISTERED USER IN THE ORGANISATION
        let userIds = await client.query(`
            SELECT user_id
            FROM app_user
            WHERE org_id = ${orgId};
        `);
        userIds = userIds?.rows?.map(i => i?.user_id);
        if (!userIds?.find((id) => (id === supervisor))) {
            throw new Error('Supervisor is not a registered user in the organization.');
        }
                        
        let _createdProductionResourceGroup = await client.insert("production_resource_groups", productionResourceGroupObj);

        await client.commit();
        return _createdProductionResourceGroup.rows[0];
    } catch (error) {

        await client.rollback();
        throw error;
    }
}