const { db } = require("../../../config");

exports.getProductionResourceGroup = async (params, client = db) => {
    // eslint-disable-next-line no-useless-catch
    try {

        let whereCases=[];

        const orgId = params?.org_id;
        if (!orgId) throw new Error("org_id is mandatory");
        whereCases.push (`prg.org_id = ${orgId}`);

        const searchKeyword = params?.search_keyword;
        const capacity = params?.capacity;
        const shift = params?.shift;
        const status = params?.status;
        
        if (searchKeyword) whereCases.push (`(prg.resource_group_name ILIKE '%${searchKeyword}%' OR prg.code ILIKE '%${searchKeyword}%')`);
        if (capacity) whereCases.push (`prg.capacity = ${capacity}`);
        // if (shift) whereCases.push (`prg.shift = '${shift}'`);
        if (status) whereCases.push (`prg.status = '${status}'`);

        let _getProductionResourceGroups = await client.query(
            `
                SELECT
                (
                    SELECT count(prg.*)
                    FROM production_resource_groups prg
                    ${whereCases.length ? "WHERE " + whereCases.join(" AND ") : ""} 
                ),
                array_to_json(array_agg(prg_list.*)) AS production_resource_groups
                FROM
                (
                    SELECT prg.*,
                    (
                        SELECT row_to_json(cbi) AS created_by_info
                        FROM
                        (
                            SELECT usr.first_name, usr.last_name,
                                usr.username, usr.mobile
                            FROM app_user usr
                            where usr.user_id = prg.created_by
                        ) cbi
                    )
                    FROM production_resource_groups prg
                    ${whereCases.length ? "WHERE " + whereCases.join(" AND ") : ""} 
                    ORDER BY prg.created_at DESC
                ) prg_list
            `
        );

        return {
            data: _getProductionResourceGroups?.rows?.[0]?.production_resource_groups,
            count: _getProductionResourceGroups?.rows?.[0]?.count
        }

    } catch (error) {
        throw error;
    }
}