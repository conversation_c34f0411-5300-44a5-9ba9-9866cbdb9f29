const { db } = require("../../../config");

exports.deleteProductionResourceGroup = async (_input, client = db) => {
    try {

        if (!_input.resource_group_id)
            throw new Error('resource_group_id is required!');

        if (!_input.org_id)
            throw new Error('org_id is required!');

        let _deletedProductionResourceGroup = await client.query(
            `
            DELETE FROM production_resource_groups
            WHERE 
                resource_group_id = $1 AND
                org_id = $2
            RETURNING *
            `, [_input.resource_group_id, _input.org_id]
        );
        
        if (_deletedProductionResourceGroup.rowCount == 0) throw new Error().UI('Production Resource Group does not exist!');

        return _deletedProductionResourceGroup.rows[0];

    } catch (error) {
        if(error?.message.includes("violates foreign key constraint")){
            throw new Error().UI("Cannot delete !! This resource group has been linked to some resources/routes!!")
        }
        throw error;
    }
}