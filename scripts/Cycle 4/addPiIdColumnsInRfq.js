const { db } = require('../../config');

exports.addPiIdColumnsInRfq = async (client = null) => {
    client = await db.begin(client);

    try {

        await client.query (`
            ALTER TABLE rfq
            ADD COLUMN pi_id integer
            DEFAULT null;
        `);
        await client.commit();
    } catch (error) {

        await client.rollback();
        throw error;
    }
};

// this.addPiIdColumnsInRfq ();

exports.addPiLineIdColumnsInRfqLines = async (client = null) => {
    client = await db.begin(client);

    try {

        await client.query (`
            ALTER TABLE rfq_lines
            ADD COLUMN pi_line_id integer
            DEFAULT null;
        `);
        await client.commit();
    } catch (error) {

        await client.rollback();
        throw error;
    }
};

// this.addPiLineIdColumnsInRfqLines ();