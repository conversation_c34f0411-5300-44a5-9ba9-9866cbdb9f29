const { db } = require('../../config');

exports.addColumnsInProductionResourceGroups = async (client = null) => {

    client = await db.begin(client);

    try {

        await client.query(`
            ALTER TABLE production_resource_groups
            ADD COLUMN code VARCHAR(20),
            ADD COLUMN description TEXT,
            ADD COLUMN uom_id INT,
            ADD COLUMN time_unit VARCHAR(20),
            ADD COLUMN capacity NUMERIC,
            ADD COLUMN shift JSONB,
            ADD COLUMN status VARCHAR(20),
            ADD COLUMN supervisor INT
        `);

        await client.commit();
    } catch (error) {

        await client.rollback();
        throw error;
    }
};

// this.addColumnsInProductionResourceGroups();

exports.createShiftTable = async (client = null) => {

    client = await db.begin(client);

    try {

        await client.query(`
            CREATE TABLE shifts (
                shift_id SERIAL PRIMARY KEY,
                shift_name VARCHAR(100) NOT NULL,
                shift_code VARCHAR(50) NOT NULL UNIQUE,
                start_time TIME NOT NULL,
                end_time TIME NOT NULL,
                total_break_time INTERVAL NOT NULL,
                shift_days BIT(7) NOT NULL DEFAULT B'1111100',
                status VARCHAR(20) NOT NULL CHECK (status IN ('ACTIVE','INACTIVE')),
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW(),
                org_id INT NOT NULL REFERENCES organisation(org_id),
                tenant_id INT NOT NULL REFERENCES tenant(tenant_id),
                created_by INT NOT NULL
            );
        `);

        console.log('~ TABLE CREATED SUCCESSFULLY');
        await client.commit();
    } catch (error) {

        await client.rollback();
        throw error;
    }
};

// this.createShiftTable();