const { common_address_v1 } = require('../../common/addresses/common.address.joi');

const Joi = require('joi');

let createCreditNoteLinesRule = Joi.object({
    cn_line_id: Joi.number().allow(null),
    tenant_product_id: Joi.number().allow(null),
    offer_price: Joi.number().required(),
    unit_price: Joi.number().required(),
    discount_percent: Joi.number().allow(null),
    tax_id: Joi.number().required(),
    quantity: Joi.number().required(),
    remarks: Joi.string().allow('', null),
    product_sku_name: Joi.string().required(),
    uom_id: Joi.number().allow(null).required(),
    invoice_line_id: Joi.number().allow(null),
    uom_info: Joi.array().items(Joi.object({
        uom_id: Joi.number().required(),
        uom_name: Joi.string().required(),
        uqc: Joi.string().required(),
        ratio: Joi.number().required(),
        precision: Joi.number().required()
    })).allow(null).min(1).required(),
    tax_group_info: Joi.object().required(),
    product_batches: Joi.array().items(Joi.object({
        batch_id: Joi.number().required(),
        quantity: Joi.number().required(),
        is_rejected_batch: Joi.boolean().default(false),
        inventory_location_id: Joi.number().allow(null), // TODO : Remove this 
    })),
    update_stock_qty: Joi.boolean().default(false),
    line_discount_percentage: Joi.number().allow(null),
    hsn_code : Joi.string().allow(null,''),
    unicommerce_item_sku : Joi.string().allow(null,''),
    shopify_refund_line_id: Joi.string().allow(null),
    refund_line_id : Joi.number().allow(null),
    returned_quantity: Joi.number().allow(0),
    is_discount_in_percent: Joi.boolean().default(true),
});
let createReturnSLipLinesRule = Joi.object({
    cn_line_id: Joi.number().allow(null),
    tenant_product_id: Joi.number().allow(null),
    quantity: Joi.number().required(),
    remarks: Joi.string().allow('', null),
    product_sku_name: Joi.string().required(),
    uom_id: Joi.number().allow(null).required(),
    entity_line_id: Joi.number().allow(null),
    uom_info: Joi.array().items(Joi.object({
        uom_id: Joi.number().required(),
        uom_name: Joi.string().required(),
        uqc: Joi.string().required(),
        ratio: Joi.number().required(),
        precision: Joi.number().required()
    })).allow(null).min(1).required(),
    product_batches: Joi.array().items(Joi.object({
        batch_id: Joi.number().required(),
        quantity: Joi.number().required(),
        is_rejected_batch: Joi.boolean().default(false),
        inventory_location_id: Joi.number().allow(null), // TODO : Remove this 
    })),
    update_stock_qty: Joi.boolean().default(false),
    hsn_code : Joi.string().allow(null,''),
    returned_quantity: Joi.number().allow(0),
    invoice_line_id: Joi.number().allow(null),
});

let createCreditNoteRule = Joi.object({
    customer_id: Joi.number().required(),
    remarks: Joi.string().allow('', null),
    charge_1_name: Joi.string().allow('', null).default(''),
    charge_2_name: Joi.string().allow('', null).default(''),
    charge_1_value: Joi.number().default(0),
    charge_2_value: Joi.number().default(0),
    shipping_address: Joi.number().allow(null),
    billing_address: Joi.number().allow(null),
    cn_lines: Joi.array().items(createCreditNoteLinesRule).min(1).required(),
    tenant_id: Joi.number().required(),
    invoice_id: Joi.number().allow(null),
    cn_date: Joi.string().allow('', null),
    reason: Joi.string().allow('', null).required(),
    customer_info: Joi.object().required(),
    tenant_info: Joi.object().required(),
    attachments: Joi.array().required(),
    shipping_address_info: common_address_v1.allow(null),
    billing_address_info: common_address_v1.allow(null),
    status: Joi.string().valid('DRAFT', 'CONFIRMED').required(),
    terms_and_conditions: Joi.string().allow('', null),
    custom_fields: Joi.array().default(null),
    read_only: Joi.boolean().default(false),
    source: Joi.string(),
    other_charges: Joi.array().items(Joi.object().keys({
        charge_name: Joi.string().required(),
        charge_amount: Joi.number().required(),
        charge_type: Joi.string().allow(null, ''),
        ledger_name: Joi.string().allow(null, ''),
        tax_info: Joi.object().allow(null),
        charge_sac_code: Joi.string().allow(null,'')
    })).allow(null, ''),
    discount_percentage: Joi.number().allow(null),
    is_discount_before_tax: Joi.boolean().default(true).allow(null),
    is_line_wise_discount: Joi.boolean().allow(null),
    tcs_id:Joi.number().allow(null),
    tcs_info:Joi.object().allow(null),
    org_currency_id:Joi.number().allow(null),
    conversion_rate:Joi.number().allow(null),
    unicommerce_return_info : Joi.object().allow(null),
    unicommerce_shipping_package_code : Joi.string().allow("",null),
    bypass_inventory_change: Joi.boolean().default(false),
    shopify_order_id: Joi.string().allow(null),
    shopify_refund_id: Joi.string().allow(null),
    refund_id: Joi.number().allow(null),
    apply_round_off : Joi.boolean().default(true),
    without_lines: Joi.boolean().default(false),
    tenant_department_id: Joi.number().allow(null),
    apply_credit_note: Joi.boolean().default(false).allow(null),
    narration: Joi.string().allow(null, ""),
    is_discount_in_percent: Joi.boolean().default(true),
    tally_credit_note_account_name:Joi.string().allow(null,''),
    doc_type : Joi.string().allow(...['CREDIT_NOTE','RETURN_SLIP']).allow(null).default('CREDIT_NOTE'),
    seq_id: Joi.number().allow(null),
    credit_note_number: Joi.string().allow(null,'')
});
let createReturnSlipRule = Joi.object({
    customer_id: Joi.number().required(),
    remarks: Joi.string().allow('', null),
    cn_lines: Joi.array().items(createReturnSLipLinesRule).min(1).required(),
    tenant_id: Joi.number().required(),
    entity_id: Joi.number().allow(null),
    entity_name: Joi.string().allow(null),
    cn_date: Joi.string().allow('', null),
    reason: Joi.string().allow('', null).required(),
    customer_info: Joi.object().required(),
    tenant_info: Joi.object().required(),
    attachments: Joi.array().required(),
    status: Joi.string().valid('DRAFT', 'CONFIRMED').required(),
    terms_and_conditions: Joi.string().allow('', null),
    custom_fields: Joi.array().default(null),
    read_only: Joi.boolean().default(false),
    source: Joi.string(),
    bypass_inventory_change: Joi.boolean().default(false),
    apply_round_off : Joi.boolean().default(true),
    tenant_department_id: Joi.number().allow(null),
    doc_type : Joi.string().allow(...['CREDIT_NOTE','RETURN_SLIP']).allow(null).default('CREDIT_NOTE'),
    invoice_id: Joi.number().allow(null),
    seq_id: Joi.number().allow(null),
    credit_note_number: Joi.string().allow(null,'')
});

let updateCreditNoteRule = Joi.object({
    cn_id: Joi.number().required(),
    remarks: Joi.string().allow('', null),
    charge_1_name: Joi.string().allow('', null),
    charge_2_name: Joi.string().allow('', null),
    charge_1_value: Joi.number(),
    charge_2_value: Joi.number(),
    shipping_address: Joi.number().allow(null),
    billing_address: Joi.number().allow(null),
    cn_lines: Joi.array().items(createCreditNoteLinesRule).min(1).required(),
    cn_date: Joi.string().allow('', null),
    reason: Joi.string(),
    customer_info: Joi.object(),
    tenant_info: Joi.object(),
    attachments: Joi.array(),
    shipping_address_info: common_address_v1.allow(null),
    billing_address_info: common_address_v1.allow(null),
    status: Joi.string().valid('DRAFT', 'CONFIRMED').required(),
    terms_and_conditions: Joi.string().allow('', null),
    custom_fields: Joi.array().allow(null),
    other_charges: Joi.array().items(Joi.object().keys({
        charge_name: Joi.string().required(),
        charge_amount: Joi.number().required(),
        charge_type: Joi.string().allow(null, ''),
        ledger_name: Joi.string().allow(null, ''),
        tax_info: Joi.object().allow(null),
        charge_sac_code: Joi.string().allow(null,'')
    })).allow(null, ''),
    discount_percentage: Joi.number().allow(null),
    is_discount_before_tax: Joi.boolean().default(true).allow(null),
    is_line_wise_discount: Joi.boolean().allow(null),
    tcs_id:Joi.number().allow(null),
    tcs_info:Joi.object().allow(null),
    org_currency_id:Joi.number().allow(null),
    conversion_rate:Joi.number().allow(null),
    tenant_department_id: Joi.number().allow(null),
    narration: Joi.string().allow(null, ""),
    is_discount_in_percent: Joi.boolean().default(true),
    tally_credit_note_account_name:Joi.string().allow(null,''),
    doc_type : Joi.string().allow(...['CREDIT_NOTE','RETURN_SLIP']).allow(null).default('CREDIT_NOTE'),
    tenant_id: Joi.number().required(),
    credit_note_number: Joi.string().allow(null,'')
});
let updateReturnSlipRule = Joi.object({
    cn_id: Joi.number().required(),
    remarks: Joi.string().allow('', null),
    cn_lines: Joi.array().items(createReturnSLipLinesRule).min(1).required(),
    cn_date: Joi.string().allow('', null),
    reason: Joi.string(),
    customer_info: Joi.object(),
    tenant_info: Joi.object(),
    attachments: Joi.array(),
    status: Joi.string().valid('DRAFT', 'CONFIRMED').required(),
    terms_and_conditions: Joi.string().allow('', null),
    custom_fields: Joi.array().allow(null),
    tenant_department_id: Joi.number().allow(null),
    doc_type : Joi.string().allow(...['CREDIT_NOTE','RETURN_SLIP']).allow(null).default('CREDIT_NOTE'),
    tenant_id: Joi.number().required(),
    credit_note_number: Joi.string().allow(null,'')
});

exports.CREDIT_NOTE_VALIDATORS = {
    CREATE_CREDIT_NOTE: Joi.array().items(createCreditNoteRule).min(1).required(),
    UPDATE_CREDIT_NOTE: Joi.array().items(updateCreditNoteRule).min(1).required(),
};

exports.RETURN_SLIP_VALIDATOR = {
    RETURN_SLIP: Joi.array().items(createReturnSlipRule).min(1).required(),
    UPDATE_RETURN_SLIP: Joi.array().items(updateReturnSlipRule).min(1).required(),
}

exports.LINK_CREDIT_NOTE_PAYMENT_VALIDATOR = Joi.array().items(Joi.object({
    cn_id: Joi.number().required(),
    amount: Joi.number().greater(0).required(),
    payment_id: Joi.number().required(),
    applied_payment_type: Joi.string().required()
})
).min(1).required();

exports.createCreditNoteValidator = (_input) => __global_joi.validate(this.CREDIT_NOTE_VALIDATORS.CREATE_CREDIT_NOTE, _input);

exports.updateCreditNoteValidator = (_input) => __global_joi.validate(this.CREDIT_NOTE_VALIDATORS.UPDATE_CREDIT_NOTE, _input);

exports.linkCreditNotePaymentValidators = (_input) => __global_joi.validate(this.LINK_CREDIT_NOTE_PAYMENT_VALIDATOR, _input);

exports.createReturnSlipValidator = (_input) => __global_joi.validate(this.RETURN_SLIP_VALIDATOR.RETURN_SLIP, _input);

exports.updateReturnSlipValidator = (_input) => __global_joi.validate(this.RETURN_SLIP_VALIDATOR.UPDATE_RETURN_SLIP, _input);
