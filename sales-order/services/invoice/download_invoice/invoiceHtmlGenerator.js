// @ts-nocheck
const ejs = require("ejs");
const { getInvoice } = require("../getInvoice");
const { db } = require(process.env.PWD + "/config");
const QRCode = require("qrcode"); // Dont comment this. It is needed if code string contains QRCode
const { render_document } = require("../../../../common/document-engine");
const { cnHtmlGenerator } = require("../../credit_note/cnHtmlGenerator");
const { calculateInvoiceForEjs } = require("./calculateInvoiceForEjs");
const { applyUserPolicy }= require("../../../../common/document-engine/applyUserPolicy");
const { generateTaxAnalysisPayload } = require("./helpers/generateTaxAnalysisPayload");
let _defaultConfiguration = {
	"primary_colour": "#4485df",
	"secondary_colour": "#EFF5FE",
	"print_line_remarks": true,
	"print_remark": true,
	"print_line_batches": true,
	"footer_message": "",
	"title": {
		"text": "Invoice",
		"font_size": "26px"
	},
	"SECTION_A": {
		"left_part": {
			"font_size": "16px"
		},
		"right_part": {
			"title": {
				"font_size": "16px"
			},
			"font_size": "16px"
		}
	},
	"SECTION_B": {
		"left_left_part": {
			"title": {
				"text": "Sold By",
				"font_size": "16px",
				"color": "#0f1010"
			},
			"font_size": "16px",
			"background_color": "#6a6a6a1a"
		},
		"left_part": {
			"title": {
				"text": "Bill To",
				"font_size": "16px"
			},
			"font_size": "16px"
		},
		"right_part": {
			"title": {
				"text": "Ship To",
				"font_size": "16px"
			},
			"font_size": "16px"
		}
	},
	"SECTION_C": {
		"font_size": "16px"
	},
	"SECTION_D": {
		"left_part": {
			"font_size": "16px"
		},
		"right_part": {
			"font_size": "16px"
		}
	},
	"SECTION_E": {
		"left_part": {
			"font_size": "16px"
		},
		"right_part": {
			"font_size": "16px"
		}
	}
};

exports.invoiceHtmlGenerator = async (
	invoiceId,
	beautify,
	type = "HTML",
	downloadButtonUrl,
	copies,
	print_credit_note =true,
	download_document_in_base_currency,
	action_by,
	required_tax_analysis_report=false
) => {

	let currency_conversion = true;
    if(download_document_in_base_currency == true || download_document_in_base_currency == 'true')
        currency_conversion = false;

	// Step 1: Get Invoice
	let { invoices } = await getInvoice({ invoice_id: invoiceId, user_id: action_by }, 1, 10, undefined, currency_conversion);
	let invoice = invoices?.[0];
	if (!invoice) throw new Error("Invoice does not exists");
	if(required_tax_analysis_report == 'true' || required_tax_analysis_report == true )
		invoice = generateTaxAnalysisPayload(invoice)
	
	invoice.tenant_info.identification_number = invoice.tenant_identification_number;

	invoice.hide_selling_price = false;
	if(action_by){
		// get data masking info for hiding cost price
		let result = await db.query(
			`
			SELECT COALESCE(dmp.hide_selling_price, false) AS hide_selling_price
					FROM app_user au 
					LEFT JOIN data_masking_policy dmp ON dmp.data_masking_policy_id = au.data_masking_policy_id
					WHERE au.user_id =${action_by}`
		)
		result = result?.rows?.[0];
		invoice.hide_selling_price = result?.hide_selling_price || false;
	}
	if(invoice.document_tenant_configuration?.print_total_quantity){
		let org_config = await db.query(`
						SELECT  oc.sales_config->'sub_modules'->'invoice'->'settings'->'calculate_total_quantity_with_parent_quantity_for_bundle_products' AS sum_total_quantity_with_parent_quantity_for_bundle_products
                        FROM tenant tnt
                        JOIN org_configuration oc 
                            ON oc.org_id = tnt.org_id
                        WHERE tnt.tenant_id = ${invoice?.tenant_id}
		`)
		invoice.calculate_total_quantity_with_parent_quantity_for_bundle_products = org_config?.rows[0]?.sum_total_quantity_with_parent_quantity_for_bundle_products;
	}

	if(download_document_in_base_currency == true || download_document_in_base_currency == 'true')
        invoice.org_currency_info = invoice?.base_currency_info;

	// Step 2: Set default values
	if (type?.toUpperCase() === 'PDF') beautify = false;
	if (!beautify) downloadButtonUrl = "";
	let defaultConfiguration = global.clone(_defaultConfiguration);
	defaultConfiguration.primary_colour = invoice?.document_tenant_configuration?.primary_document_color_hex || invoice?.tenant_configuration?.po_doc_primary_colour || defaultConfiguration?.primary_colour;
	defaultConfiguration.secondary_colour = invoice?.document_tenant_configuration?.secondary_document_color_hex || invoice?.tenant_configuration?.po_doc_secondary_colour || defaultConfiguration?.secondary_colour;


	let logo_height =  Number(invoice?.document_tenant_configuration?.logo_height)||50;

        if(Number(logo_height)<50)logo_height = 50;
        if(Number(logo_height)>100)logo_height = 100;
	// Step 3: Calculator
	// invoice = calculateInvoiceForEjs(invoice, {
    //     currency_conversion: false
    // });
	invoice.invoice_total_in_words = global.inWords(invoice.invoice_grand_total+invoice?.invoice_round_off, invoice?.org_currency_info?.currency_code || 'INR');
	if(required_tax_analysis_report == 'true' || required_tax_analysis_report == true )
		invoice.tax_amount_total_in_words = global.inWords(invoice.total_tax_amount, invoice?.org_currency_info?.currency_code || 'INR');
	
	if (invoice.irn)
		invoice.irn_uri = `data:image/png;base64,${invoice.signed_qr_code}`;
	if (invoice?.document_tenant_configuration?.doc_sign_base_64?.includes("https://")) {
		invoice.document_tenant_configuration.doc_sign_base_64 = await global.urlToBase64(invoice?.document_tenant_configuration?.doc_sign_base_64)
	}

	// Step 3A: Render custom templates of the client fetched from the database
	let custom_template = {};
	let custom_html_section_d = invoice.document_tenant_configuration?.custom_html_section_d;
	let default_t_and_c = invoice.document_tenant_configuration?.default_t_and_c;

	if (custom_html_section_d) {
		custom_html_section_d = JSON.parse(custom_html_section_d);
		let templateData = custom_html_section_d.data;

		await Promise.all(Object.keys(templateData).map(async (key) => {
			try {
				let code = `(async () => { return ${templateData[key]}; })()`;
				const variable_names = (code.match(/\${(.*?)}/g) || []).map(match => match.slice(2, -1));
				variable_names.map(v_name => {
					let v_value = eval(`${v_name}`);
					code = code.replaceVariableFromCodeString(`${v_name}`, v_value);
				});

				const result = await eval(code);
				templateData[key] = result;
			} catch (error) {
				console.error(`Error executing code for key ${key}:`, error);
			}
		}));
		custom_template.rendered_html_section_d = ejs.render(custom_html_section_d.template, templateData);
	}

	if (default_t_and_c) {
		custom_template.rendered_default_t_and_c = ejs.render(default_t_and_c);
	}
	// setting up currency code
	invoice.currency_code = invoice?.org_currency_info ? invoice?.org_currency_info?.currency_code : invoice?.organisation_info?.currency_code;
	let page_top_padding = 0;
    let page_bottom_padding = 0;
	// Step 4: Render Invoice
	let payload = {
		invoice: invoice,
		configuration: defaultConfiguration,
		download_button_url: beautify ? downloadButtonUrl : "",
		beautify: beautify,
		title_logo_base_64: (invoice?.tenant_configuration?.logo_base_64 || invoice?.organisation_info?.org_logo || ""),
		footer_message: _defaultConfiguration.footer_message,
		custom_template: custom_template,
		print_letter_head: invoice?.document_tenant_configuration?.print_letter_head,
		label:'',
		page_top_padding:invoice?.document_tenant_configuration?.print_letter_head ? page_top_padding :0,
        page_bottom_padding:invoice?.document_tenant_configuration?.print_letter_head ? page_bottom_padding : 0,
		applyUserPolicy: applyUserPolicy,
		logo_height
	};
	let template = invoice.document_tenant_configuration?.document_template;
	let html
	let invoice_html_array =[]
	if(template === 'Template2'){
		if(copies && copies>=1){
			for(let i=0;i<copies;i++){
				if(i == 0){
					payload.label ='Original'
				}
				if(i == 1){
					payload.label ='Duplicate for Transporter'
				}
				if(i == 2){
					payload.label ='Office Copy'
				}
				if(i == 3){
					payload.label ='Extra Copy'
				}
				html = await render_document("invoice_new_template.ejs", payload);
				invoice_html_array.push(html)
			}
		}else{
			html = await render_document("invoice_new_template.ejs", payload);
		}
	}
	else if(template === 'Template3'){
		if(copies && copies>=1){
			for(let i=0;i<copies;i++){
				if(i == 0){
					payload.label ='Original'
				}
				if(i == 1){
					payload.label ='Duplicate for Transporter'
				}
				if(i == 2){
					payload.label ='Office Copy'
				}
				if(i == 3){
					payload.label ='Extra Copy'
				}
				html = await render_document("invoice_template_3.ejs", payload);
				invoice_html_array.push(html)
			}
		}else{
			html = await render_document("invoice_template_3.ejs", payload);
		}
	}
	else{
		if(copies && copies>=1){
			for(let i=0;i<copies;i++){
				if(i == 0){
					payload.label ='Original'
				}
				if(i == 1){
					payload.label ='Duplicate for Transporter'
				}
				if(i == 2){
					payload.label ='Office Copy'
				}
				if(i == 3){
					payload.label ='Extra Copy'
				}
				html = await render_document("invoice_body.ejs", payload);
				invoice_html_array.push(html)
			}
		}else{
			html = await render_document("invoice_body.ejs", payload);
		}
	}
	let tax_analysis_report_html;
	if(required_tax_analysis_report == 'true' || required_tax_analysis_report == true )
		tax_analysis_report_html = await render_document("tax_analysis_report.ejs",payload)

	// Step 5: Get credit note
	const _invoice_cn = await db.query(`
        SELECT cn_id, doc_type FROM credit_note
        where invoice_id = ${invoice.invoice_id} AND status = 'CONFIRMED'
    `);
	const invoice_cn = _invoice_cn?.rows || [];
	let invoice_cn_html= [];
	if(print_credit_note){
		invoice_cn_html = await Promise.all(invoice_cn.map(async (cn) => {
			let _result = await cnHtmlGenerator(cn.cn_id, beautify, '', download_document_in_base_currency, action_by, cn?.doc_type);
			return _result.html;
		})
		);
	}

	// Step 6: generate invoice file name
	const fileName = `${invoice?.invoice_number || invoice?.invoice_id} - ${invoice?.customer_info?.customer_name ||
		invoice?.customer_info?.customer_info?.customer_name ||
		"Invoice"
		}.pdf`;

	return {
		fileName: fileName,
		html:invoice_html_array?.length>1 ? [...invoice_html_array,tax_analysis_report_html,...invoice_cn_html] : [html,tax_analysis_report_html, ...invoice_cn_html],
		configuration: defaultConfiguration,
		invoice: invoice,
	};
};
