// Importing Controller(s)
const {
    createDc<PERSON>ontroller,
    getDcController,
    downloadDcController,
    updateDcController,
    deleteDcController
} = require('../controllers/delivery_challan');

const { updateDeliveryChallanStatus } =  require('../services/delivery-challan');

// Router - function
const router = require("express").Router();

// Authentication Middleware
const { authv1 } = require(process.env.PWD + '/common/middlewares/authentication');

router.post('/', authv1, createDcController)
router.get('/', authv1, getDcController)
router.get('/download', downloadDcController)
router.put('/', authv1, updateDcController)
router.delete('/', authv1, deleteDcController)
router.put('/status', authv1, (req, res) => _EXPRESS.common_controller(req, res, updateDeliveryChallanStatus, 'body'))

module.exports = router;