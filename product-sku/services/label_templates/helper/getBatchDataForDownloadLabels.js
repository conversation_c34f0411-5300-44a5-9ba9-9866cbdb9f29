// Import other files here!
exports.getBatchInfoForDownloadLabels =(batch_config, batch, line_custom_fields) => {

    let batch_info = [] ;

    if (!!line_custom_fields?.length) {
        line_custom_fields?.filter((item) => (item?.is_printable && item?.is_active && item?.field_type !== 'ATTACHMENT' && item?.field_type !== 'CHECKBOX'))?.map((item) => {

            batch_info?.push({
                batch_field_name: item?.field_name,
                batch_field_value: (typeof item?.field_value === 'string' && item?.field_value?.length > 30) ? `${item?.field_value?.slice(0,30)}...` : item?.field_value,
            });
        });
    }
    if(batch_config?.length){
        batch_config?.map(config => {
            let key_name= config?.toLowerCase()?.split(' ')?.join('_');
            if((config.includes('Date') ||config.includes('date')) &&  (batch[key_name]?.includes('T') ||batch[key_name]?.includes(' ') )){
                if(batch[key_name]?.includes('T')){
                    batch[key_name] = batch[key_name]?.split('T')[0];
                }
                if(batch[key_name]?.includes(' ')){
                    batch[key_name] = batch[key_name]?.split(' ')[0];
                }
            }
            // DON'T PUSH BECAUSE THE BATCH HAS NO EXPIRY DATE
            if (!((config?.includes('Expiry Date') || config?.includes('expiry date')) && batch[key_name] === '2099-01-01')) {
                batch_info.push({
                    batch_field_name:config,
                    batch_field_value: batch[key_name]
                })
            }
        })
    }
    return batch_info;
}
