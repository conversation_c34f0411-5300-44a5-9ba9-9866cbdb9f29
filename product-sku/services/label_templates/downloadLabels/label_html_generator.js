const {  getGRNFromDB } = require('../../../../purchase-order/services/grn');
const {  getManufacturingOrder } = require('../../../../production/services/manufacturing_order/getManufacturingOrder.service');
const { render_ejs,render_document } = require("../../../../common/document-engine");
const { db } = require('../../../../config')
const { downloadBarcodeSheet } = require('../../barcoding/downloadBarcodeSheet')
const { getInvoice } = require('../../../../sales-order/services/invoice/getInvoice')
const { getBatchInfoForDownloadLabels } = require('../helper/getBatchDataForDownloadLabels')

exports.convertToKeyValueArray=(configArray)=> {
    const result = {};
    configArray.forEach(item => {
        result[item.key_name] = item.value;
    })
    return result;
}
let page_size  = {
    "A4": 210,
    "4x6": 110
} 
exports.labelHtmlGenerator = async (input,client=db) => {
    // Step 1: Get Template
    if(!input?.label_template_id) throw new Error('label_template_id is required')
    let label_template = await client.select('label_templates',{org_id:input.org_id,entity_name:input.entity_name?.toUpperCase(),label_template_id:input.label_template_id});
    label_template =label_template?.rows?.[0];
    if(!label_template)
        throw new Error().UI('No Template Selected');
    let label_data = [];
    let batch_copies_data = JSON.parse(input.batch_copies_data) || {};
    let page_size_value;
    if(label_template?.entity_name?.toUpperCase() === 'GOOD_RECEIVING_NOTE'){
        let result = await getGRNFromDB({grn_id:input?.document_id});
        let grns_data = result?.grn||[];
        if(grns_data?.length){
            await Promise.all(grns_data?.map(async (grn) => {
                return await Promise.all(grn?.grn_lines?.map(async (line) =>{
                    if(line?.product_batches?.length)
                        return await Promise.all(line?.product_batches?.map(async (batch)=>{
                        let configuration = this.convertToKeyValueArray(label_template?.configuration)
                        page_size_value = page_size[configuration.page_size] || 210;
                        let copies = batch_copies_data[batch?.batch_id] || 0;
                                let label_header;
                                if(((grn?.status === 'ISSUED' && !batch.is_rejected_batch )|| (grn?.status === 'PENDING_FOR_QC' && !batch.is_rejected_batch && batch.batch_id && (batch?.quality_checks?.[0]?.status ==='COMPLETED' ||  (batch?.quality_checks?.length||0) ==0 )))){
                                    label_header =configuration?.approved_label_name;
                                    configuration.background_color=configuration?.approved_background_color;
                                    configuration.colour=configuration?.approved_color;
                                    if(!batch?.ar_number && batch?.quality_checks?.[0]?.approved_batch?.ar_number){
                                        batch.ar_number = batch?.quality_checks?.[0]?.approved_batch?.ar_number;
                                    }
                                }else if((grn?.status === 'ISSUED' && batch.is_rejected_batch || (grn?.status === 'PENDING_FOR_QC' && batch.is_rejected_batch && batch.batch_id && batch?.quality_checks?.[0]?.status ==='COMPLETED' ||  (batch?.quality_checks?.length||0) ==0))){
                                    label_header =configuration?.rejected_label_name;
                                    configuration.background_color=configuration?.rejected_background_color;
                                    configuration.colour=configuration?.rejected_color;
                                    if(!batch?.ar_number && batch?.quality_checks?.[0]?.rejected_batch?.ar_number){
                                        batch.ar_number = batch?.quality_checks?.[0]?.rejected_batch?.ar_number;
                                    }
                                }else if((grn?.status === 'PENDING_FOR_QC' || grn?.status === 'SENT_FOR_APPROVAL')){
                                    label_header =configuration?.pending_label_name;
                                    configuration.background_color=configuration?.pending_background_color;
                                    configuration.colour=configuration?.pending_color;
                                }
                                let barcode_result;
                                if(configuration?.print_barcode){
                                    barcode_result= await downloadBarcodeSheet({
                                        batches: [{batch_id:batch?.batch_id}],
                                        sticker_height: 10,
                                        sticker_width: configuration?.label_width,
                                    })
                                }
                                let barcode_info = barcode_result?.payload?.barcode_list?.[0];
                                let batch_config;
                                if(JSON.parse(input?.batch_config)?.length && copies>0) {
                                    
                                    let line_cf = [];
                                    if (configuration?.print_entity_line_cf) {
                                        line_cf = line?.grn_line_custom_fields;
                                    }
                                    batch_config = getBatchInfoForDownloadLabels(JSON.parse(input?.batch_config), batch, line_cf);
                                }
                                
                                if(batch?.quantity>0 && copies>0){
                                    label_data.push({
                                        label_header,
                                        company_name: grn?.document_tenant_configuration?.print_organisation_name ?  grn?.organisation_info?.organisation_name  : grn?.tenant_info?.tenant_name,
                                        pageConfiguration:{...configuration},
                                        item_name:line?.product_sku_info?.product_sku_name,
                                        supplier_name:grn?.tenant_seller_info?.seller_name,
                                        batch_number:batch?.custom_batch_number,
                                        mfg_date:batch?.manufacturing_date?.split('T')[0],
                                        exp_date:batch?.expiry_date?.split('T')[0],
                                        quantity: batch?.quantity,
                                        grn_number:grn?.grn_number,
                                        grn_date:grn?.grn_date_time?.split('T')[0],
                                        ar_number:batch?.ar_number,
                                        remarks:grn?.remark,
                                        uom:line?.uom_info?.[0]?.uqc,
                                        barcode_data: barcode_info?.barcode_data,
                                        barcode: barcode_info?.barcode,
                                        print_barcode: configuration?.print_barcode,
                                        batch_config: batch_config|| [] ,
                                        copies:copies
                                    })
                                }
                            }))
                }))
            }));
        }
    }else if(label_template?.entity_name?.toUpperCase() === 'MANUFACTURING_ORDER'){
        let data = await getManufacturingOrder({mo_id:input?.document_id});
        let mo_data = data?.result?.manufacturing_orders||[];
        if(mo_data?.length){
            await Promise.all(mo_data?.map(async (mo) => {
                await Promise.all(mo?.mo_finished_goods?.map(async (fg_line) =>{
                    if(fg_line?.finished_good_adjustments?.length)
                        return await Promise.all(fg_line?.finished_good_adjustments?.map(async (batch)=>{
                            let configuration = this.convertToKeyValueArray(label_template?.configuration)
                            page_size_value = page_size[configuration.page_size] || 210;
                            let label_header =configuration?.fg_label_name;
                            let barcode_result;
                            let copies = batch_copies_data[batch?.batch_id] ||0;
                            if(configuration?.print_barcode){
                                barcode_result= await downloadBarcodeSheet({
                                    batches: [{batch_id:batch?.batch_id}],
                                    sticker_height: 10,
                                    sticker_width: configuration?.label_width,
                                })
                            }
                            let barcode_info = barcode_result?.payload?.barcode_list?.[0];
                            if(Math.abs(batch?.quantity)>0){
                                label_data.push({
                                    label_header,
                                    company_name: mo?.document_tenant_configuration?.print_organisation_name ?  mo?.organisation_info?.organisation_name : mo?.tenant_info?.tenant_name,
                                    company_logo:mo?.tenant_configuration.logo_base_64 || mo?.organisation_info.org_logo,
                                    pageConfiguration:{...configuration},
                                    item_name:fg_line?.tenant_product_info?.alias_name,
                                    batch_number:batch?.custom_batch_number,
                                    material_code:fg_line?.tenant_product_info?.ref_product_code,
                                    internal_sku_code:fg_line?.tenant_product_info?.internal_sku_code,                                     
                                    mfg_date:batch?.manufacturing_date?.split('T')[0],
                                    exp_date:batch?.expiry_date?.split('T')[0],
                                    quantity: Math.abs(batch?.quantity),
                                    uom:fg_line?.uom_info?.uqc,
                                    mo_number:mo?.mo_number,
                                    ar_number:batch?.ar_number,
                                    remarks:mo?.remarks,
                                    barcode_data: barcode_info?.barcode_data,
                                    barcode: barcode_info?.barcode,
                                    print_barcode: configuration?.print_barcode,
                                    copies,
                                })
                            }
                        }))
                }))
                await Promise.all(mo?.mo_lines?.map(async (line) =>{
                    if(line?.raw_material_adjustments?.length)
                        return await Promise.all(line?.raw_material_adjustments?.map(async (batch)=>{
                            let configuration = this.convertToKeyValueArray(label_template?.configuration)
                            page_size_value = page_size[configuration.page_size] || 210;
                            let label_header =configuration?.rm_label_name;
                            let barcode_result;
                            let copies = batch_copies_data[batch?.batch_id] ||0;
                            if(configuration?.print_barcode){
                                barcode_result= await downloadBarcodeSheet({
                                    batches: [{batch_id:batch?.batch_id}],
                                    sticker_height: 10,
                                    sticker_width: configuration?.label_width,
                                })
                            }
                            let barcode_info = barcode_result?.payload?.barcode_list?.[0];
                            if(Math.abs(batch?.quantity)>0 && copies>0){
                                label_data.push({
                                    label_header,
                                    company_name: mo?.document_tenant_configuration?.print_organisation_name ?  mo?.organisation_info?.organisation_name : mo?.tenant_info?.tenant_name,
                                    company_logo:mo?.tenant_configuration.logo_base_64 || mo?.organisation_info.org_logo,
                                    pageConfiguration:{...configuration},
                                    item_name:line?.tenant_product_info?.alias_name,
                                    batch_number:batch?.custom_batch_number,
                                    material_code:line?.tenant_product_info?.ref_product_code,
                                    internal_sku_code:line?.tenant_product_info?.internal_sku_code,                                     
                                    mfg_date:batch?.manufacturing_date?.split('T')[0],
                                    exp_date:batch?.expiry_date?.split('T')[0],
                                    quantity: Math.abs(batch?.quantity),
                                    uom:line?.uom_info?.uqc,
                                    mo_number:mo?.mo_number,
                                    ar_number:batch?.ar_number,
                                    remarks:mo?.remarks,
                                    barcode_data: barcode_info?.barcode_data,
                                    barcode: barcode_info?.barcode,
                                    print_barcode: configuration?.print_barcode,
                                    copies,
                                })
                            }
                        }))
                }))
                return mo;
            }));
        }
    }else if(label_template?.entity_name?.toUpperCase() === 'INVOICE'){
        let result = await getInvoice({invoice_id:input?.document_id});
        let invoice_data = result?.invoices||[];
        if(invoice_data?.length){
            await Promise.all(invoice_data?.map(async (invoice) => {
                return await Promise.all(invoice?.invoice_lines?.map(async (line) =>{
                    if(line?.product_batches?.length)
                        return await Promise.all(line?.product_batches?.map(async (batch)=>{
                            let configuration = this.convertToKeyValueArray(label_template?.configuration)
                            page_size_value = page_size[configuration.page_size] || 210;
                            let copies = batch_copies_data[batch?.batch_id] ||0 ;
                            let label_header;
                            if( !batch.is_rejected_batch){
                                label_header =configuration?.approved_label_name;
                                configuration.background_color=configuration?.approved_background_color;
                                configuration.colour=configuration?.approved_color;
                            }else {
                                label_header =configuration?.rejected_label_name;
                                configuration.background_color=configuration?.rejected_background_color;
                                configuration.colour=configuration?.rejected_color;
                            }
                            let barcode_result;
                            if(configuration?.print_barcode){
                                barcode_result= await downloadBarcodeSheet({
                                    batches: [{batch_id:batch?.batch_id}],
                                    sticker_height: 10,
                                    sticker_width: configuration?.label_width,
                                })
                            }
                            let barcode_info = barcode_result?.payload?.barcode_list?.[0];
                            let batch_config;
                            if(input?.batch_config?.length && copies>0)
                                batch_config = getBatchInfoForDownloadLabels(JSON.parse(input?.batch_config), batch, [])

                            if(batch?.quantity>0 && copies){
                                label_data.push({
                                    label_header,
                                    company_name: invoice?.document_tenant_configuration?.print_organisation_name ?  invoice?.organisation_info?.organisation_name  : invoice?.tenant_info?.tenant_name,
                                    pageConfiguration:{...configuration},
                                    item_name:line?.product_sku_info?.product_sku_name,
                                    customer_name:invoice?.customer_info?.customer_name,
                                    batch_number:batch?.custom_batch_number,
                                    mfg_date:batch?.manufacturing_date?.split('T')[0],
                                    exp_date:batch?.expiry_date?.split('T')[0],
                                    quantity: batch?.quantity,
                                    invoice_number:invoice?.invoice_number,
                                    invoice_date:invoice?.invoice_date?.split('T')[0],
                                    ar_number:batch?.ar_number,
                                    remarks:invoice?.remarks,
                                    uom:line?.uom_info?.[0]?.uqc,
                                    barcode_data: barcode_info?.barcode_data,
                                    barcode: barcode_info?.barcode,
                                    print_barcode: configuration?.print_barcode,
                                    copies,
                                    batch_config:batch_config || []
                                })
                            }
                        }))
                }))
            }));
        }
    }
    let html_array=[]
    if(label_data?.length){
        await Promise.all(label_data?.map(async (data)=>{
            if(label_template?.template_ejs){
                let copies = data?.copies || 1;
                await Promise.all(
                    Array.from({ length: copies }).map(async (_, i) => {
                      let html = await render_ejs(label_template?.template_ejs, data);
                      // let html = await render_document("label_template.ejs", data);
                      html_array.push(html);
                    })
                  )
            }
        }))
    }
    let combinedHTML = await render_document('merge_html.ejs', {html_array,page_size_value});
    return{
        html:[combinedHTML],
        fileName: label_template?.template_name||'label'
    }
};

