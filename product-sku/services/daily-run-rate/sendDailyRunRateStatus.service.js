const { common_json_to_sheet } = require("../../../common/common_json_to_sheet");
const { db, email } = require("../../../config");
const { fileUploaderService } = require("../../../file-manager/services/fileUploader");
const { getDailyRunRates } = require("./getDailyRunRates.service");
const mime = require('mime-types');
let ejs = require('ejs');

const convert_to_sheet_url = async (data, file_name) => {
  let runningLowData = data?.filter((item) => item.Status === 'Running Low');
  let outOfStockData = data?.filter((item) => item.Status === 'Out of Stock');
  let _sheetData = [];
  if (runningLowData?.length > 0) {
    _sheetData.push({
      sheet_name: "Running Low",
      sheet_json: runningLowData
    })
  }
  if (outOfStockData?.length > 0) {
    _sheetData.push({
      sheet_name: "Out of Stock",
      sheet_json: outOfStockData
    })
  }

  let _sheet = await common_json_to_sheet(_sheetData);
  let file_buffer = Buffer.from(_sheet.buffer, 'base64');
  let filename = `${file_name}.xlsx`;
  const contentType = mime.contentType(filename);
  let _file_url = await fileUploaderService({
    buffer: file_buffer,
    mimetype: contentType,
    file_name: filename,
    container_name: "temp-mail-files"
  });
  return _file_url?.response?.location;
}

exports.sendDailyRunRateStatus = async (_input, client = null) => {
  client = await db.begin(client);

  try {
    let _dailyRunRateData = await getDailyRunRates(_input);

    let dailyRunRateData = _dailyRunRateData.data.daily_run_rates[0];

    let running_low_stock_record = dailyRunRateData.outward_tenant_skus?.filter((item) => item?.status === "Running Low");
    let out_of_stock_record = dailyRunRateData.outward_tenant_skus?.filter((item) => item?.status === "Out of Stock");

    let resultData = [];
    if (dailyRunRateData?.running_low_notification && running_low_stock_record?.length) {
      running_low_stock_record.forEach((rls) => {
        let result = {};

        result.internal_sku_code = rls?.product_sku_info?.internal_sku_code;
        result.product_sku_name = rls?.product_sku_info?.product_sku_name;
        result.product_category = rls?.product_sku_info?.product_category;
        result.hsn_code = rls?.product_sku_info?.hsn_code;
        result.uom_name = rls?.product_sku_info?.uom_info?.uom_name;
        result.available_qty = rls?.available_qty;
        result.outward_quantity = rls?.outward_quantity;
        result.daily_run_rate = rls?.daily_run_rate;
        result.days_of_stock = rls?.days_of_stock;
        result.minimum_floor_qty = rls?.minimum_floor_qty;
        result.status = rls?.status;

        resultData.push(result);
      })
    }

    if (dailyRunRateData?.out_of_stock_notification && out_of_stock_record?.length) {
      out_of_stock_record.forEach((rls) => {
        let result = {};

        result.internal_sku_code = rls?.product_sku_info?.internal_sku_code;
        result.product_sku_name = rls?.product_sku_info?.product_sku_name;
        result.product_category = rls?.product_sku_info?.product_category;
        result.hsn_code = rls?.product_sku_info?.hsn_code;
        result.uom_name = rls?.product_sku_info?.uom_info?.uom_name;
        result.available_qty = rls?.available_qty;
        result.outward_quantity = rls?.outward_quantity;
        result.daily_run_rate = rls?.daily_run_rate;
        result.days_of_stock = rls?.days_of_stock;
        result.minimum_floor_qty = rls?.minimum_floor_qty;
        result.status = rls?.status;

        resultData.push(result);
      })
    }

    let glossary = {
      "internal_sku_code": 'Internal SKU Code',
      "product_sku_name": 'Product Name',
      "product_category": 'Category',
      "hsn_code": 'HSN Code',
      "uom_name": 'Unit',
      "available_qty": "Available Qty",
      "outward_quantity": "Consumed Qty",
      "daily_run_rate": "Daily Run Rate (Unit/Day)",
      "days_of_stock": "Days of Stock (Day)",
      "minimum_floor_qty": "Minimum Floor Quantity",
      "status": 'Status',
    };

    const work_sheet = [];

    resultData?.map(line => {
      let _obj = {};

      Object.keys(line).forEach(_key => {
        if (glossary[_key]) _obj[glossary[_key]] = line[_key];
      })

      work_sheet.push(_obj);
    })

    let work_sheet_url = await convert_to_sheet_url(work_sheet, "Stock Tracking Report");

    if (dailyRunRateData?.running_low_notification && dailyRunRateData?.out_of_stock_notification) {
      let _report_list = {
        data: {
          title: `Stock Tracking Report of ${dailyRunRateData?.sku_list_info?.list_name}`,
          file_url: work_sheet_url,
          product_list: dailyRunRateData?.sku_list_info?.list_name,
          tenant_name: dailyRunRateData?.tenant_info?.tenant_name,
          running_low_stock_count: running_low_stock_record.length,
          out_of_stock_count: out_of_stock_record.length,
          running_low_notification: dailyRunRateData?.running_low_notification,
          out_of_stock_notification: dailyRunRateData?.out_of_stock_notification,
        }
      }
      let mail_html_body = await ejs.renderFile(process.env.PWD + "/product-sku/views/email_templates/drr_sku_list_status.ejs", _report_list);
      let mailOptions = {
        from: `Procuzy Reporting <<EMAIL>>`,
        subject: `Stock Tracking Report`,
        html: mail_html_body,
        to: dailyRunRateData.notification_recipients,
      };
      await email.SES.sendMail(mailOptions);
    }

    await client.commit();
    return true;
  } catch (error) {
    await client.rollback();
    console.log(error);
  }
}

// let  data  = {"drr_id": 44, "org_id": 10, "tenant_id": 100351, "cron_expression": "00 13 * * *", "notification_recipients": ["<EMAIL>"], "schedule_configurations": {"date": null, "days": [], "time": "13:00", "type": "DAILY", "specific_date": null}}

// this.sendDailyRunRateStatus(data);