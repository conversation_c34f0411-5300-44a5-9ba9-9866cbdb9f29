const { db } = require('../../../config');
const { cronToText } = require("../../../application/reports/systemReports/_helpers");

exports.getDailyRunRates = async (_input, client = db) => {

    let tenant_id = _input.tenant_id?.toString();
    let drr_id = _input.drr_id;
    let org_id = _input.org_id;
    let page = _input.page || 1;
    let limit = _input.limit || 50;

    if (!org_id)
      throw new Error('Org id is required')
    let where_case = []
    if (tenant_id) {
      tenant_id = tenant_id.split(",").filter(i => i)
      if (tenant_id.length === 0) throw new Error("tenant ids are empty");
      where_case.push(`drr.tenant_id IN (${(tenant_id.join(","))})`);
    }
    if (drr_id) {
      where_case.push(`drr.drr_id = ${drr_id}`);
    }

    let data;
    let query = `
                SELECT (
                    SELECT count(*) AS count
                    FROM daily_run_rate drr
                    ${where_case.length ? "WHERE " + where_case.join(" AND ") : ""}
                ),(
                    SELECT json_agg(daily_run_rates) AS daily_run_rates
                    FROM (
                        SELECT *,
                      (
                            SELECT row_to_json(cbi) AS created_by_info
                            FROM
                            (
                                SELECT usr.first_name, usr.last_name,
                                    usr.username, usr.mobile
                                FROM app_user usr
                                WHERE usr.user_id = drr.created_by
                            ) cbi
                        ),
                        (
                            SELECT row_to_json(cbi) AS modified_by_info
                            FROM
                            (
                                SELECT usr.first_name, usr.last_name,
                                    usr.username, usr.mobile
                                FROM app_user usr
                                WHERE usr.user_id = drr.updated_by
                            ) cbi
                        ),
                        (
                          SELECT row_to_json(ski) AS sku_list_info
                          FROM
                          (
                            SELECT sl.list_name, sl.list_description
                          FROM sku_list sl
                          WHERE sl.sku_list_id = drr.sku_list_id
                          ) ski
                        ),
                        (
                          SELECT row_to_json(tenant_info.*) AS tenant_info
                          FROM
                          (
                              SELECT tnt.tenant_name, tnt.tenant_id
                              FROM tenant tnt
                              WHERE tnt.tenant_id = drr.tenant_id
                          )tenant_info
                      ),
                      (
                        SELECT json_agg(sku_list_products) as sku_list_products
                        FROM (
                            SELECT slp.*,
                                (
                                    SELECT row_to_json(product_sku_info) as product_sku_info
                                    FROM (
                                        SELECT tp.tenant_product_id, tp.threshold_qty, ps.*, 
                                        (
                                            SELECT COALESCE(SUM(tpb.available_qty), 0) AS total_available_qty
                                            FROM tenant_product_batches tpb
                                            JOIN tenant_department td ON td.tenant_department_id = tpb.tenant_department_id
                                            WHERE tpb.tenant_product_id = tp.tenant_product_id
                                            AND td.department_id IN (
                                              SELECT jsonb_array_elements_text(drr.departments_to_track::jsonb)::int
                                            )
                                        ) as total_available_qty
                                        FROM product_sku ps
                                        INNER JOIN tenant_product tp ON tp.product_sku_id = ps.product_sku_id
                                        WHERE ps.product_sku_id = slp.product_sku_id
                                        AND tp.tenant_id = drr.tenant_id
                                    ) product_sku_info
                                )
                            FROM sku_list_products slp
                            WHERE slp.sku_list_id = drr.sku_list_id
                        ) sku_list_products
                    ),
                      (
                        SELECT json_agg(outward_tenant_skus) AS outward_tenant_skus
                        FROM (
                            SELECT 
                                SUM(quantity) * -1 AS outward_quantity,  
                                il.tenant_product_id, 
                                (SUM(quantity) * -1) / drr.days_to_track AS daily_run_rate,
                                (
                                  SELECT row_to_json(tpi) AS tenant_product_info
                                  FROM (
                                    SELECT tp.alias_name,
                                    (
                                      SELECT row_to_json(product_info) AS product_sku_info
                                      FROM (
                                          SELECT * FROM product_sku ps
                                          WHERE ps.product_sku_id = tp.product_sku_id
                                      ) product_info
                                    ) 
                                    FROM tenant_product tp
                                    INNER JOIN tenant t on t.tenant_id = tp.tenant_id
                                    WHERE tp.tenant_product_id = il.tenant_product_id
                                  ) tpi
                                ),
                                (
                                    SELECT COALESCE(SUM(tpb.available_qty), 0)
                                    FROM tenant_product_batches tpb 
                                    INNER JOIN tenant_department td2 
                                        ON td2.tenant_department_id = tpb.tenant_department_id
                                    WHERE 
                                        tpb.tenant_product_id = il.tenant_product_id 
                                        AND td2.department_id IN (
                                            SELECT jsonb_array_elements_text(drr.departments_to_track::jsonb)::int
                                        ) 
                                        AND tpb.available_qty > 0
                                ) AS available_qty,
                                (
                                    SELECT COALESCE(SUM(tpb.available_qty), 0)
                                    FROM tenant_product_batches tpb 
                                    INNER JOIN tenant_department td2 
                                        ON td2.tenant_department_id = tpb.tenant_department_id
                                    WHERE 
                                        tpb.tenant_product_id = il.tenant_product_id 
                                        AND td2.department_id IN (
                                            SELECT jsonb_array_elements_text(drr.departments_to_track::jsonb)::int
                                        ) 
                                        AND tpb.available_qty > 0
                                ) / ((SUM(quantity) * -1) / drr.days_to_track) AS days_of_stock
                            FROM inventory_log il
                            INNER JOIN tenant_department td 
                                ON td.tenant_department_id = il.tenant_department_id
                            WHERE 
                                il.tenant_product_id IN (
                                    SELECT tenant_product_id
                                    FROM tenant_product
                                    WHERE 
                                        tenant_id = drr.tenant_id 
                                        AND product_sku_id IN (
                                            SELECT product_sku_id 
                                            FROM sku_list_products 
                                            WHERE sku_list_id = drr.sku_list_id
                                        )
                                ) 
                                AND quantity < 0 
                                AND td.department_id IN (
                                    SELECT jsonb_array_elements_text(drr.departments_to_track::jsonb)::int
                                )
                                AND il.created_at::date >= current_date - drr.days_to_track
                                AND il.entity_name IN (SELECT jsonb_array_elements_text(drr.entities_to_track::jsonb))
                            GROUP BY 
                                il.tenant_product_id
                          ) outward_tenant_skus
                        )
                        from daily_run_rate drr
                        ${where_case.length ? "WHERE " + where_case.join(" AND ") : ""}
                        limit ${limit}
                        offset ${(page - 1) * limit}
                    ) daily_run_rates
                )
            `

    let _daily_run_rates = await client.query(query);

    const _daily_run_rate_data = _daily_run_rates?.rows?.[0]?.daily_run_rates?.map((_daily_run_rate) => {
      const { outward_tenant_skus = [], sku_list_products, target_days_of_stock, calculate_drr_from_mfq
      } = _daily_run_rate;

      let filtered_skus = sku_list_products?.map(sku => {
        const outwardSku = outward_tenant_skus?.find(ots => ots?.tenant_product_id === sku?.product_sku_info?.tenant_product_id);

        let minimum_floor_qty = sku?.product_sku_info?.threshold_qty;
        let status = 'In Stock';
        let uom_info = sku?.product_sku_info?.uom_info;
        let available_qty = sku?.product_sku_info?.total_available_qty;
        let daily_run_rate = 0;
        let days_of_stock = 'Infinite';
        let outward_quantity;

        if (outwardSku) {
          minimum_floor_qty = outwardSku?.daily_run_rate * target_days_of_stock;
          status =
            outwardSku?.available_qty > minimum_floor_qty
              ? 'In Stock'
              : (outwardSku?.available_qty <= minimum_floor_qty && outwardSku?.available_qty > 0)
                ? 'Running Low'
                : 'Out of Stock';
          daily_run_rate = outwardSku?.daily_run_rate;
          days_of_stock = outwardSku?.days_of_stock;
          available_qty = outwardSku?.available_qty;
          outward_quantity = outwardSku?.outward_quantity;
        } else {
          if (calculate_drr_from_mfq) {
            status = sku?.product_sku_info?.total_available_qty > minimum_floor_qty
              ? 'In Stock'
              : (sku?.product_sku_info?.total_available_qty <= minimum_floor_qty && sku?.product_sku_info?.total_available_qty > 0)
                ? 'Running Low'
                : 'Out of Stock';
            daily_run_rate = 0;
            days_of_stock = 'Infinite';
           
          } else {
            status = 'In Stock';
            daily_run_rate = 0;
            days_of_stock = 'Infinite';
          }
        }

        return {
          ...sku,
          minimum_floor_qty,
          status,
          uom_info,
          daily_run_rate,
          days_of_stock,
          available_qty,
          outward_quantity
        }
      })


      return {
        drr_id: _daily_run_rate.drr_id,
        tenant_id: _daily_run_rate.tenant_id,
        tenant_info: _daily_run_rate.tenant_info,
        calculate_drr_from_mfq: _daily_run_rate.calculate_drr_from_mfq,
        departments_to_track: _daily_run_rate.departments_to_track,
        entities_to_track: _daily_run_rate.entities_to_track,
        days_to_track: _daily_run_rate.days_to_track,
        include_expired_stock: _daily_run_rate.include_expired_stock,
        include_rejected_stock: _daily_run_rate.include_rejected_stock,
        enable_notification: _daily_run_rate.enable_notification,
        out_of_stock_notification: _daily_run_rate.out_of_stock_notification,
        running_low_notification: _daily_run_rate.running_low_notification,
        notification_recipients: _daily_run_rate.notification_recipients,
        is_pinned: _daily_run_rate.is_pinned,
        schedule_configurations: _daily_run_rate.schedule_configurations,
        sku_list_info: { sku_list_id: _daily_run_rate.sku_list_id, ..._daily_run_rate.sku_list_info },
        target_days_of_stock: _daily_run_rate.target_days_of_stock,
        outward_tenant_skus: filtered_skus,
      };
    });


    data = { count: parseInt(_daily_run_rates.rows[0].count), daily_run_rates: _daily_run_rate_data || [] }

    return { data };
}