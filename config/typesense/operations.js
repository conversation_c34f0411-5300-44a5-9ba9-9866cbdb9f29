const { typesense } = require("./index");

exports.create_collection = async (org_id) => {
  let  schema = {
    "name": `${process.env.STAGE}_org_${org_id}`,
    "enable_nested_fields": true,
    "fields": [
      { "name": "id", "type": "string", "facet": false }, // this should be combination of entity_name and entity_id (id Example - PURCHASE_ORDER_123456)
      { "name": "entity_id", "type": "string", "facet": false },
      { "name": "entity_name", "type": "string", "facet": true, "index": true },
      { "name": "tenant_id", "type": "string", "facet": true, "index": true },
      { "name": "created_at", "type": "int64", "facet": false, "sort": true,"index": true },
      { "name": "title", "type": "string", "facet": false, "index": true },
      { "name": "left_side_details", "type": "object[]", "facet": false, "index": false ,optional: true },
      { "name": "right_side_details", "type": "object[]", "facet": false, "index": false ,optional: true},
      { "name": "icon", "type": "string", "facet": false, "index": false, optional: true},
      { "name": "keywords", "type": "string[]", "facet": false, "index": true}
    ],
    "default_sorting_field": "created_at",
  }
  try {
    let created_collection = await typesense.collections().create(schema);
    if (created_collection) {
      try {
        created_collection = JSON.parse(created_collection);
      } catch (error) {
        // If parsing fails, return the raw value
      }
    }
    return created_collection;
  } catch (error) {
    console.error('Create Collection error:', error);
    return null;
  }
};


exports.get_collection = async (collection_name) => {

    try {
        let fetched_collection =     await typesense.collections(collection_name).retrieve();
        if (fetched_collection) {
          try {
            fetched_collection = JSON.parse(fetched_collection);
          } catch (error) {
            // If parsing fails, return the raw value
          }
        }
        return fetched_collection;
      } catch (error) {
        console.error('Get Collection error:', error);
        return null;
      }
};


exports.get_all_collections = async () => {

    try {
        let fetched_collections =  await typesense.collections().retrieve();
        if (fetched_collections) {
          try {
            fetched_collections = JSON.parse(fetched_collections);
          } catch (error) {
            // If parsing fails, return the raw value
          }
        }
        return fetched_collections;
      } catch (error) {
        console.error('Get Collections error:', error);
        return null;
      }
};
  

exports.delete_collection = async (collection_name) => {

    try {
        let deleted_collection =     await typesense.collections(collection_name).delete();
        if (deleted_collection) {
          try {
            deleted_collection = JSON.parse(deleted_collection);
          } catch (error) {
            // If parsing fails, return the raw value
          }
        }
        return deleted_collection;
      } catch (error) {
        console.error('Delete Collections error:', error);
        return null;
      }
};


exports.index_documents = async (collection_name, documents) => {

    try {
        let inserted_docs =    await typesense.collections(collection_name).documents().import(documents, {action: 'create'});
        if (inserted_docs) {
          try {
            inserted_docs = JSON.parse(inserted_docs);
          } catch (error) {
            // If parsing fails, return the raw value
          }
        }
         
        return inserted_docs;
      } catch (error) {
        console.error('Insert Documents error:', error);
        return null;
      }
};

exports.get_document = async (collection_name, document_id) => {

  try {
      let retrieved_doc =     await typesense.collections(collection_name).documents(document_id).retrieve();
      if (retrieved_doc ) {
        try {
          retrieved_doc  = JSON.parse(retrieved_doc );
        } catch (error) {
          // If parsing fails, return the raw value
        }
      }
      return retrieved_doc ;
    } catch (error) {
      console.error('get Documents error:', error);
      return null;
    }
};


// Demo for getting documents

exports.get_all_documents = async (collection_name) => {
  let entity_document_search = {
    q: 'MANUFACTURING_ORDER',
    query_by: 'entity_name',
    per_page: 250,
    page: 1,
  };
  //Refer last getAllManufacturingOrderSample Function below to know hoe to retrieve all data 
  try {
      let retrieved_docs = await typesense.collections(collection_name).documents().search(entity_document_search);
      if (retrieved_docs ) {
        try {
          retrieved_docs  = JSON.parse(retrieved_docs );
        } catch (error) {
          // If parsing fails, return the raw value
        }
      }
      return retrieved_docs ;
    } catch (error) {
      console.error('get Documents error:', error);
      return null;
    }
};

exports.delete_document = async (collection_name, document_id) => {

  try {
      let deleted_doc =     await typesense.collections(collection_name).documents(document_id).delete();
      if (deleted_doc ) {
        try {
          deleted_doc  = JSON.parse(deleted_doc);
        } catch (error) {
          // If parsing fails, return the raw value
        }
      }
      return deleted_doc ;
    } catch (error) {
      // console.error('get Documents error:', error);
      return null;
    }
};

exports.delete_entity_documents = async (collection_name, entity_name) => {

  try {
    let filterCondition = ''

    if (Array.isArray(entity_name)) {
     filterCondition = `entity_name: [${entity_name.join(',')}]`; 
    }else{
      filterCondition = `entity_name:${entity_name}`;
    }
    let start_time = new Date();
    const deleted_docs = await typesense
      .collections(collection_name)
      .documents()
      .delete({ filter_by: filterCondition });
      let end_time = new Date();
      console.log(`${(end_time - start_time)/ 1000} Seconds to Delete`)

      //BIghaat 8.69 lakh  SO took less than 2 min to delete.

    let parsed_deleted_docs;
    try {
      parsed_deleted_docs = JSON.parse(deleted_docs);
    } catch (error) {
      console.error("Error parsing deleted documents:", error);
      parsed_deleted_docs = deleted_docs; // Return raw value if parsing fails
    }

    return parsed_deleted_docs;
  } catch (error) {
    console.error("Error deleting documents by entity_name:", error);
    if(error.name==='ObjectNotFound'){
      return true;
    }
    return null;
  }
};

exports.search_documents = async (collection_name, search_params) => {
  try {
    let search_results = await typesense.collections(collection_name).documents().search(search_params);
    
    if (search_results) {
      try {
        search_results = JSON.parse(search_results);
      } catch (error) {
        // If parsing fails, return the raw value
      }
    }
    
    return search_results;
  } catch (error) {
    console.error('Search Documents error:', error);
    return null;
  }
};


// let search_params = {
//   q: "772",
//   query_by: "keywords",
// }

//this.search_documents('org_100027',search_params)

//this.get_all_documents('org_1001');



//  this.delete_entity_documents('org_100027',
//    ["SALES_ORDER","PURCHASE_ORDERS"]);



// this.create_collection({
//     "name": "purchase_order",
//     "fields": [
//         {
//             "name": ".*",
//             "type": "auto",
//         }
//     ]
// });

//this.delete_collection('org_100027').then(r=>console.log(r));

// let  schema = {
//   "name": `${process.env.STAGE}_org_${org_id}`,
//   "enable_nested_fields": true,
//   "fields": [
//     { "name": "id", "type": "string", "facet": false }, // this should be combination of entity_name and entity_id (id Example - PURCHASE_ORDER_123456)
//     { "name": "entity_id", "type": "string", "facet": false },
//     { "name": "entity_name", "type": "string", "facet": true, "index": true },
//     { "name": "tenant_id", "type": "string", "facet": true, "index": true },
//     { "name": "created_at", "type": "int64", "facet": false, "sort": true,"index": true },
//     { "name": "title", "type": "string", "facet": false, "index": true },
//     { "name": "left_side_details", "type": "object[]", "facet": false, "index": false ,optional: true },
//     { "name": "right_side_details", "type": "object[]", "facet": false, "index": false ,optional: true},
//     { "name": "icon", "type": "string", "facet": false, "index": false, optional: true},
//     { "name": "keywords", "type": "string[]", "facet": false, "index": true , "ngram": true}
//   ],
//   "default_sorting_field": "created_at",
//   "token_separators": ["@", "."]
// }

//"token_separators": ["(", ")", "-","@", " ", "/"]





//this.delete_collection('org_1001')
//Pass org_id
 //this.create_collection(100027);
 //this.get_all_collections().then(r=>console.log(r));

//  let document = [
//   {
//       id: "PURCHASE_ORDER_104108",
//       entity_id: '104108',
//       reference_entity_id: "",
//       entity_name: "PURCHASE_ORDER",
//       tenant_id: '100227',
//       title: "BJP/PO/2223/1212",
//       created_at: 1735034470000,
//       left_side_details: [
//         {
//           label_name: "PO #",
//           label_value: "BJP/PO/2223/1212",
//         },
//         {
//           label_name: "PO Date",
//           label_value: "2024-12-24T00:00:00.000Z",
//         },
//         {
//           label_name: "Internal Seller Code",
//           label_value: "SLR606",
//         },
//         {
//           label_name: "Seller Name",
//           label_value: "New",
//         },
//         {
//           label_name: "PR Number",
//           label_value: "",
//         },
//       ],
//       right_side_details: [
//         {
//           label_name: "PO Total",
//           label_value: "111100",
//         },
//         {
//           label_name: "Status",
//           label_value: "ISSUED",
//         },
//         {
//           label_name: "PO Receiving Status",
//           label_value: "NOT_RECEIVED",
//         },
//         {
//           label_name: "Payment Status",
//           label_value: "NOT_PAID",
//         },
//         {
//           label_name: "Closing Status",
//           label_value: "OPEN",
//         },
//       ],
//       keywords: [
//         "BJP/PO/2223/1212",
//         "SLR606",
//         "New",
//         "45864709751066",
//         "SKU27382",
//         "Draft Product - 123",
//       ],
//     }
//  ];

//Simple entity_wise fetch
// async function getAllManufacturingOrderDocuments() {
//   try {
//     let allDocuments = [];
//     let page = 1;
//     let perPage = 250;
//     let hasMoreResults = true;

//     while (hasMoreResults) {
//       // Perform the search with pagination
//       let retrieved_docs = await typesense
//         .collections('org_100027')
//         .documents()
//         .search({
//           q: 'SALES_ORDER',  // Searching for documents where entity_name matches "MANUFACTURING_ORDER"
//           query_by: 'entity_name',  // Searching within the "entity_name" field
//           per_page: perPage,  // Set the number of results per page
//           page: page,  // Set the page number
//         });
      
//       // Add the documents from the current page to the array
//       allDocuments = allDocuments.concat(retrieved_docs.hits);
      
//       // Check if there are more results
//       if (retrieved_docs.hits.length < perPage) {
//         hasMoreResults = false;  // No more results
//       } else {
//         page++;  // Move to the next page
//       }
//     }
    
//     console.log(allDocuments);  // Log all retrieved documents
//     return allDocuments;
//   } catch (error) {
//     console.error("Error retrieving documents:", error);
//     return [];
//   }
// }

// Usage example
//getAllManufacturingOrderDocuments();

